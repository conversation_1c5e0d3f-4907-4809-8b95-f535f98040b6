﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{465E4163-40B8-467B-962E-3EC3A541FAF4}</ProjectGuid>
    <RootNamespace>egrabber_cuda</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 8.0.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;WIN64;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);$(CudaToolkitIncludeDir);C:\ProgramData\NVIDIA Corporation\CUDA Samples\v8.0\common\inc;C:\Program Files\Euresys\eGrabber\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>cuda.lib;cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;glew64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories);$(CudaToolkitLibDir);C:\ProgramData\NVIDIA Corporation\CUDA Samples\v8.0\common\lib\x64</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(CudaToolkitBinDir)\cudart*.dll" "$(OutDir)"
copy "$(NVCUDASAMPLES_ROOT)\bin\win64\$(Configuration)\glew*.dll" "$(OutDir)"
copy "$(NVCUDASAMPLES_ROOT)\bin\win64\$(Configuration)\freeglut*.dll" "$(OutDir)"
copy "$(MSBuildProjectDirectory)\src\config.js" "$(OutDir)"</Command>
    </PostBuildEvent>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <CodeGeneration>compute_20,sm_20;compute_30,sm_30;compute_35,sm_35;compute_37,sm_37;compute_50,sm_50;compute_52,sm_52;</CodeGeneration>
    </CudaCompile>
    <CudaLink>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;WIN64;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);$(CudaToolkitIncludeDir);C:\ProgramData\NVIDIA Corporation\CUDA Samples\v8.0\common\inc;C:\Program Files\Euresys\eGrabber\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>cuda.lib;cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;glew64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories);$(CudaToolkitLibDir);C:\ProgramData\NVIDIA Corporation\CUDA Samples\v8.0\common\lib\x64</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>copy "$(CudaToolkitBinDir)\cudart*.dll" "$(OutDir)"
copy "$(NVCUDASAMPLES_ROOT)\bin\win64\$(Configuration)\glew*.dll" "$(OutDir)"
copy "$(NVCUDASAMPLES_ROOT)\bin\win64\$(Configuration)\freeglut*.dll" "$(OutDir)"
copy "$(MSBuildProjectDirectory)\src\config.js" "$(OutDir)"</Command>
    </PostBuildEvent>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <CodeGeneration>compute_20,sm_20;compute_30,sm_30;compute_35,sm_35;compute_37,sm_37;compute_50,sm_50;compute_52,sm_52;</CodeGeneration>
    </CudaCompile>
    <CudaLink>
      <AdditionalLibraryDirectories>
      </AdditionalLibraryDirectories>
      <AdditionalDependencies>
      </AdditionalDependencies>
    </CudaLink>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CudaCompile Include=".\src\kernel.cu" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include=".\src\cuda.cpp" />
    <ClCompile Include=".\src\main.cpp" />
    <ClCompile Include=".\src\opengl.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include=".\src\check.h" />
    <ClInclude Include=".\src\cuda.h" />
    <ClInclude Include=".\src\egrabber-cuda.h" />
    <ClInclude Include=".\src\locks.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 8.0.targets" />
  </ImportGroup>
</Project>
