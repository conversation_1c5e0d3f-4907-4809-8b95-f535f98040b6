<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang xml:lang>
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>eGrabber sample programs</title>
  <style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
width: 0.8em;
margin: 0 0.8em 0.2em -1.6em;
vertical-align: middle;
}
.display.math{display: block; text-align: center; margin: 0.5rem auto;}
</style>
  <style type="text/css">body {width: 940px;}body, table, div, p, dl {background: #fff;background-color: #fff;margin-left: auto;margin-right: auto;text-align: justify;color: #797f84;font-family: "Open Sans", Verdana, sans-serif;font-weight: 400;line-height: 1.6em;font-size: 14px;}ol {padding-left: 25px;}table, th, td {border: 1px solid #797f84;border-collapse: collapse;padding: 1px 5px;}table {width: 100%;}th {border: 1px solid #797f84;color: #474c4f;font-weight: 600;border-collapse: collapse;padding: 1px 5px;}td {width: auto;}tr:hover td {background-color: #f6f6f6;}li:only-child{list-style-type:none;}.toc-section-number:after {content: ". ";}.header-section-number:after{content: ".";}pre {background-color: #f8f8f8;padding: 10px;margin: 0px;}pre:hover {background-color: #f2f2f2;}code {color: #4080c0;font-family: Consolas, monospace, serif;}pre code {color: #474c4f;font-weight: normal;line-height: 1.3em;}h1 {color: #474c4f;margin-bottom: 0.7em;font-family: "Source Sans Pro",sans-serif;font-size: 65px;font-weight: 100;text-align: left;}h1 code{color: #474c4f;font-weight: 300;font-family: "Source Sans Pro",sans-serif;} h2 code {color: #009dd8;}h3 code, th code, dt code {color: #474c4f;font-size: 1.1em;}#net-assembly em code {color: #474c4f;}h2 {color: #009dd8;margin-top: 35px;margin-bottom: 10px;font-family: Montserrat;font-weight: 400;text-transform: uppercase;}h3 {color:#474c4f;margin-top: 10px;margin-bottom: 15px;font-family: Montserrat;font-weight: 400;text-transform: uppercase;}pre.genicam-gentl code:before {float: right;color: #009dd8;font-style: italic;content: 'GenICam GenTL';}pre.egentl code:before {float: right;color: #009dd8;font-style: italic;content: 'EGenTL';}pre.multicam code:before {float: right;color: #009dd8;font-style: italic;content: 'MultiCam';}pre.egrabber code:before {float: right;color: #009dd8;font-style: italic;content: 'eGrabber';}li p {margin-bottom: 0.5em;margin-top: 0.5em;}dt {color: #474c4f;font-weight: 600;margin-top: 0.3em;}dd {margin-bottom: 0.8em;margin-left: 20px;}dd p {margin-top: 0.1em;margin-bottom: 0.1em;}dd pre, li pre {margin-top: 0.2em;margin-bottom: 0.2em;}dd table {margin-top: 0.5em;margin-bottom: 0.5em;}#acronyms dd {display: inline;margin: 0;}#acronyms dd:after {display: block;content: '';}#acronyms dt {display: inline-block;min-width: 5em;margin-top: 0em;}div.footnotes {font-size: 0.8em;}div.footnotes p {margin-top: 0;margin-bottom: 0;}div.footnotes code{color:#009dd8;}a {color: #009dd8;font-weight: normal;text-decoration: none;}a:hover {text-decoration: underline;}#TOC li {list-style-type: none;}li>ul>li{list-style-type: square;font-weight: normal;}li>ul>li>ul>li{list-style-type: circle;font-weight: normal;}code > span.kw { color: #67b145; font-weight: bold; } code > span.dt { color: #d98840; } code > span.ot { color: #2f6809; } code > span.va { color: #25254d; } code > span.cf { color: #2f6809; font-weight: bold; } @media print {@page {margin: 1.5cm;}body {font-size: 12px;}a {box-shadow: none;}a[href^="#fnref"] {display: none;}h1 {page-break-before: always;}#TOC h1 {page-break-before: auto;}dd {page-break-before: avoid;}h2, h3, dt {page-break-after: avoid;}pre, table {page-break-inside: avoid;}div[id$=".js"] pre {page-break-inside: auto;}}</style>
  <style type="text/css">@font-face {
font-family: 'Source Sans Pro';
font-style: normal;
font-weight: 300;
src: url(data:font/ttf;base64,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************************************************************************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) format('truetype');
}
@font-face {
font-family: 'Source Sans Pro';
font-style: normal;
font-weight: 400;
src: url(data:font/ttf;base64,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************************************************************************Asj+HgEAw/7d6mqAAAABAFL/9ADYAsgADwAAFyImNREzERQWMzI2NxcGBqkuKVIOCQQHBwsIFgw5NQJm/ZQTEQEBPgQEAAEAUgAAAvEB8gAhAAAzETMXMzY2MzIWFzY2MzIWFREjETQmIyIHESMRNCYjIgcRUkQHAyBMKzg/DyZOLEtJUiwuNkRSLC82RAHmRiMvMSwpNGFd/swBKUQ+S/6gASlEPkv+oAABAFIAAAHXAfIAFAAAMxEzFzM2NjMyFhURIxE0JiMiBgcRUkQHAyNOMk1HUiwwJTslAeZGIy9hXf7MASlEPiYl/qAAAAIALv/0AfAB8gAPABsAAAUiJiY1NDY2MzIWFhUUBgYnMjY1NCYjIgYVFBYBDztnPz9nOzxmPz9mPD9NTT8+Tk4MPHJQUnI8PHJSUHI8RGdTVGhoVFNnAAACAFL/MwH7AfIAEwAgAAAXETMXMzY2MzIWFRQGBiMiJicXFRMyNjU0JiMiBgcVFhZSRAcDIU8rXmI7XzgiQyICdzxPO0UfPyQhPs0CszgcKIdxUnY+HhpVpAEGZ1pQYyIg/xwXAAACAC//MwHZAfIAEwAgAAAFNTcGBiMiJjU0NjYzMhYXMzczEQMyNzUmJiMiBgYVFBYBhgQdSypbbjthNypAIQIIQs9APB85Hic/JkbNrVgcKIV5T3M+HR0u/U0BBkP+HBcvUzhXYwABAFIAAAFeAfIAEQAAMxEzFzM2NjMyFwcmJiMiBgcRUkQHAxlIKR0XEAwUDx9DGQHmWC42CkgEBDI+/sgAAAEAHP/0AYMB8gAqAAAXIiYnNxYWMzI2NTQmJicuAjU0NjMyFhcHJiYjIgYVFBYWFx4CFRQGBtE0XiMpIEQrMDAhMxsiQipZTy1OHCgZNiAuKx4wGyNELSlPDCccNxkhLR8ZIhcKDSE1KTtPIBc0ExgqHBcdFQsNITcuJ0IoAAEAGP/0AUUCbgAXAAAXIiY1ESM1NzczFTMVIxEUFjMyNjcXBgbrTj1ITApFg4MhKg0eDBAULwxaSAENPgWIiEP+8i0xCQQ+BwsAAQBL//QBzgHmABQAABciJjURMxEUFjMyNjcRMxEjJyMGBuBNSFMrMCY7IlJEBwMiTAxhXQE0/tdEPicrAVn+GkwoMAABAAwAAAHHAeYADQAAMwMzExYWFzM2NjcTMwO7r1VcCxcLBAsXClxRrAHm/uwkSCMjSCQBFP4aAAEAGAAAArYB5gAhAAAzAzMTFhYXMzY2NxMzExYWFzM2NjcTMwMjAyYmJyMGBgcDn4dUSAgOBwQIEAlLUEwJEQgECA8HR06CZEYJDwkECBAKRAHm/uciQyIiQyIBGf7nIkMiIkMiARn+GgEFI0QlJUUj/vwAAQAOAAABsAHmABkAADM3JzMXFhYXMzY2NzczBxcjJyYmJyMGBgcHDp+TWUELGA0ECxYLO1aTnllHDRoOBA0YDEL+6GsTKhQUKhNr8fVxFiwVFSsXcQABAAz/LwHHAeYAGwAAFyImJzcWFjMyNjc3AzMTFhYXMzY2NxMzAw4CWhAdDBAIFAkpNg8Lw1VjCxkLBAsUCldQtxEvRdEGBEECBTstJAHn/vMfSCIhSCABDf3yMEwtAAABAB8AAAGPAeYACQAAMzUBIzUhFQEhFR8BAOQBTP8AAQgsAXdDLP6JQ///AAMAAAIdA2MCJgACAAAABwEMAQ8AAP//AAMAAAIdA2MCJgACAAAABwEPAQ8AAP//AAMAAAIdA0YCJgACAAAABwESAQ8AAP//AAMAAAIdA0kCJgACAAAABwEUAQ8AAP//AAMAAAIdAy0CJgACAAAABwEZAQ8AAP//AAMAAAIdA3ACJgACAAAABwEdAQ8AAAACAAgAAAMFApAABQAVAAABBzMRIwYBASEVIRUzFSMVIRUhNSMHASI9rAQ1/rABWAGb/uro6AEg/ozOYwF4dgFMa/4dApBGzkfuR7+/AP//ADT/HgIbApwCJgAEAAAABwEhAVcAAP//AFoAAAHeA2MCJgAGAAAABwEMASIAAP//AFoAAAHeA2MCJgAGAAAABwEPASIAAP//AFoAAAHeA0YCJgAGAAAABwESASIAAP//AFoAAAHeAy0CJgAGAAAABwEZASIAAP//ADT/9AImA0kCJgAIAAAABwEUAV8AAP////0AAAC6A2MCJgAKAAAABwEMAIQAAP//AE4AAAELA2MCJgAKAAAABwEPAIQAAP////AAAAEYA0YCJgAKAAAABwESAIQAAP///+wAAAEcAy0CJgAKAAAABwEZAIQAAP//AFoAAAItA0kCJgAPAAAABwEUAU4AAP//ADT/9AJlA2MCJgAQAAAABwEMAUwAAP//ADT/9AJlA2MCJgAQAAAABwEPAUwAAP//ADT/9AJlA0YCJgAQAAAABwESAUwAAP//ADT/9AJlA0kCJgAQAAAABwEUAUwAAP//ADT/9AJlAy0CJgAQAAAABwEZAUwAAAADADL/4gJrAq4AFwAgACoAABcnNyYmNTQ2NjMyFzcXBxYWFRQGBiMiJwMUFhcBJiMiBhMyNjY1NCYnARZgLkYfIkd/UmhIPi5FICJIflNoRxMREAEgM0xYasI7VzAREP7hMh4kWyx2SGmXUT9RI1krdUdpmlRBARYwUyABdTaO/nhDeVIwUR/+ijgAAAIANAAAAx4CkAAQABkAACEiJjU0NjMhFSEVMxUjFSEVJTMRIyIGFRQWAXGVqKmYAZ/+6ujoASD+XTAwdnt7rp2cqUbOR+5HRAIIhH19igD//wBX//QCLgNjAiYAFgAAAAcBDAFCAAD//wBX//QCLgNjAiYAFgAAAAcBDwFCAAD//wBX//QCLgNGAiYAFgAAAAcBEgFCAAD//wBX//QCLgMtAiYAFgAAAAcBGQFCAAD/////AAAB3QNjAiYAGgAAAAcBDwDuAAAAAgAhAAACSgKQAAwAGQAAMxEjNTcRMzIWFRQGIyczMjY1NCYjIxUzFSNwT0+kmJ6dlVVLc3Nzc0uVlQFBKwQBIKmcna5Ei3x8hdwvAAIAWgAAAhUCkAANABYAADMRMxUzMhYWFRQGIyMVNTMyNjU0JiMjWlN2SWw9hmx2bFZTVFVsApBuJVRIaGOW2kFGRzb//wA0//QBsQMNAiYAHAAAAAcBCwELAAD//wA0//QBsQMNAiYAHAAAAAcBDgELAAD//wA0//QBsQLkAiYAHAAAAAcBEQELAAD//wA0//QBuALRAiYAHAAAAAcBEwELAAD//wA0//QBsQKvAiYAHAAAAAcBGAELAAD//wA0//QBsQLvAiYAHAAAAAcBHAELAAAAAwA6//QC6wHyACsAOAA/AAAXIiY1NDY3NCYmIyIGByc2NjMyFhc2NjMyFhUUByEWFjMyNjcXBgYjIiYnBicyNjcmJic1BgYVFBY3MzQmIyIGyT1Sj5cQKygoSB0hImA2NkYPHVIxWWAD/sUDVj4jOBseH00yPVIcZkkiUCEICgFzYjHx9js4MkoMSUFQVREfOCMgFDkWKTgvLzh9aBwSTFwXETkUHjckW0InJBM1HBkPPC8pJeBLUFT//wAu/x4BrwHyAiYAHgAAAAcBIAEKAAD//wAu//QBygMNAiYAIAAAAAcBCwEIAAD//wAu//QBygMNAiYAIAAAAAcBDgEIAAD//wAu//QBygLkAiYAIAAAAAcBEQEIAAD//wAu//QBygKvAiYAIAAAAAcBGAEIAAAABAAt/yAB7ALoADEAPQBNAFsAABciJjU0Njc1JiY1NDY3NSYmNTQ2NjMyFhczFSMWFhUUBgYjIicGBhUUFjMzMhYVFAYGAzI2NTQmIyIGFRQWEzI2NTQmIyMiJicGBhUUFhMmJjU0NjcXBgYVFBYX9llwJiESGSITGCcyVDEUJA2pZBEXMFAyJyINEiUxXlVVPG5MKj08Kys8PTZGVDMvVA4hEBoYS2lBNFZPCD8uJSLgRD8fORYECygcHy4NBBREKzVOKgcFPxA1HzRMKRELGxQXHjc9LUwvAbE/NTU8PDU1P/6IPSYiGgQEEyoVJy4C5ggjIiwsBCkEFxUTFAMA//8ALf8gAewC0QImACIAAAAHARMBAgAA////7QAAALQDDQImAGgAAAAGAQt8AP//AEQAAAELAw0CJgBoAAAABgEOfAD////hAAABFwLkAiYAaAAAAAYBEXwA////6gAAAQ4CrwImAGgAAAAGARh8AAABAFIAAACkAeYAAwAAMxEzEVJSAeb+Gv//AFIAAAHXAtECJgApAAAABwETAScAAP//AC7/9AHwAw0CJgAqAAAABwELAQ8AAP//AC7/9AHwAw0CJgAqAAAABwEOAQ8AAP//AC7/9AHwAuQCJgAqAAAABwERAQ8AAP//AC7/9AHwAtECJgAqAAAABwETAQ8AAP//AC7/9AHwAq8CJgAqAAAABwEYAQ8AAAADAC7/6QHwAf0ACAAgACgAADcUFxMmIyIGBgMnNyYmNTQ2NjMyFzcXBxYWFRQGBiMiJzcyNjU0JwMWfxfWJTgpQiUsJTYZHT9nO1A7MiQ2GR0/ZjxPPIs/URjVJ/ZDLgEDKC9U/rwdQSBWNVJyPDI9HUEgVzZQcjwxEWdSRC7+/CcAAwAu//QDIQHyACMALwA2AAAFIiYmNTQ2NjMyFhc2NjMyFhUUByEeAjMyNjcXBgYjIiYnBicyNjU0JiMiBhUUFiUzNCYjIgYBCDpkPD1kOzheGhxaNVpiA/7BAipEKSM6Gx4fTzI5Xhw5eDxMTDw8TEwBEfo+ODJLDDxyUFJyPD87OUF9aBwSMkwqFxE5FB5BOHlEZ1NUaGhUU2feS1BUAAEAUv/0AiMC0gAxAAAFIiYnNxYzMjY1NC4DNTQ+AjU0JiMiBhURIxE0NjMyFhYVFA4CFRQeAxUUBgGDKUUfITQ2KSsoOjsoGyIbKik1PFJmXjVIJhslGyg7OihWDBsWOisxHyMqHiEyKiIxKy8fJjFOTf4MAgNecShCKCU2KyoaHSMdJToxP1YA//8AS//0Ac4DDQImADAAAAAHAQsBDwAA//8AS//0Ac4DDQImADAAAAAHAQ4BDwAA//8AS//0Ac4C5AImADAAAAAHAREBDwAA//8AS//0Ac4CrwImADAAAAAHARgBDwAA//8ADP8vAccDDQImADQAAAAHAQ4A8wAA//8ADP8vAccCrwImADQAAAAHARgA8wAAAAIANf/0AeUC2gAPADAAADcUFhYzMjY1NCYnJiYjIgYTIiYmNTQ2NjMyFhcmJicHJzcmJzcWFhc3FwcWFhUUBgaDJkAmREMBASFCIkNJijhjPTddOiZHGQ04Jo0YfzQ8JiRGII4YgTxMNmHeMkspblgOHA0sHlz+0TlpSERlOCAiPl0lSSlBKCA0Ey0bSSlCPah3UHlEAAIAUv8zAfsCyAATACAAABcRMxUHNjYzMhYVFAYGIyImJxcVEzI2NTQmIyIGBxUWFlJSASBMKGBkO184I0IhAXc8TztFHz8kIT7NA5XBVBolh3FSdj4dGVOkAQZnWlBjIiD/HBcAAAH/2P8nAKUB5gAPAAAXIiYnNxYWMzI2NREzERQGIBYlDREJGA0kGFI82QgFPgMFMywCHf3jSlgAAQAeAAACXALUACcAACERIxEjESM1NzU0NjMyFwcmIyIGFRUzNTQ2MzIWFwcmIyIVFTMVIxEBfctSQkJLTDInER4jJCjLRUkXKhASGxxEZ2cBo/5dAaM+BUBMWBI+DTMwPk1KVwkHPwxeTUP+XQABAB7/9AJFAtQAKQAAMxEjNTc1NDYzMhYXByYjIhUVMzczFTMVIxEUFjMyNjcXBgYjIiY1ESMRYEJCRUkXKhASGxxEsgpEhIQiKg0eDBAULxdOPa4Boz4FTUpXCQc/DF5NiIhD/vItMQkEPgcLWkgBDf5dAAEAHv/0A2IC1AA7AAAhESMRIxEjNTc1NDYzMhcHJiMiBhUVMzU0NjMyFhcHJiMiFRUzNzMVMxUjERQWMzI2NxcGBiMiJjURIxEBfctSQkJLTDInER4jJCjLRUkXKhASGxxEsgpEhIQiKg0eDBAULxdOPa4Bo/5dAaM+BUBMWBI+DTMwPk1KVwkHPwxeTYiIQ/7yLTEJBD4HC1pIAQ3+XQABAFL/TQIXAeYAJQAAFxEzERQWMzI2NxEzBgYVFBYzMjcXBgYjIiYnIwYGIyImJx4CF1JSKi8hPiJTAQUUEA0QCwocFCkqBQIaRSgdMBEBAQMCswKZ/tdAQiM8AUxizVgUEwY+BQctMS4uExwwRD0nAAADACD/9AJSApwACwA1AEEAABMUFhc2NjU0JiMiBhMiJiY1NDY2NyYmNTQ2NjMyFhUUBgYHFhYXNjY3MwYGBxYWFwcmJicGBicUFjMyNjcmJicGBr0RDi1DHSElLCs7WzIlOiIUFyZFLT1ELEMlIFguHi8PTRM5JyI+GxYiTSglXrJLNiI/HDBZIyIwAgIbOh4eQSsdKzb9yS9SNSxENhcpTSQsRilIOitEOBsyXycpYThAdzQXIAhECiUcIim7N0IcGSpkNRw9AAIALP/0AcUCigALABcAABciJjU0NjMyFhUUBicyNjU0JiMiBhUUFvlgbW1gX21tXzhERDg4RUUMraChqKihoK1Cf4yMe3uMjH8AAQBPAAABtwJ+AAwAADM1MxEjNTY2NzMRMxVPknQsQRo/hEQB1jUIFxD9xkQAAAEAJAAAAcQCigAbAAAzNT4CNTQmIyIGByc2NjMyFhUUBgYHNjYzMxUoYIZGPD0oRBwvKFs+WWZDdUwaOBm5MWCRdTM3Ri0gLyw1Z1U8e4dPAgRHAAEAGv/0Ab4CigAqAAAXIiYnNxYWMzI2NTQmJiM1MjY2NTQmIyIGByc2NjMyFhUUBgcVFhYVFAYG7ExmICodTjg6SidZTERPIjszKEMdLCVaOFNsQDQ6UDhfDDcjNh4uQDUmOSA/IDYhLzYkHTQjLVVNOkoUBA1TQjhSLAACABEAAAHVAn4ACQAUAAA3MzU0NjcjBgYHEzUhNQEzETMVIxVoyAQBBAwaDjP+4QERXFdX8rkaSBkXLBf+NLA2AZj+dEKwAAABABn/9AHBAn4AIAAAFyImJzcWFjMyNjU0JiMiBgcnEyEVIwc2NjMyFhYVFAYG6kxkISgdTDg7UUo+IS8dLBUBP/cRFy4dN1k2PWIMNiE2HC1PQ0JKFBMcATNHvQwOK1hFRWEyAAACADD/9AHJAooACwAoAAABIgYHFhYzMjY1NCYDIiYmNTQ2NjMyFhcHJiYjIgYGBzY2MzIWFRQGBgEFH0geCEc9L0A7ND5kOkVvPzRLGy4TOB4tSi8CHlAnU2M0VQFIKCxdYk0/Pkn+rESJZoCcRycdMxcbM3dlJStjYj1cNAABACwAAAHHAn4ADQAAMz4CNyE1IRUOAwexBidNP/7CAZs5SCgUBHi/qVdHM0iFh5pdAAMAKf/0AcgCigAfACwAOQAAFyImJjU0NjY3NSYmNTQ2NjMyFhUUBgYHFR4CFRQGBgM2NjU0JiMiBhUUFhYDMjY1NCYmJwYGFRQW+jxeNyM3HiM2MFIzU2EcKBQdMiAzXREgIzo1LDspRAU4RTBPLiYzTwwtUDIqQTEQBBlIMjFKKWBKITsvDwQQLD0rL04tAWgdQCMvQjgvJjIj/sY/MSk0JhIZRiw1RQAAAgAo//QBwAKKAAsAKAAAExQWMzI2NyYmIyIGEyImJzcWFjMyNjY3BgYjIiY1NDY2MzIWFhUUBgZ1OzsgRx8ISD0uQVgzTRouFDceLUwvAR5QKFJjNFUzP2Q5RW4BvT5JKC1dYU39+CccNBccNHhmJixjYj1cNESIZ4CcRwAAAgA3//QB4wKKAAsAFwAABSImNTQ2MzIWFRQGJzI2NTQmIyIGFRQWAQ1kcnJkZHJyZDtISDs7SEgMsJ2eq6uenbBEf4qKenqKin8AAAEAMgAAAPkCfgAIAAAzESM1NjY3MxGndSxCGj8CGjUIFxD9ggAAAQAlAAABwAKKABsAADM1PgI1NCYjIgYHJzY2MzIWFRQGBgc2NjMzFStghEU5PihFGzAqWz1ZZEJ0Sho4GbExYJF1MzdGLSAvLjNnVTx7h08CBEf//wAa//QBvgKKAgYAgwAA//8AIgAAAeYCfgAGAIQRAP//ABn/9AHBAn4CBgCFAAD//wA9//QB1gKKAAYAhg0AAAEALAAAAbkCfgANAAAzPgI3ITUhFQ4DB6cGJUw+/tABjThGKBMEeL+pV0czSIWHml3//wA3//QB1gKKAAYAiA4A//8ANP/0AcwCigAGAIkMAAABAEH/9AC4AHIACwAAFyImNTQ2MzIWFRQGfRkjIxkYIyMMIxsdIyMdGyMAAAEAL/9WAMYAcgASAAAXJzY2JwYiIyImNTQ2MzIWFRQGQxQrMAECBgIYIyQZICVGqjATPygBHRwbHzQtQGH//wBB//QAuAHbAicAlAAAAWkABgCUAAD//wAv/1YAxgHbAicAlAAAAWkABgCVAAD//wBe//QDcwByACYAlB0AACcAlAFsAAAABwCUArsAAAACAFX/9ADMAp4ABQARAAA3AyczBwMHIiY1NDYzMhYVFAZ0CwJTAgscGSMjGRgjI8YBel5e/obSIxsdIyMdGyMAAgBV/0gAzAHyAAUAEQAAFzcTMxMXAyImNTQ2MzIWFRQGZwILOQsCKRkjIxkYIyO4XgF6/oZeAiwkHBsjIxscJAAAAgAm//QBeQKqABkAJQAANyY+AzU0JiMiBgcnNjYzMhYVFA4DFwciJjU0NjMyFhUUBqAHGCstIDEwITsXLyBVN0xbIC8sGwUiGSIiGRkjI8YvSDw2NiAoOR8bKyQvVkonPzg6QirSIxsdIyMdGyMAAAIAMP88AYMB8gAZACUAABciJjU0PgMnMxYOAxUUFjMyNjcXBgYDIiY1NDYzMhYVFAbXTFsgLywaBUkHGCstIDAxIToXMCBVKxkjIxkYIyPEVkonPzg6QiovSDw2NiAoOB4bKyMwAjgkHBsjIxscJAABAFABrwCoArIABQAAEycnMwcHYxADWAMQAa+nXFyn//8AUAGvAVkCsgAmAJ0AAAAHAJ0AsQAAAAEAOQGsALsCuAARAAATIiY1NDY3FwYGFTYzMhYVFAZ4HiE2NBgmJgMGFCEeAawvKzxYHicZOSoBGhkZHgAAAQA/Aa8AwQK7ABEAABMnNjY1BiMiJjU0NjMyFhUUBlYXJiUDBRUgHhcdIjcBryYZOSsBGhgaHi8sPFf//wA5AawBbAK4ACYAnwAAAAcAnwCxAAD//wA/Aa8BcgK7ACYAoAAAAAcAoACxAAD//wA//28AwQB7AgcAoAAA/cD//wA//28BcgB7ACYAowAAAAcAowCxAAAAAQAtAEIA2QG2AAYAADcnNTcXBxe1iIgkdnZCmz6bHpyeAAEANgBCAOIBtgAGAAA3JzcnNxcVWSN2diOJQhyenB6bPv//AC0AQgF3AbYAJgClAAAABwClAJ4AAP//ADYAQgGAAbYAJgCmAAAABwCmAJ4AAAABACkA2wEPARoAAwAANzUzFSnm2z8/AAABACkA3wG3ARgAAwAANzUhFSkBjt85OQABACkA3wL3ARgAAwAANzUhFSkCzt85Of//AEEBAwC4AYECBwCUAAABDwABACgAjwEIAYAACwAANyImNTQ2MzIWFRQGmC1DQy0tQ0OPQjY2Q0M2NkIAAAEADP+CAej/uQADAAAXNSEVDAHcfjc3AAEAUv9QAQkC3AANAAAXJiY1NDY3FwYGFRQWF9Y+RkY+Mzo5OTqwZd2EhN5kGF/dcnLdXwABACb/UADdAtwADQAAFyc2NjU0Jic3FhYVFAZZMzo5OTozPkZGsBhf3XJy3V8YZN6EhN0AAQBe/2gBEQLEAAcAABcRMxUjETMVXrN1dZgDXC/9Ai8AAAEAH/9oANECxAAHAAAXNTMRIzUzER90dLKYLwL+L/ykAAABACL/aAERAsQAKwAAFyImNTQ2NTQmJzU2NjU0JjU0NjMzFSMiBhUUFhUUBgcVFhYVFAYVFBYzMxXkOzoJIzMzIwk6Oy0bKBwGHCAgHAYcKBuYOE03WDIdMAE0ATAcM1g3TTgvKjEuVDMxMwkECTQwM1QuMSovAAABAB//aAENAsQAKwAAFzUzMjY1NCY1NDY3NSYmNTQ2NTQmIyM1MzIWFRQGFRQWFxUGBhUUFhUUBiMfGikbBRsgIBsFGykaLDw5CSQyMiQJOTyYLyoxLlQzMDQJBAkzMTNULjEqLzhNN1gzHDABNAEwHTJYN004AAABAAr/YAFRAsYAAwAAFwEzAQoBCzz+9aADZvyaAAABAFz/BgCWAu4AAwAAFxEzEVw6+gPo/BgAAAEADv9gAVQCxgADAAAFATMBARn+9TsBC6ADZvyaAAIAXP8GAJYC7gADAAcAABMRMxEDETMRXDo6OgEjAcv+Nf3jAdD+MAABADoBpAFoAsgADgAAEyc3JzcXNzMXNxcHFwcniig5YQ9mCTEJZw9hOCdHAaQdXiguGWxrGC4oXh1WAAACAC3/wAHEAqwANABEAAAXIiYnNxYWMzI2NTQuBDU0NjcmJjU0NjMyFhcHJiYjIgYVFB4EFRQGBxYWFRQGBgMUHgIXNjY1NC4CJwYG7DVZHzIZOigoLSg/Rj8oMSUPEU1LL08cKBg2ISolKD9HPygvJg4QKUmmJz5HHx0fJz5GHxwhQCYhLRgcKRwdJR0eKT4uLEAVECgaNE4iFzUUGiUaGyUdHio+Li88FhAoGic+JAGcISsgHRIOJiEiLCAdEhAoAAIAKf+wAdACkAADAA4AAAURMxEDIiYmNTQ2NjMzEQF8VKtIckI/bUUsUALg/SABMi5gS09dKf5SAAADADH/9QK3Ao0AEwAjAD8AAAUiLgI1ND4CMzIeAhUUDgInMjY2NTQmJiMiBgYVFBYWNyImJjU0NjYzMhYXByYmIyIGFRQWMzI2NxcGBgF0QXVaMzNadUFBdVozM1p1QUx9S0t9TEt+S0t+UzJTMTRVMCo7GCMUKRo3Q0E2IDAWHhw+Cy9XfExMelYuLlZ6TEx8Vy8qSYRXV4JISIJXV4RJXi9ZPjpVLiEYJxQVSztCTRkTKhghAAQAFwE/AZACyQAPAB8ALQA1AAATIiYmNTQ2NjMyFhYVFAYGJzI2NjU0JiYjIgYGFRQWFic1MzIWFRQGBxcjJyMVNTMyNTQmIyPTM1YzM1YzNFYzM1Y0KkMoKEMqKkMnJ0MeTCAuFBEuLiMpGisSFxwBPzJZOjtYMjJYOzpZMiUoSS8vSSkpSS8vSSg9yx0kEh8GU0ZGZiIPEgACAAMBbgJgAqQABwAbAAATESM1IRUjETMRMxcXMzc3MxEjNTcjByMnIxcVZmMBA2SUSS8cBBwuSDcHBEkvSQQHAW4BADY2/wABNnROTnT+yolpwsJpiQAAAgAz/2UDHAKGAEAATgAABSImJjU0PgIzMhYWFRQGBiMiJicjBgYjIiY1ND4CMzIWFzM3MwcGMzI2NjU0JiYjIg4CFRQWFjMyNjcXBgYDMjY3NyYmIyIGBhUUFgGTYqBeQnWWVGWTUD5cLig6BQIZQCEzRRsyRywaKQ0CCzcnHlQgPSk/e1tDfWQ6TodVLlIiFitfRhUuGR0OHhQnOR8om1KgdGWjdD9TlWNXdzwnJR0nSUQoUkQqFxkoyHUxXUFRfkg2ZY5YYYlIGhMxGhkBDBwfnxcTNk4mMCoAAAIAIwAAAdMCigAbAB8AADM3IzUzNyM1MzczBzM3MwczFSMHMxUjByM3IwcTMzcjWhlQVxJVXBc1F4UYNRhRVxJVXBk1GIQZIIQShMw5lDq3t7e3OpQ5zMzMAQWUAP//AEUAAACzArYCBgAkAAD//wBXAYsA7AMRAgcAxwAAAYv//wAoAYsBQAMdAgcAyAAAAYv//wAjAX8BPwMdAgcAyQAAAYv//wAqAYsBUAMRAgcAygAAAYsAAgAj//QBTQGSAAsAFwAAFyImNTQ2MzIWFRQGJzI2NTQmIyIGFRQWuENSUkNDUlJDJjAwJicwMAxsZGJsbGJkbDNPTk5NTU5OTwABAFcAAADsAYYACAAAMxEjNTY2NzMRrFUhLBQ0ATQqBhMP/noAAAEAKAAAAUABkgAXAAAzNTY2NTQmIyIGByc2NjMyFhUUBgYHMxU0W14oIxkqESYXQyg7RyZBKqUlUmcsJiwhGCMiKkA+JUVIKzcAAQAj//QBPwGSACYAABciJic3FhYzMjY1NCYjNTI2NTQmIyIGByc2NjMyFhUUBgcWFhUUBrQvSxcrEjIfIC5AOTI4JyAWKBEnGj4oMkomHiEzUgwrISEbHyQiIiMpKB4cIhsUIh0jNzIiMA4IMSc2PwAAAgAqAAABUAGGAAUAEAAANzM1NyMHFzUjNTczFTMVIxVtbwQEMjKypEg6OpZGbVH4aCH98C5oAAABACP/9AFDAYYAHgAAFyImJzcWFjMyNjU0JiMiBgcnNzMVIwc2NjMyFhUUBrc0SRcrEzAiIy0uJBcjEB8S1aALDiARNUtRDCshIRsfMSUoLhIOF7w4XwYJRD88SwAAAgAt//QBRgGSAAsAJAAANyIGBxYWMzI2NTQmByImNTQ2MzIWFwcmJiMiBgc2MzIWFRQGBr8WKBcFMCQgJyYgRVJiSCItEBoOIRQrPwUrNzs/IjvGExg6Oi0jIyzSaVxuaxIMLQoOSEooRTglPCMAAAEAMgAAAUMBhgAMAAAzPgI3IzUhFQ4CB4MEGjIoyQERLzQWBERuZzY3JDxvc0QAAAMALf/0AUABkgAZACUAMQAAFyImNTQ2NzUmJjU0NjMyFhUUBgcVFhYVFAYnNjY1NCYjIgYVFBYXMjY1NCYmJwYVFBa2PUwuHBkhRzI1SCgUIidPJBYUJhsZJTIMIC4cLhs2KwxBLCU4EAQRKR8vODgvIi4OBBExIy9B6xEmExoeHxgeIscnHBgcEwsgMRoqAAIAJ//0AUABkgALACQAABMUFjMyNjcmJiMiBhMiJic3FhYzMjY3BiMiJjU0NjYzMhYVFAZiJyQXKRYEMSUfKDQiLREbDiEULD8EKzc6QCI7JEZSYgEPIi0TGDo6Lf7CEgwtCg5ISylGNyY7I2lcbmsAAAEAQf+xAMYB0wANAAAXJiY1NDY3FwYGFRQWF5krLS0rLSYhISZPPYBVVH89Fjp+QkN9PAABACf/sQCsAdMADQAAFyc2NjU0Jic3FhYVFAZWLyYhISYvKS0tTxY8fUNCfjoWPX9UVYAAAQAr//gAhgBXAAsAABciJjU0NjMyFhUUBlkUGhoUFBkZCBoVFhoaFhUaAAABACH/jACQAFcAEQAAFyc2NjUGIyImNTQ2MzIWFRQGMRAeIgMFERwcExkcM3QlCygcARUVFRklJCxFAP//ACMA/gFNApwCBwDGAAABCv//AFcBCgDsApACBwDHAAABCv//ACgBCgFAApwCBwDIAAABCv//ACMA/gE/ApwCBwDJAAABCv//ACoBCgFQApACBwDKAAABCv//ACMA/gFDApACBwDLAAABCv//AC0A/gFGApwCBwDMAAABCv//ADIBCgFDApACBwDNAAABCv//AC0A/gFAApwCBwDOAAABCv//ACcA/gFAApwCBwDPAAABCv//AEEAuwDGAt0CBwDQAAABCv//ACcAuwCsAt0CBwDRAAABCv//ACsBAgCGAWECBwDSAAABCv//ACEAlgCQAWECBwDTAAABCv//ACUBgwEqAtQCBgDkAAD//wAeAYMBTgLUAgYA5QAAAAIAJQGDASoC1AAZACIAABMiJjU0NjcmJiMiBgcnNjYzMhYVFSMnIwYGJzI3NQYGFRQWiCw3YGcBGiMaNxQXGUUnPDcyBwQUMg0nK009HwGDMys1OAogKRUNKw8bRj/EJRIbMShVCCccGRkAAAIAHgGDAU4C1AAPABsAABMiJiY1NDY2MzIWFhUUBgYnMjY1NCYjIgYVFBa2KUYpKUYpKUUqKkUpKS8vKSkwMAGDKEs1NkwnJ0w2NUsoM0E0NUFBNTRBAAACACkBrQEjAq0ADQAZAAATIiYmNTQ2MzIWFRQGBicyNjU0JiMiBhUUFqYhOSNLMjJLIzgiISoqISEqKgGtHzomO0ZGOyY6Hy4uIyUuLiUjLgAAAgAaAGcB1wItAB4AKgAANyc3JjU0Nyc3FzYzMhc3FwcWFhUUBgcXBycGBiMiJzcyNjU0JiMiBhUUFkYsQCQkQCxEMD8+MEQsQREUFBFBLEQXOh1AL28wRkYwMUZGZy1BMUNEMUItRiUlRi1CFzsjIjsXQS1FExMmF0o8PEpKPDxKAAEANP+SAbUC7AAtAAAXNSYmJzcWFjMyNjU0LgQ1NDY3NTMVFhYXByYmIyIGFRQeBBUUBgcV3jBaICYgTS44NyhAR0AoUEE8MEMbLBw1KS42KEBHQChUR25jBSsdORwnOS4pNCYjK0AxQ1kKZWMFKh0xGx40LCQvIiItRjZHXQplAAABADUAAAHFAooAKgAAMzU2NjU0JicjNTczJiY1NDYzMhYXByYmIyIGFRQWFzMVIxYWFRQGBxUhFTYzNwQDZEMSChFnUzVMGjATMCI2OQ8Jn5ICAyAeARkyHGA4DhwNNAQgPh9VYysgLxceQTQfPCA4DRwPNEcfBEcAAAEAFwAAAdoCfgAdAAAzNSM1MzUjNTMDMxcWFhczNjY3NzMDMxUjFTMVIxXPoqKijaNWTg8dEAQRHQ9OVKSOo6OjnjBBLwFAqyFDIyNDIav+wC9BMJ4AAQAX//QB6wKKADEAAAUiJicjNTcmNDU0NDcjNTc2NjMyFhcHJiYjIgYHMxUhBhQVFBQXMxUjFhYzMjY3FwYGAT9afRFAOwEBO0ARg2ItThoxFTIgQlAN/v7+AQHa1Q1OPSU4GTEhVAyCdSsECRIJCBEHLAV2hS0hLxohY1YxBw4IChMJMFVgJSIsKzIAAAIAPf/fAcYCjQAbACIAAAU1JiY1NDY2NzUzFRYWFwcmJicRNjY3FwYGBxUDFBYXEQYGAQZZcDVcODQsQBcoFC0aIDQUJB1IJ6s/ODdAIWgLempFZDwIamcCIhY0EhcB/qgCGxI0GiIDZwFXQ1gNAU8NWAAAAf9Z//QA+wKcAAMAAAcBMwGnAWo4/pYMAqj9WAD///9Z//QA+wKcAgYA7QAA////Wf/0APsCnAIGAO0AAP//ACP/9AMWApwAJwDGAAABCgAnAO0BcgAAAAcAxgHJAAD//wA///QC7QKcACcAx//oAQoAJwDtAVsAAAAHAMoBnQAA//8AP//0AvkCnAAnAMf/6AEKACcA7QFHAAAABwDIAbkAAP//ACL/9AL8ApwAJwDJ//8BCgAnAO0BgQAAAAcAygGsAAAAAQAiAGgBzwIsAAsAADc1IzUzNTMVMxUjFdi2tkG2tmjDPsPDPsMAAAEAIgErAc8BaQADAAATNSEVIgGtASs+PgAAAQAyAH4BvwIVAAsAADcnNyc3FzcXBxcHJ14sm5ssm5osm5ssmn4tn54tn58tnp8toAADACIAYAHPAjMAAwAPABsAABM1IRUHIiY1NDYzMhYVFAYDIiY1NDYzMhYVFAYiAa3WFyAgFxcfHxcXICAXFx8fASs+PsseGBceHhcYHgFoHhgXHh4XGB7//wAiAMABzwHUAiYA9QBrAAYA9QCVAAEAIgCDAc8CFQAJAAAlJTUlFQcHFRcXAc/+UwGt04aG04OoQqhHTjIEMk4AAQAiAIMBzwIVAAkAADc1Nzc1Jyc1BRUi04aG0wGtg0dOMgQyTkeoQgAAAgAiAAABzwIsAAsADwAANzUjNTM1MxUzFSMVBzUhFdi2tkG2tvcBrX+xPr6+PrF/Pj4AAAEAPAEcAbUCngAJAAATEzMTIycnIwcHPJhJmEhBMQQyQQEcAYL+frCFhbAAAQAkAQEBzQGTABcAAAEiLgIjIgYHJzY2MzIeAjMyNjcXBgYBUB4vKCcVFiYRLhtCIB4vKCcVFiYRLhtCAQEZIhkdICEwKhkiGR0gIi8qAAABACIAaAHPAWkABQAAJTUhNSERAY3+lQGtaMM+/v8AAAEAKv/nAkECDwAJAAAFEQcnATMBBycRARO7LgEJBAEKL7sZAbfJKgEQ/vAqyf5JAAEAKv/nAkECDwAJAAAFATcXETMRNxcBATP+9y67RLsv/vYZAREqyQG2/krJKv7vAAEAUgHBALkCsgAEAAATNxcHB1IWUQ8iAcLwAVuV//8AUgHBAWoCsgAmAQEAAAAHAQEAsQAA//8AgAI9AUcDDQAHAQsBDwAA//8A1wI9AZ4DDQAHAQ4BDwAA//8AdAI4AaoC5AAHAREBDwAA//8AYgJBAbwC0QAHARMBDwAA//8AfQJLAaECrwAHARgBDwAA//8AigJZAZQCkgAHARUBDwAA//8AoQIqAX0C7wAHARwBDwAA//8Atv8eAWMAAwAHASABFQAAAAH/cQI9ADgDDQADAAATJzcXDp06jQI9mTenAAAB/3kCuQA2A2MAAwAAEyc3FxGYL44CuXM3gAAAAf+lAi4AIQMCAAMAAAMnNxcYQ1UnAi7FD8kAAAH/yAI9AI8DDQADAAADJzcXDiqNOgI9Kac3AAAB/8oCuQCHA2MAAwAAAyc3FxElji8CuSqANwAAAf/fAi4AXwMCAAMAABMnNxcYOStVAi4LyQ8AAAH/ZQI4AJsC5AAHAAADNzMXBycjB5tyUnIjdgR2AliMjCBxcQAAAf9sArsAlANGAAcAAAMnNzMXBycjcCRpVmkkbgQCuxpxcRpdAAAB/1MCQQCtAtEAFwAAEyIuAiMiBgcnNjYzMh4CMzI2NxcGBkgbJh8eERYXAjcCLzQbJiAdEhYWAjcCLwJBGSIZLCMDO00ZIhktIgQ6TQAB/08CxQCxA0kAFgAAEyIuAiMiBgcnNjYzMh4CMzI3FwYGSxwpHx4SExoDOAM2LRwpHx4SKAg4AzYCxRUdFSIgBDlCFR0VQgQ6QQAAAf97AlkAhQKSAAMAAAM1IRWFAQoCWTk5AAAB/3oC3wCGAxgAAwAAAzUhFYYBDALfOTkAAAH/yQJKADcCtgALAAARIiY1NDYzMhYVFAYYHx8YGB8fAkodGRgeHhgZHQAAAv9uAksAkgKvAAsAFwAAAyImNTQ2MzIWFRQGMyImNTQ2MzIWFRQGYBYcHBYVHByrFRwcFRYcHAJLHRUVHR0VFR0dFRUdHRUVHQAC/2gCywCYAy0ACwAXAAADIiY1NDYzMhYVFAYzIiY1NDYzMhYVFAZnFRwcFRYbG7gWGxsWFRwcAsscFRYbGxYVHBwVFhsbFhUcAAH/vAI8AFAC+AAMAAADJzY2NTQnNxYWFRQGIAkYIFMFREtEAjwoBhYTKwQ2ATEsKysAAf+8AroATwNoAAwAAAMnNjY1NCc3FhYVFAYgCRggUwlBSUMCuiYHFBMnAzACKSYrKgAC/5ICKgBuAu8ACwAXAAARIiY1NDYzMhYVFAYnMjY1NCYjIgYVFBYyPDwyMjw8MhghIRgYISECKjgrKjg4Kis4JSIcHCEhHBwiAAL/nwK7AGEDcAALABcAABEiJjU0NjMyFhUUBicyNjU0JiMiBhUUFio3NyopODgpFB8fFBYeHgK7MigpMjIpKDIkHBoaHBwaGhz////J/zIAN/+eAgcBFwAA/OgAAf+h/x4ATv/GAA0AAAcnNjY1NCYnNxYWFRQGVwg/LiQjEkE0VeIoBRcUFBMDJgckIissAAH/of8eAE4AAwAPAAAHJzY2NTQmJzczBxYWFRQGVwg/LiIrLDUdIyZV4igFFxYUFgZbQwggHyssAAAB/6H/HgBOAAMADwAAByc2NjU0Jic3MwcWFhUUBlcIPy4iKyw1HSMmVeIoBRcWFBYGW0MIIB8rLAAAAf/d/vIAI/+5AAMAAAM3MxcjBjoG/vLHxwAD/2UCMwCbAwYACwAPABsAAAMiJjU0NjMyFhUUBhcnNxcXIiY1NDYzMhYVFAZxEhgYEhEYGGMrLUIqERgYERIYGAJTFxMTFxcTExcgCskOpRcTExcXExMXAAAD/3ICTACOA0wACwAPABsAAAMiJjU0NjMyFhUUBjcnNxcXIiY1NDYzMhYVFAZgExsbExQZGUUjUjEHExoaExQaGgJMGhMUGhoUExp1GHMi3hoTFBoaFBMaAAAD/3ICTACOA0wACwAPABsAAAMiJjU0NjMyFhUUBjcnNxcXIiY1NDYzMhYVFAZgExsbExQZGVNgMVI2ExoaExQaGgJMGhMUGhoUExp1aSJzjRoTFBoaFBMaAAAD/2UCMwCbAwYACwAPABsAAAMiJjU0NjMyFhUUBhcnNxc3IiY1NDYzMhYVFAZxEhgYEhEYGF1AQilJERgYERIYGAJTFxMTFxcTExcgxQ7JFhcTExcXExMXAAAD/2cCTACZAyYAFQAhAC0AABMiJiYjIgYHJzY2MzIWFjMyNjcXBgYHIiY1NDYzMhYVFAYzIiY1NDYzMhYVFAZHIyskGBEYAyoCKiYjKyQYERgDKgIqzRMbGxMUGRmsExoaExQaGgLDGRgXFwQnNRgZFxcEJzV3GhMUGhoUExoaExQaGhQTGgAAA/9yAkwAjgMdAAsADwAbAAADIiY1NDYzMhYVFAYnNSEVByImNTQ2MzIWFRQGYBMbGxMUGRk6AQslExoaExQaGgJMGhMUGhoUExqiLy+iGhMUGhoUExoAAAL/egI6AOsDEQAHAAsAAAMnNzMXBycjFyc3F2kdZERkHWcEiCNXMQI6IlxcIkgFHHgiAAAC/wkCOgCGAxEABwALAAADJzczFwcnIwcnNxdpHWREZB1nBIRxMGQCOiJcXCJIBXAkdQAAAv96AjoA0QMXAAcAFQAAAyc3MxcHJyMXJzY2NTQmJzcWFhUUBmkdZERkHWcEdgkTFyAlBzs/OgI6IlxcIkgHIwUSEBQUASkBJSMoIwAAAv93AjoAiQMjAAcAHQAAAyc3MxcHJyM3IiYmIyIGByc2NjMyFhYzMjY3FwYGaR1kRGQdZwRDHSokEw8OBCsEISMeKiMTDw4EKwQhAjoiWFgiRUcVFRQWBCcyFRUUFgQmMwAA) format('truetype');
}
</style>
  <style type="text/css">@font-face {
font-family: 'Open Sans';
font-style: normal;
font-weight: 400;
font-stretch: normal;
src: url(data:font/ttf;base64,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) format('truetype');
}
@font-face {
font-family: 'Open Sans';
font-style: normal;
font-weight: 600;
font-stretch: normal;
src: url(data:font/ttf;base64,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) format('truetype');
}
</style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<div style="width: 100%; height: 56px;">
   <div style="height: 25px; left: 0; position: absolute; top: 0; background-color: #3e3e40; right: 0; color:#FFF; text-transform:uppercase; text-align:right; padding: 5px 15px 5px;">
     eGrabber sample programs **********
   </div>
</div>
<img src="data:image/png;base64,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">
<h1 id="sample-programs">Sample programs</h1>
<p>Sample programs for eGrabber are provided in a dedicated package
named
<code>egrabber-&lt;OS&gt;-sample-programs-&lt;YY.MM.RE.BU&gt;.&lt;EXT&gt;</code>
where <code>&lt;OS&gt;</code> is the operating system
(<code>linux</code>, <code>macos</code>, or <code>win</code>) and
<code>&lt;YY.MM.RE.BU&gt;</code> is the version number of the
package.</p>
<p>These sample programs will use Coaxlink by default. To use them with
Grablink:</p>
<ul>
<li>either define <code>EURESYS_DEFAULT_GENTL_PRODUCER=grablink</code>
in your environment;</li>
<li>or pass <code>Grablink()</code> to the <code>EGenTL</code>
constructor.</li>
</ul>
<p>Similarly, to use Gigelink (which requires a license<a href="#fn1" class="footnote-ref" id="fnref1" role="doc-noteref"><sup>1</sup></a>):</p>
<ul>
<li>either define <code>EURESYS_DEFAULT_GENTL_PRODUCER=gigelink</code>
in your environment;</li>
<li>or pass <code>Gigelink()</code> to the <code>EGenTL</code>
constructor.</li>
</ul>
<p>And for Playlink (which also requires a license<a href="#fn2" class="footnote-ref" id="fnref2" role="doc-noteref"><sup>2</sup></a>):</p>
<ul>
<li>either define <code>EURESYS_DEFAULT_GENTL_PRODUCER=playlink</code>
in your environment;</li>
<li>or pass <code>Playlink()</code> to the <code>EGenTL</code>
constructor.</li>
</ul>
<table>
<colgroup>
<col style="width: 33%" />
<col style="width: 39%" />
<col style="width: 9%" />
<col style="width: 17%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">Sample program</th>
<th style="text-align: left;">Description</th>
<th style="text-align: left;">Language</th>
<th style="text-align: left;">OS</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><code>cpp/egrabber-snippets</code></td>
<td style="text-align: left;">Collection of <a href="#egrabber-c-code-snippets">code snippets</a> for eGrabber</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/display-latest-buffer</code></td>
<td style="text-align: left;">Win32 application showing image
acquisition and display, discarding buffers when processing is slower
than image acquisition</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cpp/egrabber-mfc</code></td>
<td style="text-align: left;">MFC application showing image acquisition
and display</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/sdl2/display-all-buffers</code></td>
<td style="text-align: left;">SDL2 application showing image acquisition
and display of all acquired buffers</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cpp/sdl2/display-latest-buffer</code></td>
<td style="text-align: left;">SDL2 application showing image acquisition
and display, discarding buffers when processing is slower than image
acquisition (using OnDemand callback model)</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/sdl2/display-latest-buffer-mt</code></td>
<td style="text-align: left;">SDL2 application showing image acquisition
and display, discarding buffers when processing is slower than image
acquisition (using MultiThread callback model)</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cpp/sdl2/egrabber-cuda-sdl2</code></td>
<td style="text-align: left;">SDL2 application showing image acquisition
with eGrabber and processing with CUDA (on Nvidia GPU)</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/amd-direct-gma</code></td>
<td style="text-align: left;">OpenGL application showing image
acquisition, direct transfer to AMD GPU memory, and display</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cpp/nvidia-cuda</code></td>
<td style="text-align: left;">OpenGL console application showing image
acquisition with eGrabber and processing with CUDA (on Nvidia GPU)</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/ffc-wizard</code></td>
<td style="text-align: left;">Console application showing how to compute
coefficients for the Coaxlink FFC (flat-field correction)</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cpp/exif</code></td>
<td style="text-align: left;">Collection of <a href="#exif-sample-programs">sample programs</a> showing how to use the
<em>Coaxlink Quad CXP-12 JPEG</em> and how to embed metadata in [EXIF]
files</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cpp/grablink-serial-communication-mfc</code></td>
<td style="text-align: left;">Simple application demonstrating Camera
Link serial communication through the clseregl library on a Grablink Duo
board</td>
<td style="text-align: left;">C++</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cs/egrabber</code></td>
<td style="text-align: left;">Console application showing how to use
eGrabber and callbacks in C#</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cs/grabn</code></td>
<td style="text-align: left;">Console application showing image
acquisition</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cs/grabn.NET</code></td>
<td style="text-align: left;">.NET console application showing image
acquisition</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cs/display-latest-buffer</code></td>
<td style="text-align: left;">Windows Forms application showing image
acquisition and display, discarding buffers when processing is slower
than image acquisition</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cs/egrabber-wpf</code></td>
<td style="text-align: left;">WPF application showing image acquisition
and display</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>cs/*-recorder*</code></td>
<td style="text-align: left;">Collection of <a href="#sample-programs">sample programs</a> <a href="#egrabber-recorder-c-sample-programs">eGrabber Recorder C# sample
programs</a> for eGrabber Recorder</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>cs/grablink-serial-communication</code></td>
<td style="text-align: left;">Simple application demonstrating Camera
Link serial communication through the clseregl library on a Grablink Duo
board</td>
<td style="text-align: left;">C#</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>python/*</code></td>
<td style="text-align: left;">Collection of <a href="#sample-programs">sample programs</a> <a href="#egrabber-python-sample-programs">eGrabber Python sample
programs</a> for eGrabber</td>
<td style="text-align: left;">Python</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>python/*-recorder</code></td>
<td style="text-align: left;">Collection of <a href="#sample-programs">sample programs</a> <a href="#egrabber-recorder-python-sample-programs">eGrabber Recorder
Python sample programs</a> for eGrabber Recorder</td>
<td style="text-align: left;">Python</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>python/display-all-buffers*</code></td>
<td style="text-align: left;">Collection of extra <a href="#sample-programs">sample programs</a> <a href="#egrabber-python-sample-programs">eGrabber Python sample
programs</a> for eGrabber showing how to process acquired data with
numpy, opencv, Pillow, etc.</td>
<td style="text-align: left;">Python</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>python/display-latest-buffer</code></td>
<td style="text-align: left;">Simple application showing image
acquisition and display, discarding buffers when processing is slower
than image acquisition</td>
<td style="text-align: left;">Python</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>vb/grabn</code></td>
<td style="text-align: left;">Console application showing image
acquisition</td>
<td style="text-align: left;">VB.NET</td>
<td style="text-align: left;">Windows</td>
</tr>
<tr class="odd">
<td style="text-align: left;"><code>vb/display-latest-buffer</code></td>
<td style="text-align: left;">Windows Forms application showing image
acquisition and display, discarding buffers when processing is slower
than image acquisition</td>
<td style="text-align: left;">VB.NET</td>
<td style="text-align: left;">Windows</td>
</tr>
</tbody>
</table>
<table>
<colgroup>
<col style="width: 33%" />
<col style="width: 39%" />
<col style="width: 9%" />
<col style="width: 17%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">Additional files</th>
<th style="text-align: left;">Description</th>
<th style="text-align: left;">Language</th>
<th style="text-align: left;">OS</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><code>tools/stripeGeometry.py</code></td>
<td style="text-align: left;">Python script showing the effect of the
image transfer settings: <code>StripeArrangement</code>,
<code>StripeHeight</code>, <code>StripePitch</code>,
<code>StripeOffset</code>, and <code>BlockHeight</code></td>
<td style="text-align: left;">Python</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
<tr class="even">
<td style="text-align: left;"><code>LICENSE</code></td>
<td style="text-align: left;">License text for eGrabber sample
programs</td>
<td style="text-align: left;">All</td>
<td style="text-align: left;">Windows, Linux, macOS</td>
</tr>
</tbody>
</table>
<h1 id="nvidia-rdma">NVIDIA RDMA</h1>
<p><code>NVIDIA RDMA</code> is only suppported on Linux.</p>
<p><code>NVIDIA RDMA</code> samples require a <code>NVIDIA</code>
<code>GPU</code> that suppports <code>RDMA</code>.</p>
<p>The <code>NVIDIA RDMA</code> samples allocate memory on the
<code>GPU</code> and announce this memory using
<code>NvidiaRdmaMemory</code>.</p>
<p>See the following files in the eGrabber sample programs:</p>
<ul>
<li><code>cpp/egrabber/samples/503-grabn-cuda-rdma-process.*</code></li>
<li><code>cpp/nvidia/egrabber-cuda</code> with the command line argument
<code>cudaRDMA</code></li>
</ul>
<h2 id="installation-instructions">Installation instructions:</h2>
<ul>
<li><code>NVIDIA CUDA</code> drivers:
<ul>
<li>Follow the installation instructions from: <a href="https://developer.nvidia.com/cuda-downloads?target_os=Linux&amp;target_arch=x86_64&amp;Distribution=Ubuntu&amp;target_version=20.04&amp;target_type=deb_network" class="uri">https://developer.nvidia.com/cuda-downloads?target_os=Linux&amp;target_arch=x86_64&amp;Distribution=Ubuntu&amp;target_version=20.04&amp;target_type=deb_network</a></li>
</ul></li>
<li><code>NVIDIA</code> driver sources:
<ul>
<li>These are needed to produce the <code>Module.symvers</code> file
associated with the installed nvidia driver. This file will be required
to install the <code>eGrabber</code> package</li>
<li>Select the appropriate driver from <a href="https://www.nvidia.com/download/index.aspx?lang=en-us" class="uri">https://www.nvidia.com/download/index.aspx?lang=en-us</a></li>
<li>Make sure to download the version that matches the
<code>nvidia-&lt;version&gt;</code> already installed in
<code>/usr/src/</code></li>
<li>Extract the archive with <code>-x</code> option</li>
<li>Change to directory <code>kernel</code> in the extracted
archive</li>
<li>Run <code>make module</code></li>
<li>The file <code>Module.symvers</code> should have been generated</li>
</ul></li>
<li><code>eGrabber</code> package
<ul>
<li>Extract the egrabber-linux-x86_64 archive</li>
<li>Install the package with the following command:
<ul>
<li><code>sudo NVIDIA_KERNEL_PATH=&lt;dir path containing Module.symvers&gt; ./install.sh</code></li>
</ul></li>
</ul></li>
</ul>
<p>The line <code>Enabling NVIDIA RDMA build</code> should appear during
the installation of the <code>eGrabber</code> package.</p>
<p>A successful build can be confirmed if the command
<code>lsmod | grep coaxlink</code> (or
<code>lsmod | grep grablink</code>) indicates that <code>coaxlink</code>
(or <code>grablink</code>) module depends on <code>nvidia</code>.</p>
<h2 id="egrabber-c-code-snippets">eGrabber C++ code snippets</h2>
<p><code>cpp/egrabber</code> contains the following code snippets:</p>
<table>
<colgroup>
<col style="width: 38%" />
<col style="width: 61%" />
</colgroup>
<thead>
<tr class="header">
<th>Snippet</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><code>100-grabn</code></td>
<td>Simple Grab N frames using ScopedBuffer class</td>
</tr>
<tr class="even">
<td><code>101-singleframe</code></td>
<td>Single frame grabbing using ScopedBuffer class</td>
</tr>
<tr class="odd">
<td><code>102-action-grab</code></td>
<td>Single frame triggered by an action</td>
</tr>
<tr class="even">
<td><code>105-area-scan-grabn</code></td>
<td>Set image size and Grab N frames (area-scan)</td>
</tr>
<tr class="odd">
<td><code>106-line-scan-grabn</code></td>
<td>Set image size and Grab N frames (line-scan)</td>
</tr>
<tr class="even">
<td><code>110-get-string-list</code></td>
<td>Basic usage of EGrabber method getStringList</td>
</tr>
<tr class="odd">
<td><code>120-converter</code></td>
<td>Measure FormatConverter speed</td>
</tr>
<tr class="even">
<td><code>130-using-buffer</code></td>
<td>Simple Grab N frames using Buffer class</td>
</tr>
<tr class="odd">
<td><code>140-genapi-command</code></td>
<td>Queries on GenApi commands</td>
</tr>
<tr class="even">
<td><code>150-discover</code></td>
<td>Discover and create eGrabbers or cameras with EGrabberDiscovery</td>
</tr>
<tr class="odd">
<td><code>200-grabn-callbacks</code></td>
<td>Grab N frames and get DataStream events with callbacks</td>
</tr>
<tr class="even">
<td><code>201-grabn-pop-oneof</code></td>
<td>Grab N frames and get DataStream events using
pop(OneOf&lt;&gt;)</td>
</tr>
<tr class="odd">
<td><code>210-show-all-grabbers</code></td>
<td>Show available grabbers</td>
</tr>
<tr class="even">
<td><code>211-show-all-grabbers-ro</code></td>
<td>Show available grabbers (devices are opened with
DEVICE_ACCESS_READONLY)</td>
</tr>
<tr class="odd">
<td><code>212-create-all-grabbers</code></td>
<td>Create available grabbers</td>
</tr>
<tr class="even">
<td><code>213-egrabbers</code></td>
<td>Use available grabbers with EGrabbers</td>
</tr>
<tr class="odd">
<td><code>220-get-announced-handles</code></td>
<td>Get info and handles of announced buffers</td>
</tr>
<tr class="even">
<td><code>221-queue-buffer-ranges</code></td>
<td>Create and use 2 sets of buffers configured differently</td>
</tr>
<tr class="odd">
<td><code>230-script-vars</code></td>
<td>Pass data between native code and Euresys script</td>
</tr>
<tr class="even">
<td><code>231-script-var</code></td>
<td>Create and use virtual features from native code and Euresys
scripts</td>
</tr>
<tr class="odd">
<td><code>240-user-memory</code></td>
<td>Grab into user allocated buffer</td>
</tr>
<tr class="even">
<td><code>241-multi-part</code></td>
<td>Grab N multi-part buffers using Buffer class</td>
</tr>
<tr class="odd">
<td><code>250-using-lut</code></td>
<td>Configure and enable the LUT processor</td>
</tr>
<tr class="even">
<td><code>260-recorder-read-write</code></td>
<td>Write/Read buffers to/from a Recorder container</td>
</tr>
<tr class="odd">
<td><code>261-recorder-parameters</code></td>
<td>Show Recorder parameters</td>
</tr>
<tr class="even">
<td><code>270-multicast-master</code></td>
<td>Sending packets on multicast group</td>
</tr>
<tr class="odd">
<td><code>271-multicast-receiver</code></td>
<td>Save the image received on the multicast group</td>
</tr>
<tr class="even">
<td><code>300-events-mt-cic</code></td>
<td>CIC events on EGrabber Multi-Thread Configuration</td>
</tr>
<tr class="odd">
<td><code>301-events-st-all</code></td>
<td>All events on EGrabber Single-Thread Configuration</td>
</tr>
<tr class="even">
<td><code>302-cxp-connector-detection</code></td>
<td>Show CoaXPress events related to connection and device
discovery</td>
</tr>
<tr class="odd">
<td><code>310-high-frame-rate</code></td>
<td>Grab in high frame rate mode for 10 seconds</td>
</tr>
<tr class="even">
<td><code>311-high-frame-rate</code></td>
<td>Process images as soon as available in high frame rate mode for 10
seconds</td>
</tr>
<tr class="odd">
<td><code>312-part-timestamps</code></td>
<td>Show timestamp of each buffer part in HFR mode</td>
</tr>
<tr class="even">
<td><code>320-cl-serial-cli</code></td>
<td>Command line interface for serial communication with a Camera Link
camera</td>
</tr>
<tr class="odd">
<td><code>321-gencp-serial</code></td>
<td>Simple Grab N frames with a GenCP camera</td>
</tr>
<tr class="even">
<td><code>330-metadata-insertion</code></td>
<td>Insert buffer and line metadata into a buffer and get them</td>
</tr>
<tr class="odd">
<td><code>340-dma-roi</code></td>
<td>Grab N frames but store a smaller region in the user buffers</td>
</tr>
<tr class="even">
<td><code>341-dma-deinterlace</code></td>
<td>Grab and deinterlace N frames</td>
</tr>
<tr class="odd">
<td><code>342-dma-roi-deinterlace</code></td>
<td>Grab N frames but store a deinterlaced smaller region in the user
buffers</td>
</tr>
<tr class="even">
<td><code>500-grabn-cuda-process</code></td>
<td>Grab N frames and process them with cuda operations</td>
</tr>
<tr class="odd">
<td><code>501-all-grabbers-cuda-process</code></td>
<td>Use all available interfaces and devices to grab N frames and
process them with cuda operation</td>
</tr>
<tr class="even">
<td><code>502-grabn-cuda-copy-and-process</code></td>
<td>Grab N frames, copy the buffers to the cuda device and process them
with cuda operations</td>
</tr>
<tr class="odd">
<td><code>503-grabn-cuda-rdma-process</code></td>
<td>Grab N frames in the GPU memory with RDMA and process them with cuda
operations</td>
</tr>
<tr class="even">
<td><code>600-thread-start-stop-callbacks</code></td>
<td>Perform specific operations on a callback thread when it
starts/stops</td>
</tr>
<tr class="odd">
<td><code>610-line-scan-array</code></td>
<td>Array of (contiguous) buffers on Line-Scan with EGrabber
Single-Thread</td>
</tr>
<tr class="even">
<td><code>620-multiple-camera</code></td>
<td>Acquire data from all cameras</td>
</tr>
<tr class="odd">
<td><code>650-multistream</code></td>
<td>Acquire data from 4 data streams on the same device</td>
</tr>
<tr class="even">
<td><code>700-memento</code></td>
<td>Generate memento waves</td>
</tr>
<tr class="odd">
<td><code>800-process-latest-buffer</code></td>
<td>Simulate a busy environment and acquire images, discarding some
buffers when busy</td>
</tr>
</tbody>
</table>
<h2 id="exif-sample-programs"><code>EXIF</code> sample programs</h2>
<p><code>cpp/exif</code> contains the following samples:</p>
<table>
<colgroup>
<col style="width: 35%" />
<col style="width: 64%" />
</colgroup>
<thead>
<tr class="header">
<th>Sample</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><code>100-jpeg-exif</code></td>
<td>Acquire data from 4 JPEG encoded data streams and produce EXIF
files</td>
</tr>
<tr class="even">
<td><code>200-jpeg-preview-exif</code></td>
<td>Acquire data from 4 Preview and 4 JPEG encoded data streams and
produce EXIF files with thumbnails</td>
</tr>
</tbody>
</table>
<h2 id="egrabber-python-sample-programs">eGrabber Python sample
programs</h2>
<table>
<colgroup>
<col style="width: 35%" />
<col style="width: 64%" />
</colgroup>
<thead>
<tr class="header">
<th>Sample</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><code>100-grabn</code></td>
<td>Simple Grab N using ‘with Buffer’</td>
</tr>
<tr class="even">
<td><code>120-converter</code></td>
<td>Python version of the C++ 120-converter eGrabber sample program</td>
</tr>
<tr class="odd">
<td><code>130-using-buffer</code></td>
<td>Simple Grab N with manual buffer management</td>
</tr>
<tr class="even">
<td><code>140-genapi-command</code></td>
<td>Queries on GenApi commands</td>
</tr>
<tr class="odd">
<td><code>150-discover</code></td>
<td>Discover and create eGrabbers or cameras with EGrabberDiscovery</td>
</tr>
<tr class="even">
<td><code>200-grabn-callbacks</code></td>
<td>Grab N frames and get DataStream events with callbacks</td>
</tr>
<tr class="odd">
<td><code>201-grabn-pop-oneof</code></td>
<td>Grab N frames and get DataStream events with pop_one_of</td>
</tr>
<tr class="even">
<td><code>210-show-all-grabbers</code></td>
<td>Show available grabbers</td>
</tr>
<tr class="odd">
<td><code>240-user-memory</code></td>
<td>Grab into user allocated buffer</td>
</tr>
<tr class="even">
<td><code>300-events-mt</code></td>
<td>Grab frames for a few seconds and get DataStream events with
callbacks, processing them in a separate thread</td>
</tr>
<tr class="odd">
<td><code>310-high-frame-rate</code></td>
<td>Grab in high frame rate mode for 10 seconds</td>
</tr>
<tr class="even">
<td><code>display-all-buffers</code></td>
<td>Image acquisition and display</td>
</tr>
<tr class="odd">
<td><code>display-all-buffers-capture-opencv</code></td>
<td>Acquire and convert frames to RGB8 to produce an avi file with
opencv and numpy</td>
</tr>
<tr class="even">
<td><code>display-all-buffers-numpy-opencv</code></td>
<td>Create numpy arrays from acquired Mono8 data, transpose arrays and
use opencv to show images</td>
</tr>
<tr class="odd">
<td><code>display-all-buffers-tkinter-pillow</code></td>
<td>Simple tkinter application showing acquired data processed by a
Pillow contour filter</td>
</tr>
<tr class="even">
<td><code>display-latest-buffer</code></td>
<td>Image acquisition and display. When the acquisition is faster than
the display processing, buffers are discarded</td>
</tr>
</tbody>
</table>
<h2 id="egrabber-recorder-python-sample-programs">eGrabber Recorder
Python sample programs</h2>
<table>
<colgroup>
<col style="width: 35%" />
<col style="width: 64%" />
</colgroup>
<thead>
<tr class="header">
<th>Sample</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td><code>260-recorder-read-write</code></td>
<td>Write/Read buffers to/from a Recorder container</td>
</tr>
<tr class="even">
<td><code>261-recorder-parameters</code></td>
<td>Show Recorder parameters</td>
</tr>
<tr class="odd">
<td><code>262-recorder-export</code></td>
<td>Export images from the container created by sample260.py to an MKV
file, and then use opencv to read the MKV file and display the
images</td>
</tr>
<tr class="even">
<td><code>360-recorder-write-with-callback</code></td>
<td>Write to a Recorder container using EGrabber callback</td>
</tr>
</tbody>
</table>
<h2 id="egrabber-recorder-c-sample-programs">eGrabber Recorder C# sample
programs</h2>
<table>
<colgroup>
<col style="width: 35%" />
<col style="width: 64%" />
</colgroup>
<thead>
<tr class="header">
<th>Sample</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>﻿<code>260-recorder-read-write</code></td>
<td>Write/Read buffers to/from a Recorder container</td>
</tr>
<tr class="even">
<td>﻿<code>261-recorder-parameters</code></td>
<td>Show Recorder parameters</td>
</tr>
</tbody>
</table>
<aside id="footnotes" class="footnotes footnotes-end-of-document" role="doc-endnotes">
<hr />
<ol>
<li id="fn1"><p><code>PC4400 eGrabber Gigelink</code> or
<code>PC4400-EV eGrabber Gigelink (30-day evaluation)</code>.<a href="#fnref1" class="footnote-back" role="doc-backlink">↩︎</a></p></li>
<li id="fn2"><p><code>PC4401 eGrabber Recorder and Playlink</code> or
<code>PC4401-EV eGrabber Recorder and Playlink (30-day evaluation)</code>.<a href="#fnref2" class="footnote-back" role="doc-backlink">↩︎</a></p></li>
</ol>
</aside>
<div style="width: 100%; left: 0; position: absolute; text-align: right;">
    <hr style="border: none; border-top: 1px solid #C4CFE5;">
    <address style="font-style: normal; color: #2A3D61;">
        <small>© <a href="http://www.euresys.com">EURESYS S.A.</a> - Subject to change without notice.</small>
    </address>
</div>
</body>
</html>
