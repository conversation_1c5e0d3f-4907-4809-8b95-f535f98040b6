require('./config-sc.js');
var g = grabbers[0];
var iid = g.InterfacePort.get('InterfaceID');

function setupBayer(g) {
    var fmt = g.RemotePort.get("PixelFormat");
    if (!/Bayer..8/.test(fmt)) {
    	var bayerFormats = g.RemotePort.$ee('PixelFormat').filter(/Bayer..8/.test);
        if (bayerFormats.length) {
            g.RemotePort.set('PixelFormat', bayerFormats[0]);
        } else {
            throw 'No Bayer format available on remote device';
        }
    }
}

if (!/JPEG/.test(iid)) {
    throw 'This sample is not compatible with this interface';
}

setupBayer(g);
if (!g.StreamPort.available('JpegQuality')) {
    g.StreamPort.set("BayerMethod", "Advanced");
    g.StreamPort.set("ImageScaling", "Scaling_1_8");
}
