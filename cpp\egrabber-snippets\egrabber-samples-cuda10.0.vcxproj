﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2416A39C-2B1B-4349-BEDA-D1DBFFCBCF9E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>egrabbersamplescuda70</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 10.0.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v10.0\include;C:\Program Files\Euresys\eGrabber\include;C:\Program Files (x86)\Euresys\eGrabber\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>cuda.lib;cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v10.0\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v10.0\include;C:\Program Files\Euresys\eGrabber\include;C:\Program Files (x86)\Euresys\eGrabber\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>cuda.lib;cudart.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v10.0\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="samples\100-grabn.cpp" />
    <ClCompile Include="samples\101-singleframe.cpp" />
    <ClCompile Include="samples\102-action-grab.cpp" />
    <ClCompile Include="samples\105-area-scan-grabn.cpp" />
    <ClCompile Include="samples\106-line-scan-grabn.cpp" />
    <ClCompile Include="samples\110-get-string-list.cpp" />
    <ClCompile Include="samples\120-converter.cpp" />
    <ClCompile Include="samples\130-using-buffer.cpp" />
    <ClCompile Include="samples\140-genapi-command.cpp" />
    <ClCompile Include="samples\150-discover.cpp" />
    <ClCompile Include="samples\200-grabn-callbacks.cpp" />
    <ClCompile Include="samples\201-grabn-pop-oneof.cpp" />
    <ClCompile Include="samples\210-show-all-grabbers.cpp" />
    <ClCompile Include="samples\211-show-all-grabbers-ro.cpp" />
    <ClCompile Include="samples\212-create-all-grabbers.cpp" />
    <ClCompile Include="samples\213-egrabbers.cpp" />
    <ClCompile Include="samples\220-get-announced-handles.cpp" />
    <ClCompile Include="samples\221-queue-buffer-ranges.cpp" />
    <ClCompile Include="samples\230-script-vars.cpp" />
    <ClCompile Include="samples\231-script-var.cpp" />
    <ClCompile Include="samples\240-user-memory.cpp" />
    <ClCompile Include="samples\241-multi-part.cpp" />
    <ClCompile Include="samples\250-using-lut.cpp" />
    <ClCompile Include="samples\260-recorder-read-write.cpp" />
    <ClCompile Include="samples\261-recorder-parameters.cpp" />
    <ClCompile Include="samples\270-multicast-master.cpp" />
    <ClCompile Include="samples\271-multicast-receiver.cpp" />
    <ClCompile Include="samples\300-events-mt-cic.cpp" />
    <ClCompile Include="samples\301-events-st-all.cpp" />
    <ClCompile Include="samples\302-cxp-connector-detection.cpp" />
    <ClCompile Include="samples\310-high-frame-rate.cpp" />
    <ClCompile Include="samples\311-high-frame-rate.cpp" />
    <ClCompile Include="samples\312-part-timestamps.cpp" />
    <ClCompile Include="samples\320-cl-serial-cli.cpp" />
    <ClCompile Include="samples\321-gencp-serial.cpp" />
    <ClCompile Include="samples\330-metadata-insertion.cpp" />
    <ClCompile Include="samples\340-dma-roi.cpp" />
    <ClCompile Include="samples\341-dma-deinterlace.cpp" />
    <ClCompile Include="samples\342-dma-roi-deinterlace.cpp" />
    <ClCompile Include="samples\500-grabn-cuda-process.cpp" />
    <ClCompile Include="samples\501-all-grabbers-cuda-process.cpp" />
    <ClCompile Include="samples\502-grabn-cuda-copy-and-process.cpp" />
    <ClCompile Include="samples\600-thread-start-stop-callbacks.cpp" />
    <ClCompile Include="samples\610-line-scan-array.cpp" />
    <ClCompile Include="samples\620-multiple-camera.cpp" />
    <ClCompile Include="samples\650-multistream.cpp" />
    <ClCompile Include="samples\700-memento.cpp" />
    <ClCompile Include="samples\800-process-latest-buffer.cpp" />
    <ClCompile Include="tools\main.cpp" />
    <ClCompile Include="tools\tools.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="samples\101-singleframe.setup.js" />
    <None Include="samples\102-action-grab.setup.js" />
    <None Include="samples\200-grabn-callbacks.setup.js" />
    <None Include="samples\200-grabn-callbacks.teardown.js" />
    <None Include="samples\201-grabn-pop-oneof.setup.js" />
    <None Include="samples\201-grabn-pop-oneof.teardown.js" />
    <None Include="samples\210-show-all-grabbers.show.js" />
    <None Include="samples\211-show-all-grabbers-ro.show.js" />
    <None Include="samples\212-create-all-grabbers.setup.js" />
    <None Include="samples\213-egrabbers.setup.js" />
    <None Include="samples\221-queue-buffer-ranges.bufferSet1.js" />
    <None Include="samples\221-queue-buffer-ranges.bufferSet2.js" />
    <None Include="samples\231-script-var.greetingsFromCpp.js" />
    <None Include="samples\231-script-var.numbersFromScript.js" />
    <None Include="samples\250-using-lut.userLUT.js" />
    <None Include="samples\270-multicast-master.setup.js" />
    <None Include="samples\271-multicast-receiver.setup.js" />
    <None Include="samples\300-events-mt-cic.setup.js" />
    <None Include="samples\300-events-mt-cic.teardown.js" />
    <None Include="samples\301-events-st-all.setup.js" />
    <None Include="samples\301-events-st-all.teardown.js" />
    <None Include="samples\302-cxp-connector-detection.setup.js" />
    <None Include="samples\302-cxp-connector-detection.teardown.js" />
    <None Include="samples\330-metadata-insertion.setup.js" />
    <None Include="samples\330-metadata-insertion.teardown.js" />
    <None Include="samples\500-grabn-cuda-process.setup.js" />
    <None Include="samples\501-all-grabbers-cuda-process.setup.js" />
    <None Include="samples\502-grabn-cuda-copy-and-process.setup.js" />
    <None Include="samples\600-thread-start-stop-callbacks.setup.js" />
    <None Include="samples\600-thread-start-stop-callbacks.teardown.js" />
    <None Include="samples\610-line-scan-array.setup.js" />
    <None Include="samples\610-line-scan-array.teardown.js" />
    <None Include="samples\650-multistream.setup.js" />
    <None Include="samples\config-rg.js" />
    <None Include="samples\config-sc.js" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="samples\500-grabn-cuda-process.h" />
    <ClInclude Include="samples\501-all-grabbers-cuda-process.h" />
    <ClInclude Include="samples\502-grabn-cuda-copy-and-process.h" />
    <ClInclude Include="tools\tools.h" />
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="samples\500-grabn-cuda-process.cu" />
    <CudaCompile Include="samples\501-all-grabbers-cuda-process.cu" />
    <CudaCompile Include="samples\502-grabn-cuda-copy-and-process.cu" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 10.0.targets" />
  </ImportGroup>
</Project>
