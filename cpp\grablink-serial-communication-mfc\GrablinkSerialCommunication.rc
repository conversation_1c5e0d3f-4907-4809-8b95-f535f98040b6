// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#ifndef APSTUDIO_INVOKED
#include "targetver.h"
#endif
#include "afxres.h"
#include "verrsrc.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (United States) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOGEX 0, 0, 170, 62
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About GrablinkSerialCommunication"
FONT 8, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,14,14,20,20
    LTEXT           "GrablinkSerialCommunication, Version 1.0",IDC_STATIC,42,14,114,8,SS_NOPREFIX
    LTEXT           "Copyright (C) EURESYS 2021",IDC_STATIC,42,26,114,8
    DEFPUSHBUTTON   "OK",IDOK,113,41,50,14,WS_GROUP
END

IDD_GRABLINKSERIALCOMMUNICATION_DIALOG DIALOGEX 0, 0, 321, 139
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_VISIBLE | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
EXSTYLE WS_EX_APPWINDOW
CAPTION "GrablinkSerialCommunication"
FONT 8, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    LTEXT           "Available ports",IDC_STATIC,16,11,48,8
    COMBOBOX        IDC_COMBO_PORTS,14,22,158,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    GROUPBOX        "Send command",IDC_STATIC,14,71,158,54
    EDITTEXT        IDC_SENDCOMMAND,19,84,146,14,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "Send",IDC_SENDBUTTON,115,105,50,14
    PUSHBUTTON      "Open Port",IDC_OPENBUTTON,187,21,50,14
    PUSHBUTTON      "Close Port",IDC_CLOSEBUTTON,255,21,50,14
    GROUPBOX        "Read Message",IDC_STATIC,187,40,118,85
    EDITTEXT        IDC_READMESSAGE,192,53,108,46,ES_MULTILINE | ES_READONLY | WS_VSCROLL
    PUSHBUTTON      "Read",IDC_READBUTTON,250,105,50,14
    COMBOBOX        IDC_COMBO_BAUDRATES,14,53,158,30,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Supported baud rates",IDC_STATIC,15,41,79,8
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "EURESYS"
            VALUE "FileDescription", "GrablinkSerialCommunication"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "GrablinkSerialCommunication.exe"
            VALUE "LegalCopyright", "Copyright (c) EURESYS 2021"
            VALUE "OriginalFilename", "GrablinkSerialCommunication.exe"
            VALUE "ProductName", "eGrabber"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 163
        TOPMARGIN, 7
        BOTTOMMARGIN, 55
    END

    IDD_GRABLINKSERIALCOMMUNICATION_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 314
        TOPMARGIN, 7
        BOTTOMMARGIN, 131
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

IDD_GRABLINKSERIALCOMMUNICATION_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_ABOUTBOX            "&About GrablinkSerialCommunication..."
END

#endif    // English (United States) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// French (Belgium) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_FRB)
LANGUAGE LANG_FRENCH, SUBLANG_FRENCH_BELGIAN

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#ifndef APSTUDIO_INVOKED\r\n"
    "#include ""targetver.h""\r\n"
    "#endif\r\n"
    "#include ""afxres.h""\r\n"
    "#include ""verrsrc.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "LANGUAGE 9, 1\r\n"
    "#include ""res\\GrablinkSerialCommunication.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""afxres.rc""      // Standard components\r\n"
    "#if !defined(_AFXDLL)\r\n"
    "#include ""afxribbon.rc""   // MFC ribbon and control bar resources\r\n"
    "#endif\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON                    "res\\GrablinkSerialCommunication.ico"

#endif    // French (Belgium) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE 9, 1
#include "res\GrablinkSerialCommunication.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"      // Standard components
#if !defined(_AFXDLL)
#include "afxribbon.rc"   // MFC ribbon and control bar resources
#endif
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

