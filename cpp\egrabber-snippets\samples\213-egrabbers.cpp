#include "../tools/tools.h"

#include <EGrabbers.h>

using namespace Euresys;

namespace {

    class MyGrabber: public EGrabberCallbackSingleThread {
        public:
            MyGrabber(EGenTL &gentl, int interfaceIndex, int deviceIndex, int streamIndex,
                      gc::DEVICE_ACCESS_FLAGS deviceOpenFlags, bool remoteRequired)
            : EGrabberCallbackSingleThread(gentl, interfaceIndex, deviceIndex, streamIndex, deviceOpenFlags, remoteRequired)
            , interfaceIndex(interfaceIndex)
            , deviceIndex(deviceIndex)
            , streamIndex(streamIndex)
            {
                Tools::log("Creating MyGrabber(" +
                    Tools::toString(interfaceIndex) + "," +
                    Tools::toString(deviceIndex) + "," +
                    Tools::toString(streamIndex) + ")");
            }
            ~MyGrabber()
            {
                Tools::log("Destroying MyGrabber(" +
                    Tools::toString(interfaceIndex) + "," +
                    Tools::toString(deviceIndex) + "," +
                    Tools::toString(streamIndex) + ")");
                shutdown();
            }
        private:
            int interfaceIndex;
            int deviceIndex;
            int streamIndex;

            virtual void onNewBufferEvent(const NewBufferData& data)
            {
                ScopedBuffer buffer(*this, data);
                Tools::log("Getting image buffer " +
                    Tools::toString(buffer.getInfo<void *>(gc::BUFFER_INFO_BASE)) + " on MyGrabber(" +
                    Tools::toString(interfaceIndex) + "," +
                    Tools::toString(deviceIndex) + "," +
                    Tools::toString(streamIndex) + ")");
            }
    };

};

static void sample() {
    // collect all available grabbers in the "grabbers" instance
    EGenTL genTL;
    EGrabbers<MyGrabber> grabbers(genTL);

    // access available grabbers as a flat structure
    for (size_t i = 0; i < grabbers.length(); ++i) {
        if (grabbers[i]) {
            Tools::log("Configuring " + grabbers[i].getSdi());
            grabbers[i]->runScript(Tools::getSampleFilePath("213-egrabbers.setup.js"));
        }
    }

    // another possibility is walking the GenTL modules tree structure
    for (size_t i = 0; i < grabbers.root.length(); ++i) {
        for (size_t d = 0; d < grabbers.root[i].length(); ++d) {
            for (size_t s = 0; s < grabbers.root[i][d].length(); ++s) {
                if (grabbers.root[i][d][s]) {
                    if (grabbers.root[i][d][s]->isOpen<RemoteModule>()) {
                        Tools::log("Allocating buffers on " + grabbers.root[i][d][s].getSdi());
                        grabbers.root[i][d][s]->reallocBuffers(3);
                    } else {
                        Tools::log("No remote device on " + grabbers.root[i][d][s].getSdi());
                    }
                }
            }
        }
    }

    // walk the tree structure of the grabbers to acquire one frame
    for (size_t i = 0; i < grabbers.root.length(); ++i) {
        for (size_t d = 0; d < grabbers.root[i].length(); ++d) {
            // indexes are optional (and default to 0), for instance the following
            // code omits the stream index and is equivalent to grabbers.root[i][d][0]
            if (grabbers.root[i][d]) {
                if (grabbers.root[i][d]->isOpen<RemoteModule>()) {
                    Tools::log("Starting " + grabbers.root[i][d].getSdi());
                    grabbers.root[i][d]->start(1);
                }
            }
        }
    }

    Tools::sleepMs(2000);
    // the destructor of "grabbers" will cleanup (stop and delete) the grabbers
}

static Tools::DeprecatedSample addSample(__FILE__, sample, "Use available grabbers with EGrabbers\n"
    "Please consider using EGrabberDiscovery instead of EGrabbers (cf. 150-discover)");
