﻿<Window x:Class="EGrabberWPF.EGrabberWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="EGrabberWindow" Name="EGrabberChildWindow" Width="Auto" Height="Auto">
    <DockPanel Background="Black">
        <StatusBar Name="EGrabberStatusBar" DockPanel.Dock="Bottom">
            <StatusBar.ItemsPanel>
                <ItemsPanelTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                    </Grid>
                </ItemsPanelTemplate>
            </StatusBar.ItemsPanel>
            <StatusBarItem Grid.Column="0">
                <Label Content="PixelFormat:"/>
            </StatusBarItem>
            <StatusBarItem Grid.Column="1">
                <TextBlock Name="statusPixelFormat">-</TextBlock>
            </StatusBarItem>
            <Separator Grid.Column="2" />
            <StatusBarItem Grid.Column="3">
                <Label Content="Resolution:"/>
            </StatusBarItem>
            <StatusBarItem Grid.Column="4">
                <TextBlock Name="statusResolution">-</TextBlock>
            </StatusBarItem>
            <Separator Grid.Column="5" />
            <StatusBarItem Grid.Column="6">
                <Label Content="Frame Rate:"/>
            </StatusBarItem>
            <StatusBarItem Grid.Column="7">
                <TextBlock  Name="statusFrameRate">-</TextBlock>
            </StatusBarItem>
            <StatusBarItem Grid.Column="8" />
        </StatusBar>
        <Image x:Name="EGrabberImage" />
    </DockPanel>
</Window>
