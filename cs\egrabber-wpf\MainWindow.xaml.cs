﻿using System;
using System.Windows;

namespace EGrabberWPF
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    /// 

    public partial class MainWindow : Window
    {
        private readonly EGrabberWindow myEGrabberWin;

        public MainWindow()
        {
            InitializeComponent();
            try
            {
                myEGrabberWin = new EGrabberWindow();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.Message, "Grabber Initialization Error");
            }
        }

        private void Button_Start(object sender, RoutedEventArgs e)
        {
            if (myEGrabberWin != null)
            {
                myEGrabberWin.StartStream();
                myEGrabberWin.Show();
            }
        }

        private void Button_Stop(object sender, RoutedEventArgs e)
        {
            if (myEGrabberWin != null)
            {
                myEGrabberWin.StopStream();
            }
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            grabberInfo.Text = myEGrabberWin == null ? "<no grabber>" : myEGrabberWin.GetGrabberInfo();
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (myEGrabberWin != null)
            {
                myEGrabberWin.StopStream();
                myEGrabberWin.DisposeUnmanagedResources();
            }
        }
    }
}
