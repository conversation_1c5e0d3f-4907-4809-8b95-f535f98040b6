# Polish translation for libexif.
# Copyright (C) 2006, 2007, 2008, 2009, 2010 Free Software Foundation, Inc.
# This file is distributed under the same license as the libexif package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2005-2012.
#
msgid ""
msgstr ""
"Project-Id-Version: libexif 0.6.21-pre1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2012-07-12 20:41+0200\n"
"PO-Revision-Date: 2012-07-02 16:15+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: libexif/canon/mnote-canon-entry.c:40 libexif/fuji/mnote-fuji-entry.c:35
#: libexif/olympus/mnote-olympus-entry.c:37
#: libexif/pentax/mnote-pentax-entry.c:39
#, c-format
msgid "Invalid format '%s', expected '%s'."
msgstr "Błędny format '%s', oczekiwano '%s'."

#: libexif/canon/mnote-canon-entry.c:52 libexif/fuji/mnote-fuji-entry.c:47
#: libexif/olympus/mnote-olympus-entry.c:62
#: libexif/pentax/mnote-pentax-entry.c:51
#, c-format
msgid "Invalid number of components (%i, expected %i)."
msgstr "Błędna liczba składowych (%i, a oczekiwano %i)."

#: libexif/canon/mnote-canon-entry.c:61
#: libexif/olympus/mnote-olympus-entry.c:72
#: libexif/pentax/mnote-pentax-entry.c:61
#, c-format
msgid "Invalid number of components (%i, expected %i or %i)."
msgstr "Błędna liczba składowych (%i, a oczekiwano %i lub %i)."

#: libexif/canon/mnote-canon-entry.c:76 libexif/canon/mnote-canon-entry.c:130
#: libexif/canon/mnote-canon-entry.c:182 libexif/exif-entry.c:816
#: libexif/olympus/mnote-olympus-entry.c:199
#: libexif/olympus/mnote-olympus-tag.c:108
#: libexif/pentax/mnote-pentax-entry.c:174
#: libexif/pentax/mnote-pentax-entry.c:209
#: libexif/pentax/mnote-pentax-entry.c:297
msgid "Macro"
msgstr "Makro"

#: libexif/canon/mnote-canon-entry.c:77 libexif/canon/mnote-canon-entry.c:79
#: libexif/canon/mnote-canon-entry.c:157 libexif/canon/mnote-canon-entry.c:160
#: libexif/canon/mnote-canon-entry.c:163 libexif/exif-entry.c:694
#: libexif/exif-entry.c:697 libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/exif-entry.c:765 libexif/fuji/mnote-fuji-entry.c:64
#: libexif/olympus/mnote-olympus-entry.c:121
#: libexif/olympus/mnote-olympus-entry.c:198
#: libexif/olympus/mnote-olympus-entry.c:206
#: libexif/olympus/mnote-olympus-entry.c:216
#: libexif/olympus/mnote-olympus-entry.c:592
#: libexif/pentax/mnote-pentax-entry.c:105
#: libexif/pentax/mnote-pentax-entry.c:110
#: libexif/pentax/mnote-pentax-entry.c:115
#: libexif/pentax/mnote-pentax-entry.c:208
msgid "Normal"
msgstr "Standard"

#: libexif/canon/mnote-canon-entry.c:78
msgid "Economy"
msgstr "Ekonomiczna"

#: libexif/canon/mnote-canon-entry.c:80
msgid "Fine"
msgstr "Dobra"

#: libexif/canon/mnote-canon-entry.c:81 libexif/fuji/mnote-fuji-entry.c:178
#: libexif/pentax/mnote-pentax-entry.c:141
msgid "RAW"
msgstr "RAW"

#: libexif/canon/mnote-canon-entry.c:82
msgid "Superfine"
msgstr "Bardzo dobra"

#: libexif/canon/mnote-canon-entry.c:83 libexif/canon/mnote-canon-entry.c:304
#: libexif/canon/mnote-canon-entry.c:307 libexif/canon/mnote-canon-entry.c:315
#: libexif/canon/mnote-canon-entry.c:348 libexif/canon/mnote-canon-entry.c:360
#: libexif/canon/mnote-canon-entry.c:373 libexif/canon/mnote-canon-entry.c:375
#: libexif/canon/mnote-canon-entry.c:577 libexif/canon/mnote-canon-entry.c:674
#: libexif/fuji/mnote-fuji-entry.c:70 libexif/fuji/mnote-fuji-entry.c:103
#: libexif/fuji/mnote-fuji-entry.c:107 libexif/fuji/mnote-fuji-entry.c:115
#: libexif/fuji/mnote-fuji-entry.c:142
#: libexif/olympus/mnote-olympus-entry.c:181
#: libexif/olympus/mnote-olympus-entry.c:189
#: libexif/olympus/mnote-olympus-entry.c:254
#: libexif/olympus/mnote-olympus-entry.c:536
#: libexif/olympus/mnote-olympus-entry.c:553
#: libexif/pentax/mnote-pentax-entry.c:195
#: libexif/pentax/mnote-pentax-entry.c:260
msgid "Off"
msgstr "Wyłączony"

#: libexif/canon/mnote-canon-entry.c:84 libexif/canon/mnote-canon-entry.c:167
#: libexif/canon/mnote-canon-entry.c:180 libexif/canon/mnote-canon-entry.c:331
#: libexif/canon/mnote-canon-entry.c:403 libexif/fuji/mnote-fuji-entry.c:73
#: libexif/fuji/mnote-fuji-entry.c:101 libexif/fuji/mnote-fuji-entry.c:111
#: libexif/fuji/mnote-fuji-entry.c:119
#: libexif/olympus/mnote-olympus-entry.c:134
#: libexif/olympus/mnote-olympus-entry.c:186
#: libexif/olympus/mnote-olympus-entry.c:202
#: libexif/olympus/mnote-olympus-entry.c:247
#: libexif/pentax/mnote-pentax-entry.c:77
#: libexif/pentax/mnote-pentax-entry.c:88
#: libexif/pentax/mnote-pentax-entry.c:91
#: libexif/pentax/mnote-pentax-entry.c:97
#: libexif/pentax/mnote-pentax-entry.c:131
#: libexif/pentax/mnote-pentax-entry.c:229
#: libexif/pentax/mnote-pentax-entry.c:245
#: libexif/pentax/mnote-pentax-entry.c:290
msgid "Auto"
msgstr "Auto"

#: libexif/canon/mnote-canon-entry.c:85 libexif/canon/mnote-canon-entry.c:305
#: libexif/canon/mnote-canon-entry.c:350 libexif/canon/mnote-canon-entry.c:364
#: libexif/canon/mnote-canon-entry.c:374 libexif/fuji/mnote-fuji-entry.c:102
#: libexif/fuji/mnote-fuji-entry.c:108 libexif/fuji/mnote-fuji-entry.c:116
#: libexif/fuji/mnote-fuji-entry.c:143
#: libexif/olympus/mnote-olympus-entry.c:182
#: libexif/olympus/mnote-olympus-entry.c:539
#: libexif/olympus/mnote-olympus-entry.c:556
#: libexif/pentax/mnote-pentax-entry.c:198
#: libexif/pentax/mnote-pentax-entry.c:261
msgid "On"
msgstr "Włączony"

#: libexif/canon/mnote-canon-entry.c:86 libexif/fuji/mnote-fuji-entry.c:104
#: libexif/olympus/mnote-olympus-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:94
msgid "Red-eye reduction"
msgstr "Redukcja czerwonych oczu"

#: libexif/canon/mnote-canon-entry.c:87
msgid "Slow synchro"
msgstr "Powolna synchronizacja"

#: libexif/canon/mnote-canon-entry.c:88
msgid "Auto, red-eye reduction"
msgstr "Automatyczny, redukcja czerwonych oczu"

#: libexif/canon/mnote-canon-entry.c:89
#: libexif/pentax/mnote-pentax-entry.c:200
msgid "On, red-eye reduction"
msgstr "Włączony, redukcja czerwonych oczu"

#: libexif/canon/mnote-canon-entry.c:90
msgid "External flash"
msgstr "Zewnętrzny flesz"

#: libexif/canon/mnote-canon-entry.c:91 libexif/canon/mnote-canon-entry.c:101
#: libexif/canon/mnote-canon-entry.c:297
msgid "Single"
msgstr "Pojedynczy"

#: libexif/canon/mnote-canon-entry.c:92 libexif/canon/mnote-canon-entry.c:102
#: libexif/canon/mnote-canon-entry.c:298
msgid "Continuous"
msgstr "Ciągły"

#: libexif/canon/mnote-canon-entry.c:93
msgid "Movie"
msgstr "Film"

#: libexif/canon/mnote-canon-entry.c:94
msgid "Continuous, speed priority"
msgstr "Ciągły, priorytet szybkości"

#: libexif/canon/mnote-canon-entry.c:95
msgid "Continuous, low"
msgstr "Ciągły, niska"

#: libexif/canon/mnote-canon-entry.c:96
msgid "Continuous, high"
msgstr "Ciągły, wysoka"

#: libexif/canon/mnote-canon-entry.c:97
msgid "One-shot AF"
msgstr "AF One-Shot"

#: libexif/canon/mnote-canon-entry.c:98
msgid "AI servo AF"
msgstr "AF AI servo"

#: libexif/canon/mnote-canon-entry.c:99
msgid "AI focus AF"
msgstr "AF AI Focus"

#: libexif/canon/mnote-canon-entry.c:100 libexif/canon/mnote-canon-entry.c:103
msgid "Manual focus"
msgstr "Ręczna ogniskowa"

#: libexif/canon/mnote-canon-entry.c:104 libexif/canon/mnote-canon-entry.c:132
#: libexif/canon/mnote-canon-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:212
msgid "Pan focus"
msgstr "Pan focus"

#: libexif/canon/mnote-canon-entry.c:105
msgid "JPEG"
msgstr "JPEG"

#: libexif/canon/mnote-canon-entry.c:106
msgid "CRW+THM"
msgstr "CRW+THM"

#: libexif/canon/mnote-canon-entry.c:107
msgid "AVI+THM"
msgstr "AVI+THM"

#: libexif/canon/mnote-canon-entry.c:108
msgid "TIF"
msgstr "TIF"

#: libexif/canon/mnote-canon-entry.c:109
msgid "TIF+JPEG"
msgstr "TIF+JPEG"

#: libexif/canon/mnote-canon-entry.c:110
msgid "CR2"
msgstr "CR2"

#: libexif/canon/mnote-canon-entry.c:111
msgid "CR2+JPEG"
msgstr "CR2+JPEG"

#: libexif/canon/mnote-canon-entry.c:112
msgid "Large"
msgstr "Duży"

#: libexif/canon/mnote-canon-entry.c:113
msgid "Medium"
msgstr "Średni"

#: libexif/canon/mnote-canon-entry.c:114
msgid "Small"
msgstr "Mały"

#: libexif/canon/mnote-canon-entry.c:115
msgid "Medium 1"
msgstr "Średni 1"

#: libexif/canon/mnote-canon-entry.c:116
msgid "Medium 2"
msgstr "Średni 2"

#: libexif/canon/mnote-canon-entry.c:117
msgid "Medium 3"
msgstr "Średni 3"

#: libexif/canon/mnote-canon-entry.c:118
msgid "Postcard"
msgstr "Pocztówka"

#: libexif/canon/mnote-canon-entry.c:119
msgid "Widescreen"
msgstr "Szeroki ekran"

#: libexif/canon/mnote-canon-entry.c:120
msgid "Full auto"
msgstr "Pełny automat"

#: libexif/canon/mnote-canon-entry.c:121 libexif/canon/mnote-canon-entry.c:179
#: libexif/canon/mnote-canon-entry.c:201 libexif/canon/mnote-canon-entry.c:288
#: libexif/canon/mnote-canon-entry.c:395 libexif/exif-entry.c:764
#: libexif/fuji/mnote-fuji-entry.c:112
#: libexif/olympus/mnote-olympus-entry.c:93
#: libexif/olympus/mnote-olympus-entry.c:203
#: libexif/pentax/mnote-pentax-entry.c:79
#: libexif/pentax/mnote-pentax-entry.c:102
#: libexif/pentax/mnote-pentax-entry.c:133
#: libexif/pentax/mnote-pentax-entry.c:165
#: libexif/pentax/mnote-pentax-entry.c:211
#: libexif/pentax/mnote-pentax-entry.c:250
msgid "Manual"
msgstr "Ręczny"

#: libexif/canon/mnote-canon-entry.c:122 libexif/canon/mnote-canon-entry.c:433
#: libexif/exif-entry.c:691 libexif/exif-entry.c:775
#: libexif/fuji/mnote-fuji-entry.c:121 libexif/pentax/mnote-pentax-entry.c:167
#: libexif/pentax/mnote-pentax-entry.c:301
msgid "Landscape"
msgstr "Pejzaż"

#: libexif/canon/mnote-canon-entry.c:123
msgid "Fast shutter"
msgstr "Szybka migawka"

#: libexif/canon/mnote-canon-entry.c:124
msgid "Slow shutter"
msgstr "Wolna migawka"

#: libexif/canon/mnote-canon-entry.c:125 libexif/fuji/mnote-fuji-entry.c:123
#: libexif/olympus/mnote-olympus-entry.c:257
msgid "Night"
msgstr "Noc"

#: libexif/canon/mnote-canon-entry.c:126
msgid "Grayscale"
msgstr "Odcienie szarości"

#: libexif/canon/mnote-canon-entry.c:127 libexif/canon/mnote-canon-entry.c:311
#: libexif/pentax/mnote-pentax-entry.c:128
msgid "Sepia"
msgstr "Sepia"

#: libexif/canon/mnote-canon-entry.c:128 libexif/canon/mnote-canon-entry.c:432
#: libexif/exif-entry.c:691 libexif/exif-entry.c:773
#: libexif/fuji/mnote-fuji-entry.c:120 libexif/pentax/mnote-pentax-entry.c:166
#: libexif/pentax/mnote-pentax-entry.c:291
#: libexif/pentax/mnote-pentax-entry.c:294
#: libexif/pentax/mnote-pentax-entry.c:300
msgid "Portrait"
msgstr "Portret"

#: libexif/canon/mnote-canon-entry.c:129 libexif/fuji/mnote-fuji-entry.c:122
msgid "Sports"
msgstr "Sport"

#: libexif/canon/mnote-canon-entry.c:131 libexif/canon/mnote-canon-entry.c:312
#: libexif/canon/mnote-canon-entry.c:338 libexif/canon/mnote-canon-entry.c:410
#: libexif/fuji/mnote-fuji-entry.c:89 libexif/pentax/mnote-pentax-entry.c:127
msgid "Black & white"
msgstr "Czarno-białe"

#: libexif/canon/mnote-canon-entry.c:133 libexif/canon/mnote-canon-entry.c:308
msgid "Vivid"
msgstr "Ostre światło"

#: libexif/canon/mnote-canon-entry.c:134 libexif/canon/mnote-canon-entry.c:309
#: libexif/canon/mnote-canon-entry.c:434
msgid "Neutral"
msgstr "Neutralne"

#: libexif/canon/mnote-canon-entry.c:135
#: libexif/pentax/mnote-pentax-entry.c:93
msgid "Flash off"
msgstr "Flesz wyłączony"

#: libexif/canon/mnote-canon-entry.c:136
msgid "Long shutter"
msgstr "Długa migawka"

#: libexif/canon/mnote-canon-entry.c:137 libexif/canon/mnote-canon-entry.c:188
#: libexif/olympus/mnote-olympus-entry.c:174
msgid "Super macro"
msgstr "Super makro"

#: libexif/canon/mnote-canon-entry.c:138
msgid "Foliage"
msgstr "Liście"

#: libexif/canon/mnote-canon-entry.c:139
msgid "Indoor"
msgstr "Pomieszczenie"

#: libexif/canon/mnote-canon-entry.c:140 libexif/fuji/mnote-fuji-entry.c:135
#: libexif/pentax/mnote-pentax-entry.c:175
msgid "Fireworks"
msgstr "Fajerwerki"

#: libexif/canon/mnote-canon-entry.c:141 libexif/fuji/mnote-fuji-entry.c:133
msgid "Beach"
msgstr "Plaża"

#: libexif/canon/mnote-canon-entry.c:142 libexif/canon/mnote-canon-entry.c:347
#: libexif/canon/mnote-canon-entry.c:419 libexif/fuji/mnote-fuji-entry.c:136
#: libexif/pentax/mnote-pentax-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:292
#: libexif/pentax/mnote-pentax-entry.c:298
msgid "Underwater"
msgstr "Podwodne"

#: libexif/canon/mnote-canon-entry.c:143 libexif/fuji/mnote-fuji-entry.c:134
msgid "Snow"
msgstr "Śnieg"

#: libexif/canon/mnote-canon-entry.c:144
msgid "Kids & pets"
msgstr "Dzieci i zwierzęta"

#: libexif/canon/mnote-canon-entry.c:145
msgid "Night snapshot"
msgstr "Nocna migawka"

#: libexif/canon/mnote-canon-entry.c:146
msgid "Digital macro"
msgstr "Makro cyfrowe"

#: libexif/canon/mnote-canon-entry.c:147
msgid "My colors"
msgstr "Moje kolory"

#: libexif/canon/mnote-canon-entry.c:148
msgid "Still image"
msgstr "Obraz nieruchomy"

#: libexif/canon/mnote-canon-entry.c:149
msgid "Color accent"
msgstr "Kolorowy akcent"

#: libexif/canon/mnote-canon-entry.c:150
msgid "Color swap"
msgstr "Zamiana kolorów"

#: libexif/canon/mnote-canon-entry.c:151
msgid "Aquarium"
msgstr "Akwarium"

#: libexif/canon/mnote-canon-entry.c:152
msgid "ISO 3200"
msgstr "ISO 3200"

#: libexif/canon/mnote-canon-entry.c:153 libexif/canon/mnote-canon-entry.c:351
#: libexif/canon/mnote-canon-entry.c:368 libexif/canon/mnote-canon-entry.c:420
#: libexif/olympus/mnote-olympus-entry.c:192
#: libexif/olympus/mnote-olympus-entry.c:229
#: libexif/olympus/mnote-olympus-entry.c:457
#: libexif/pentax/mnote-pentax-entry.c:242
msgid "None"
msgstr "Brak"

#: libexif/canon/mnote-canon-entry.c:154
msgid "2x"
msgstr "2x"

#: libexif/canon/mnote-canon-entry.c:155
msgid "4x"
msgstr "4x"

#: libexif/canon/mnote-canon-entry.c:156 libexif/exif-entry.c:722
#: libexif/exif-entry.c:752
msgid "Other"
msgstr "Inny"

#: libexif/canon/mnote-canon-entry.c:158 libexif/canon/mnote-canon-entry.c:161
#: libexif/canon/mnote-canon-entry.c:164 libexif/canon/mnote-canon-entry.c:401
#: libexif/fuji/mnote-fuji-entry.c:86 libexif/pentax/mnote-pentax-entry.c:112
#: libexif/pentax/mnote-pentax-entry.c:117
msgid "High"
msgstr "Dużo"

#: libexif/canon/mnote-canon-entry.c:159 libexif/canon/mnote-canon-entry.c:162
#: libexif/canon/mnote-canon-entry.c:165 libexif/canon/mnote-canon-entry.c:399
#: libexif/pentax/mnote-pentax-entry.c:111
#: libexif/pentax/mnote-pentax-entry.c:116
msgid "Low"
msgstr "Mało"

#: libexif/canon/mnote-canon-entry.c:166
msgid "Auto high"
msgstr "Dużo auto"

#: libexif/canon/mnote-canon-entry.c:168
msgid "50"
msgstr "50"

#: libexif/canon/mnote-canon-entry.c:169
#: libexif/pentax/mnote-pentax-entry.c:120
#: libexif/pentax/mnote-pentax-entry.c:122
msgid "100"
msgstr "100"

#: libexif/canon/mnote-canon-entry.c:170
#: libexif/pentax/mnote-pentax-entry.c:121
#: libexif/pentax/mnote-pentax-entry.c:123
msgid "200"
msgstr "200"

#: libexif/canon/mnote-canon-entry.c:171
msgid "400"
msgstr "400"

#: libexif/canon/mnote-canon-entry.c:172
msgid "800"
msgstr "800"

#: libexif/canon/mnote-canon-entry.c:173
msgid "Default"
msgstr "Domyślny"

#: libexif/canon/mnote-canon-entry.c:174 libexif/exif-entry.c:718
msgid "Spot"
msgstr "Punktowy"

#: libexif/canon/mnote-canon-entry.c:175 libexif/exif-entry.c:716
msgid "Average"
msgstr "Średnia"

#: libexif/canon/mnote-canon-entry.c:176
msgid "Evaluative"
msgstr "Szacowany"

#: libexif/canon/mnote-canon-entry.c:177 libexif/exif-entry.c:721
msgid "Partial"
msgstr "Częściowy"

#: libexif/canon/mnote-canon-entry.c:178 libexif/exif-entry.c:717
msgid "Center-weighted average"
msgstr "Średnia centralnie ważona"

#: libexif/canon/mnote-canon-entry.c:181
msgid "Not known"
msgstr "Brak informacji"

#: libexif/canon/mnote-canon-entry.c:183
msgid "Very close"
msgstr "Bardzo blisko"

#: libexif/canon/mnote-canon-entry.c:184 libexif/exif-entry.c:817
msgid "Close"
msgstr "Blisko"

#: libexif/canon/mnote-canon-entry.c:185
msgid "Middle range"
msgstr "Średnio blisko"

#: libexif/canon/mnote-canon-entry.c:186
msgid "Far range"
msgstr "Daleko"

#: libexif/canon/mnote-canon-entry.c:189
#: libexif/pentax/mnote-pentax-entry.c:210
msgid "Infinity"
msgstr "Nieskończoność"

#: libexif/canon/mnote-canon-entry.c:190
msgid "Manual AF point selection"
msgstr "Ręczny wybór punktu AF"

#: libexif/canon/mnote-canon-entry.c:191 libexif/canon/mnote-canon-entry.c:352
msgid "None (MF)"
msgstr "Brak (MF)"

#: libexif/canon/mnote-canon-entry.c:192
msgid "Auto-selected"
msgstr "Automatycznie wybrany"

#: libexif/canon/mnote-canon-entry.c:193 libexif/canon/mnote-canon-entry.c:353
#: libexif/pentax/mnote-pentax-entry.c:224
#: libexif/pentax/mnote-pentax-entry.c:238
msgid "Right"
msgstr "Prawy"

#: libexif/canon/mnote-canon-entry.c:194 libexif/canon/mnote-canon-entry.c:354
#: libexif/pentax/mnote-pentax-entry.c:222
#: libexif/pentax/mnote-pentax-entry.c:237
msgid "Center"
msgstr "Środek"

#: libexif/canon/mnote-canon-entry.c:195 libexif/canon/mnote-canon-entry.c:356
#: libexif/pentax/mnote-pentax-entry.c:220
#: libexif/pentax/mnote-pentax-entry.c:236
msgid "Left"
msgstr "Lewy"

#: libexif/canon/mnote-canon-entry.c:196
msgid "Auto AF point selection"
msgstr "Automatyczny wybór punktu AF"

#: libexif/canon/mnote-canon-entry.c:197
msgid "Easy shooting"
msgstr "Łatwe robienie zdjęć"

#: libexif/canon/mnote-canon-entry.c:198
#: libexif/pentax/mnote-pentax-entry.c:163
msgid "Program"
msgstr "Program"

#: libexif/canon/mnote-canon-entry.c:199
msgid "Tv-priority"
msgstr "Priorytet Tv"

#: libexif/canon/mnote-canon-entry.c:200
msgid "Av-priority"
msgstr "Priorytet Av"

#: libexif/canon/mnote-canon-entry.c:202
msgid "A-DEP"
msgstr "A-DEP"

#: libexif/canon/mnote-canon-entry.c:203
msgid "M-DEP"
msgstr "M-DEP"

#: libexif/canon/mnote-canon-entry.c:204
msgid "Canon EF 50mm f/1.8"
msgstr "Canon EF 50mm f/1.8"

#: libexif/canon/mnote-canon-entry.c:205
msgid "Canon EF 28mm f/2.8"
msgstr "Canon EF 28mm f/2.8"

#: libexif/canon/mnote-canon-entry.c:206
msgid "Sigma UC Zoom 35-135mm f/4-5.6"
msgstr "Sigma UC Zoom 35-135mm f/4-5.6"

#: libexif/canon/mnote-canon-entry.c:207
msgid "Tokina AF193-2 19-35mm f/3.5-4.5"
msgstr "Tokina AF193-2 19-35mm f/3.5-4.5"

#: libexif/canon/mnote-canon-entry.c:208
msgid "Canon EF 100-300mm F5.6L"
msgstr "Canon EF 100-300mm F5.6L"

#: libexif/canon/mnote-canon-entry.c:209
msgid "Sigma 50mm f/2.8 EX or 28mm f/1.8"
msgstr "Sigma 50mm f/2.8 EX lub 28mm f/1.8"

#: libexif/canon/mnote-canon-entry.c:210
msgid "Canon EF 35mm f/2"
msgstr "Canon EF 35mm f/2"

#: libexif/canon/mnote-canon-entry.c:211
msgid "Canon EF 15mm f/2.8"
msgstr "Canon EF 15mm f/2.8"

#: libexif/canon/mnote-canon-entry.c:212
msgid "Canon EF 80-200mm f/2.8L"
msgstr "Canon EF 80-200mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:213
msgid "Tokina AT-X280AF PRO 28-80mm F2.8 Aspherical"
msgstr "Tokina AT-X280AF PRO 28-80mm F2.8 Aspherical"

#: libexif/canon/mnote-canon-entry.c:214
msgid "Cosina 100mm f/3.5 Macro AF"
msgstr "Cosina 100mm f/3.5 Macro AF"

#: libexif/canon/mnote-canon-entry.c:215
msgid "Tamron AF Aspherical 28-200mm f/3.8-5.6"
msgstr "Tamron AF Aspherical 28-200mm f/3.8-5.6"

#: libexif/canon/mnote-canon-entry.c:216
msgid "Canon EF 50mm f/1.8 MkII"
msgstr "Canon EF 50mm f/1.8 MkII"

#: libexif/canon/mnote-canon-entry.c:217
msgid "Tamron SP AF 300mm f/2.8 LD IF"
msgstr "Tamron SP AF 300mm f/2.8 LD IF"

#: libexif/canon/mnote-canon-entry.c:218
msgid "Canon EF 24mm f/2.8 or Sigma 15mm f/2.8 EX Fisheye"
msgstr "Canon EF 24mm f/2.8 lub Sigma 15mm f/2.8 EX Fisheye"

#: libexif/canon/mnote-canon-entry.c:219
msgid "Canon EF 35-80mm f/4-5.6"
msgstr "Canon EF 35-80mm f/4-5.6"

#: libexif/canon/mnote-canon-entry.c:220
msgid "Canon EF 75-300mm f/4-5.6"
msgstr "Canon EF 75-300mm f/4-5.6"

#: libexif/canon/mnote-canon-entry.c:221
msgid "Canon EF 28-80mm f/3.5-5.6"
msgstr "Canon EF 28-80mm f/3.5-5.6"

#: libexif/canon/mnote-canon-entry.c:222
msgid "Canon EF 28-105mm f/4-5.6"
msgstr "Canon EF 28-105mm f/4-5.6"

#: libexif/canon/mnote-canon-entry.c:223
msgid "Canon EF-S 18-55mm f/3.5-5.6"
msgstr "Canon EF-S 18-55mm f/3.5-5.6"

#: libexif/canon/mnote-canon-entry.c:224
msgid "Canon EF-S 18-55mm f/3.5-5.6 IS II"
msgstr "Canon EF-S 18-55mm f/3.5-5.6 IS II"

#: libexif/canon/mnote-canon-entry.c:225
msgid "Canon MP-E 65mm f/2.8 1-5x Macro Photo"
msgstr "Canon MP-E 65mm f/2.8 1-5x Macro Photo"

#: libexif/canon/mnote-canon-entry.c:226
msgid "Canon TS-E 24mm f/3.5L"
msgstr "Canon TS-E 24mm f/3.5L"

#: libexif/canon/mnote-canon-entry.c:227
msgid "Canon TS-E 45mm f/2.8"
msgstr "Canon TS-E 45mm f/2.8"

#: libexif/canon/mnote-canon-entry.c:228
msgid "Canon TS-E 90mm f/2.8"
msgstr "Canon TS-E 90mm f/2.8"

#: libexif/canon/mnote-canon-entry.c:229
msgid "Canon EF 50mm f/1.0L"
msgstr "Canon EF 50mm f/1.0L"

#: libexif/canon/mnote-canon-entry.c:230
msgid "Sigma 17-35mm f2.8-4 EX Aspherical HSM"
msgstr "Sigma 17-35mm f2.8-4 EX Aspherical HSM"

#: libexif/canon/mnote-canon-entry.c:231
msgid "Canon EF 600mm f/4L IS"
msgstr "Canon EF 600mm f/4L IS"

#: libexif/canon/mnote-canon-entry.c:232
msgid "Canon EF 200mm f/1.8L"
msgstr "Canon EF 200mm f/1.8L"

#: libexif/canon/mnote-canon-entry.c:233
msgid "Canon EF 300mm f/2.8L"
msgstr "Canon EF 300mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:234
msgid "Canon EF 85mm f/1.2L"
msgstr "Canon EF 85mm f/1.2L"

#: libexif/canon/mnote-canon-entry.c:235
msgid "Canon EF 400mm f/2.8L"
msgstr "Canon EF 400mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:236
msgid "Canon EF 500mm f/4.5L"
msgstr "Canon EF 500mm f/4.5L"

#: libexif/canon/mnote-canon-entry.c:237
msgid "Canon EF 300mm f/2.8L IS"
msgstr "Canon EF 300mm f/2.8L IS"

#: libexif/canon/mnote-canon-entry.c:238
msgid "Canon EF 500mm f/4L IS"
msgstr "Canon EF 500mm f/4L IS"

#: libexif/canon/mnote-canon-entry.c:239
msgid "Canon EF 100mm f/2"
msgstr "Canon EF 100mm f/2"

#: libexif/canon/mnote-canon-entry.c:240
msgid "Sigma 20mm EX f/1.8"
msgstr "Sigma 20mm EX f/1.8"

#: libexif/canon/mnote-canon-entry.c:241
msgid "Canon EF 200mm f/2.8L"
msgstr "Canon EF 200mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:242
msgid "Sigma 10-20mm F4-5.6 or 12-24mm f/4.5-5.6 or 14mm f/2.8"
msgstr "Sigma 10-20mm F4-5.6 lub 12-24mm f/4.5-5.6 lub 14mm f/2.8"

#: libexif/canon/mnote-canon-entry.c:243
msgid "Canon EF 35-350mm f/3.5-5.6L"
msgstr "Canon EF 35-350mm f/3.5-5.6L"

#: libexif/canon/mnote-canon-entry.c:244
msgid "Canon EF 85mm f/1.8 USM"
msgstr "Canon EF 85mm f/1.8 USM"

#: libexif/canon/mnote-canon-entry.c:245
msgid "Canon EF 28-105mm f/3.5-4.5 USM"
msgstr "Canon EF 28-105mm f/3.5-4.5 USM"

#: libexif/canon/mnote-canon-entry.c:246
msgid "Canon EF 20-35mm f/3.5-4.5 USM"
msgstr "Canon EF 20-35mm f/3.5-4.5 USM"

#: libexif/canon/mnote-canon-entry.c:247
msgid "Canon EF 28-70mm f/2.8L or Sigma 24-70mm EX f/2.8"
msgstr "Canon EF 28-70mm f/2.8L lub Sigma 24-70mm EX f/2.8"

#: libexif/canon/mnote-canon-entry.c:248
msgid "Canon EF 70-200mm f/2.8 L"
msgstr "Canon EF 70-200mm f/2.8 L"

#: libexif/canon/mnote-canon-entry.c:249
msgid "Canon EF 70-200mm f/2.8 L + x1.4"
msgstr "Canon EF 70-200mm f/2.8 L + x1.4"

#: libexif/canon/mnote-canon-entry.c:250
msgid "Canon EF 70-200mm f/2.8 L + x2"
msgstr "Canon EF 70-200mm f/2.8 L + x2"

#: libexif/canon/mnote-canon-entry.c:251
msgid "Canon EF 28mm f/1.8 USM"
msgstr "Canon EF 28mm f/1.8 USM"

#: libexif/canon/mnote-canon-entry.c:252
msgid "Sigma 15-30mm f/3.5-4.5 EX DG Aspherical"
msgstr "Sigma 15-30mm f/3.5-4.5 EX DG Aspherical"

#: libexif/canon/mnote-canon-entry.c:253
msgid "Canon EF 200mm f/2.8L II"
msgstr "Canon EF 200mm f/2.8L II"

#: libexif/canon/mnote-canon-entry.c:254
msgid "Canon EF 180mm Macro f/3.5L or Sigma 180mm EX HSM Macro f/3.5"
msgstr "Canon EF 180mm Macro f/3.5L lub Sigma 180mm EX HSM Macro f/3.5"

#: libexif/canon/mnote-canon-entry.c:255
msgid "Canon EF 135mm f/2L"
msgstr "Canon EF 135mm f/2L"

#: libexif/canon/mnote-canon-entry.c:256
msgid "Canon EF 24-85mm f/3.5-4.5 USM"
msgstr "Canon EF 24-85mm f/3.5-4.5 USM"

#: libexif/canon/mnote-canon-entry.c:257
msgid "Canon EF 300mm f/4L IS"
msgstr "Canon EF 300mm f/4L IS"

#: libexif/canon/mnote-canon-entry.c:258
msgid "Canon EF 28-135mm f/3.5-5.6 IS"
msgstr "Canon EF 28-135mm f/3.5-5.6 IS"

#: libexif/canon/mnote-canon-entry.c:259
msgid "Canon EF 35mm f/1.4L"
msgstr "Canon EF 35mm f/1.4L"

#: libexif/canon/mnote-canon-entry.c:260
msgid "Canon EF 100-400mm f/4.5-5.6L IS + x1.4"
msgstr "Canon EF 100-400mm f/4.5-5.6L IS + x1.4"

#: libexif/canon/mnote-canon-entry.c:261
msgid "Canon EF 100-400mm f/4.5-5.6L IS + x2"
msgstr "Canon EF 100-400mm f/4.5-5.6L IS + x2"

#: libexif/canon/mnote-canon-entry.c:262
msgid "Canon EF 100-400mm f/4.5-5.6L IS"
msgstr "Canon EF 100-400mm f/4.5-5.6L IS"

#: libexif/canon/mnote-canon-entry.c:263
msgid "Canon EF 400mm f/2.8L + x2"
msgstr "Canon EF 400mm f/2.8L + x2"

#: libexif/canon/mnote-canon-entry.c:264
msgid "Canon EF 70-200mm f/4L"
msgstr "Canon EF 70-200mm f/4L"

#: libexif/canon/mnote-canon-entry.c:265
msgid "Canon EF 100mm f/2.8 Macro"
msgstr "Canon EF 100mm f/2.8 Macro"

#: libexif/canon/mnote-canon-entry.c:266
msgid "Canon EF 400mm f/4 DO IS"
msgstr "Canon EF 400mm f/4 DO IS"

#: libexif/canon/mnote-canon-entry.c:267
msgid "Canon EF 75-300mm f/4-5.6 IS"
msgstr "Canon EF 75-300mm f/4-5.6 IS"

#: libexif/canon/mnote-canon-entry.c:268
msgid "Canon EF 50mm f/1.4"
msgstr "Canon EF 50mm f/1.4"

#: libexif/canon/mnote-canon-entry.c:269
msgid "Canon EF 28-80 f/3.5-5.6 USM IV"
msgstr "Canon EF 28-80 f/3.5-5.6 USM IV"

#: libexif/canon/mnote-canon-entry.c:270
msgid "Canon EF 28-200mm f/3.5-5.6"
msgstr "Canon EF 28-200mm f/3.5-5.6"

#: libexif/canon/mnote-canon-entry.c:271
msgid "Canon EF 90-300mm f/4.5-5.6"
msgstr "Canon EF 90-300mm f/4.5-5.6"

#: libexif/canon/mnote-canon-entry.c:272
msgid "Canon EF-S 18-55mm f/3.5-4.5 USM"
msgstr "Canon EF-S 18-55mm f/3.5-4.5 USM"

#: libexif/canon/mnote-canon-entry.c:273
msgid "Canon EF 70-200mm f/2.8L IS USM"
msgstr "Canon EF 70-200mm f/2.8L IS USM"

#: libexif/canon/mnote-canon-entry.c:274
msgid "Canon EF 70-200mm f/2.8L IS USM + x1.4"
msgstr "Canon EF 70-200mm f/2.8L IS USM + x1.4"

#: libexif/canon/mnote-canon-entry.c:275
msgid "Canon EF 70-200mm f/2.8L IS USM + x2"
msgstr "Canon EF 70-200mm f/2.8L IS USM + x2"

#: libexif/canon/mnote-canon-entry.c:276
msgid "Canon EF 16-35mm f/2.8L"
msgstr "Canon EF 16-35mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:277
msgid "Canon EF 24-70mm f/2.8L"
msgstr "Canon EF 24-70mm f/2.8L"

#: libexif/canon/mnote-canon-entry.c:278
msgid "Canon EF 17-40mm f/4L"
msgstr "Canon EF 17-40mm f/4L"

#: libexif/canon/mnote-canon-entry.c:279
msgid "Canon EF 70-300mm f/4.5-5.6 DO IS USM"
msgstr "Canon EF 70-300mm f/4.5-5.6 DO IS USM"

#: libexif/canon/mnote-canon-entry.c:280
msgid "Canon EF-S 17-85mm f4-5.6 IS USM"
msgstr "Canon EF-S 17-85mm f4-5.6 IS USM"

#: libexif/canon/mnote-canon-entry.c:281
msgid "Canon EF-S10-22mm F3.5-4.5 USM"
msgstr "Canon EF-S10-22mm F3.5-4.5 USM"

#: libexif/canon/mnote-canon-entry.c:282
msgid "Canon EF-S60mm F2.8 Macro USM"
msgstr "Canon EF-S60mm F2.8 Macro USM"

#: libexif/canon/mnote-canon-entry.c:283
msgid "Canon EF 24-105mm f/4L IS"
msgstr "Canon EF 24-105mm f/4L IS"

#: libexif/canon/mnote-canon-entry.c:284
msgid "Canon EF 70-300mm F4-5.6 IS USM"
msgstr "Canon EF 70-300mm F4-5.6 IS USM"

#: libexif/canon/mnote-canon-entry.c:285
msgid "Canon EF 50mm F1.2L USM"
msgstr "Canon EF 50mm F1.2L USM"

#: libexif/canon/mnote-canon-entry.c:286
msgid "Canon EF 70-200mm f/4L IS USM"
msgstr "Canon EF 70-200mm f/4L IS USM"

#: libexif/canon/mnote-canon-entry.c:287
msgid "Canon EF 70-200mm f/2.8L IS II USM"
msgstr "Canon EF 70-200mm f/2.8L IS II USM"

#: libexif/canon/mnote-canon-entry.c:289
msgid "TTL"
msgstr "TTL"

#: libexif/canon/mnote-canon-entry.c:290
msgid "A-TTL"
msgstr "A-TTL"

#: libexif/canon/mnote-canon-entry.c:291
msgid "E-TTL"
msgstr "E-TTL"

#: libexif/canon/mnote-canon-entry.c:292
msgid "FP sync enabled"
msgstr "Włączono FP sync"

#: libexif/canon/mnote-canon-entry.c:293
msgid "2nd-curtain sync used"
msgstr "Użyto 2nd-curtain sync"

#: libexif/canon/mnote-canon-entry.c:294
msgid "FP sync used"
msgstr "Użyto FP sync"

#: libexif/canon/mnote-canon-entry.c:295
#: libexif/olympus/mnote-olympus-entry.c:193
msgid "Internal"
msgstr "Wewnętrzny"

#: libexif/canon/mnote-canon-entry.c:296
#: libexif/olympus/mnote-olympus-entry.c:194
msgid "External"
msgstr "Zewnętrzny"

#: libexif/canon/mnote-canon-entry.c:299
msgid "Normal AE"
msgstr "Normalna AE"

#: libexif/canon/mnote-canon-entry.c:300
msgid "Exposure compensation"
msgstr "Kompensacja ekspozycji"

#: libexif/canon/mnote-canon-entry.c:301
msgid "AE lock"
msgstr "Blokada AE"

#: libexif/canon/mnote-canon-entry.c:302
msgid "AE lock + exposure compensation"
msgstr "Blokada AE + kompensacja ekspozycji"

#: libexif/canon/mnote-canon-entry.c:303
msgid "No AE"
msgstr "Brak AE"

#: libexif/canon/mnote-canon-entry.c:306
msgid "On, shot only"
msgstr "Włączona, tylko zdjęcie"

#: libexif/canon/mnote-canon-entry.c:310
msgid "Smooth"
msgstr "Płynny"

#: libexif/canon/mnote-canon-entry.c:313 libexif/canon/mnote-canon-entry.c:337
#: libexif/canon/mnote-canon-entry.c:396 libexif/canon/mnote-canon-entry.c:409
#: libexif/fuji/mnote-fuji-entry.c:81 libexif/pentax/mnote-pentax-entry.c:87
msgid "Custom"
msgstr "Własny"

#: libexif/canon/mnote-canon-entry.c:314
msgid "My color data"
msgstr "Moje dane kolorów"

#: libexif/canon/mnote-canon-entry.c:316 libexif/canon/mnote-canon-entry.c:378
#: libexif/pentax/mnote-pentax-entry.c:126
#: libexif/pentax/mnote-pentax-entry.c:145
msgid "Full"
msgstr "Pełny"

#: libexif/canon/mnote-canon-entry.c:317 libexif/canon/mnote-canon-entry.c:377
msgid "2/3"
msgstr "2/3"

#: libexif/canon/mnote-canon-entry.c:318 libexif/canon/mnote-canon-entry.c:376
msgid "1/3"
msgstr "1/3"

#: libexif/canon/mnote-canon-entry.c:324
msgid "Fixed"
msgstr "Stała"

#: libexif/canon/mnote-canon-entry.c:325 libexif/pentax/mnote-pentax-tag.c:44
msgid "Zoom"
msgstr "Powiększenie"

#: libexif/canon/mnote-canon-entry.c:332
msgid "Sunny"
msgstr "Słonecznie"

#: libexif/canon/mnote-canon-entry.c:333 libexif/canon/mnote-canon-entry.c:405
#: libexif/exif-entry.c:739 libexif/fuji/mnote-fuji-entry.c:75
#: libexif/olympus/mnote-olympus-entry.c:139
#: libexif/pentax/mnote-pentax-entry.c:255
msgid "Cloudy"
msgstr "Pochmurno"

#: libexif/canon/mnote-canon-entry.c:334 libexif/canon/mnote-canon-entry.c:406
#: libexif/exif-entry.c:736 libexif/pentax/mnote-pentax-entry.c:100
#: libexif/pentax/mnote-pentax-entry.c:249
msgid "Tungsten"
msgstr "Wolfram"

#: libexif/canon/mnote-canon-entry.c:335 libexif/canon/mnote-canon-entry.c:407
#: libexif/exif-entry.c:735 libexif/pentax/mnote-pentax-entry.c:101
#: libexif/pentax/mnote-pentax-entry.c:248
msgid "Fluorescent"
msgstr "Fluorescencja"

#: libexif/canon/mnote-canon-entry.c:336 libexif/canon/mnote-canon-entry.c:408
#: libexif/exif-entry.c:737 libexif/exif-entry.c:779 libexif/exif-tag.c:577
#: libexif/fuji/mnote-fuji-entry.c:80 libexif/pentax/mnote-pentax-entry.c:254
msgid "Flash"
msgstr "Flesz"

#: libexif/canon/mnote-canon-entry.c:339 libexif/canon/mnote-canon-entry.c:411
#: libexif/exif-entry.c:740 libexif/pentax/mnote-pentax-entry.c:99
#: libexif/pentax/mnote-pentax-entry.c:247
msgid "Shade"
msgstr "Cień"

#: libexif/canon/mnote-canon-entry.c:340 libexif/canon/mnote-canon-entry.c:412
msgid "Manual temperature (Kelvin)"
msgstr "Temperatura ręczna (w kelwinach)"

#: libexif/canon/mnote-canon-entry.c:341 libexif/canon/mnote-canon-entry.c:413
msgid "PC set 1"
msgstr "Ustawienie PC 1"

#: libexif/canon/mnote-canon-entry.c:342 libexif/canon/mnote-canon-entry.c:414
msgid "PC set 2"
msgstr "Ustawienie PC 2"

#: libexif/canon/mnote-canon-entry.c:343 libexif/canon/mnote-canon-entry.c:415
msgid "PC set 3"
msgstr "Ustawienie PC 3"

#: libexif/canon/mnote-canon-entry.c:344 libexif/canon/mnote-canon-entry.c:416
#: libexif/exif-entry.c:741 libexif/fuji/mnote-fuji-entry.c:76
#: libexif/pentax/mnote-pentax-entry.c:251
msgid "Daylight fluorescent"
msgstr "Fluorescencyjne światło dzienne"

#: libexif/canon/mnote-canon-entry.c:345 libexif/canon/mnote-canon-entry.c:417
msgid "Custom 1"
msgstr "Własne 1"

#: libexif/canon/mnote-canon-entry.c:346 libexif/canon/mnote-canon-entry.c:418
msgid "Custom 2"
msgstr "Własne 2"

#: libexif/canon/mnote-canon-entry.c:349 libexif/exif-entry.c:692
#: libexif/pentax/mnote-pentax-entry.c:78
#: libexif/pentax/mnote-pentax-entry.c:132
#: libexif/pentax/mnote-pentax-entry.c:169
#: libexif/pentax/mnote-pentax-entry.c:295
msgid "Night scene"
msgstr "Scena nocna"

#: libexif/canon/mnote-canon-entry.c:355
msgid "Center-right"
msgstr "Środkowy i prawy"

#: libexif/canon/mnote-canon-entry.c:357
msgid "Left-right"
msgstr "Lewy i prawy"

#: libexif/canon/mnote-canon-entry.c:358
msgid "Left-center"
msgstr "Lewy i środkowy"

#: libexif/canon/mnote-canon-entry.c:359
msgid "All"
msgstr "Wszystkie"

#: libexif/canon/mnote-canon-entry.c:361
msgid "On (shot 1)"
msgstr "Włączony (zdjęcie 1)"

#: libexif/canon/mnote-canon-entry.c:362
msgid "On (shot 2)"
msgstr "Włączony (zdjęcie 2)"

#: libexif/canon/mnote-canon-entry.c:363
msgid "On (shot 3)"
msgstr "Włączony (zdjęcie 3)"

#: libexif/canon/mnote-canon-entry.c:365
msgid "EOS high-end"
msgstr "EOS wysokiej klasy"

#: libexif/canon/mnote-canon-entry.c:366
msgid "Compact"
msgstr "Kompakt"

#: libexif/canon/mnote-canon-entry.c:367
msgid "EOS mid-range"
msgstr "EOS średniej klasy"

#: libexif/canon/mnote-canon-entry.c:369
msgid "Rotate 90 CW"
msgstr "Obrót o 90 (zg.ze wsk.)"

#: libexif/canon/mnote-canon-entry.c:370
msgid "Rotate 180"
msgstr "Obrót o 180 stopni"

#: libexif/canon/mnote-canon-entry.c:371
msgid "Rotate 270 CW"
msgstr "Obrót o 270 (zg.ze wsk.)"

#: libexif/canon/mnote-canon-entry.c:372
msgid "Rotated by software"
msgstr "Obrót programowy"

#: libexif/canon/mnote-canon-entry.c:384
#: libexif/olympus/mnote-olympus-entry.c:612
msgid "Left to right"
msgstr "Od lewej do prawej"

#: libexif/canon/mnote-canon-entry.c:385
#: libexif/olympus/mnote-olympus-entry.c:615
msgid "Right to left"
msgstr "Od prawej do lewej"

#: libexif/canon/mnote-canon-entry.c:386
#: libexif/olympus/mnote-olympus-entry.c:618
msgid "Bottom to top"
msgstr "Od dołu do góry"

#: libexif/canon/mnote-canon-entry.c:387
#: libexif/olympus/mnote-olympus-entry.c:621
msgid "Top to bottom"
msgstr "Od góry do dołu"

#: libexif/canon/mnote-canon-entry.c:388
msgid "2x2 matrix (clockwise)"
msgstr "Macierz 2x2 (zgodnie ze wskazówkami)"

#: libexif/canon/mnote-canon-entry.c:394 libexif/canon/mnote-canon-entry.c:400
#: libexif/canon/mnote-canon-entry.c:421 libexif/canon/mnote-canon-entry.c:431
#: libexif/exif-entry.c:691 libexif/fuji/mnote-fuji-entry.c:84
#: libexif/fuji/mnote-fuji-entry.c:93 libexif/fuji/mnote-fuji-entry.c:163
#: libexif/olympus/mnote-olympus-entry.c:230
msgid "Standard"
msgstr "Standardowy"

#: libexif/canon/mnote-canon-entry.c:397
msgid "N/A"
msgstr "nd."

#: libexif/canon/mnote-canon-entry.c:398
msgid "Lowest"
msgstr "Najniższa"

#: libexif/canon/mnote-canon-entry.c:402
msgid "Highest"
msgstr "Najwyższa"

#: libexif/canon/mnote-canon-entry.c:404 libexif/exif-entry.c:734
#: libexif/fuji/mnote-fuji-entry.c:74
#: libexif/olympus/mnote-olympus-entry.c:136
#: libexif/pentax/mnote-pentax-entry.c:98
#: libexif/pentax/mnote-pentax-entry.c:246
msgid "Daylight"
msgstr "Światło dzienne"

#: libexif/canon/mnote-canon-entry.c:422
msgid "Set 1"
msgstr "Ustawienie 1"

#: libexif/canon/mnote-canon-entry.c:423
msgid "Set 2"
msgstr "Ustawienie 2"

#: libexif/canon/mnote-canon-entry.c:424
msgid "Set 3"
msgstr "Ustawienie 3"

#: libexif/canon/mnote-canon-entry.c:425
msgid "User def. 1"
msgstr "Użytkownika 1"

#: libexif/canon/mnote-canon-entry.c:426
msgid "User def. 2"
msgstr "Użytkownika 2"

#: libexif/canon/mnote-canon-entry.c:427
msgid "User def. 3"
msgstr "Użytkownika 3"

#: libexif/canon/mnote-canon-entry.c:428
msgid "External 1"
msgstr "Zewnętrzny 1"

#: libexif/canon/mnote-canon-entry.c:429
msgid "External 2"
msgstr "Zewnętrzny 2"

#: libexif/canon/mnote-canon-entry.c:430
msgid "External 3"
msgstr "Zewnętrzny 3"

#: libexif/canon/mnote-canon-entry.c:435
msgid "Faithful"
msgstr "Wierny"

#: libexif/canon/mnote-canon-entry.c:436
#: libexif/olympus/mnote-olympus-entry.c:118
msgid "Monochrome"
msgstr "Monochromatyczny"

#: libexif/canon/mnote-canon-entry.c:494
msgid ", "
msgstr ", "

#: libexif/canon/mnote-canon-entry.c:580 libexif/canon/mnote-canon-entry.c:677
#, c-format
msgid "%i (ms)"
msgstr "%i (ms)"

#: libexif/canon/mnote-canon-entry.c:624
#, c-format
msgid "%.2f mm"
msgstr "%.2f mm"

#: libexif/canon/mnote-canon-entry.c:648
#, c-format
msgid "%.2f EV"
msgstr "%.2f EV"

#: libexif/canon/mnote-canon-entry.c:658 libexif/exif-entry.c:1089
#, c-format
msgid "1/%i"
msgstr "1/%i"

#: libexif/canon/mnote-canon-entry.c:670
#, c-format
msgid "%u mm"
msgstr "%u mm"

#: libexif/canon/mnote-canon-tag.c:35
msgid "Settings (First Part)"
msgstr "Ustawienia (część pierwsza)"

#: libexif/canon/mnote-canon-tag.c:36 libexif/canon/mnote-canon-tag.c:92
#: libexif/exif-tag.c:581 libexif/pentax/mnote-pentax-tag.c:88
msgid "Focal Length"
msgstr "Ogniskowa"

#: libexif/canon/mnote-canon-tag.c:37
msgid "Settings (Second Part)"
msgstr "Ustawienia (część druga)"

#: libexif/canon/mnote-canon-tag.c:38
#: libexif/olympus/mnote-olympus-entry.c:601
#: libexif/pentax/mnote-pentax-entry.c:177
msgid "Panorama"
msgstr "Panorama"

#: libexif/canon/mnote-canon-tag.c:39
msgid "Image Type"
msgstr "Rodzaj obrazu"

#: libexif/canon/mnote-canon-tag.c:40 libexif/olympus/mnote-olympus-tag.c:37
#: libexif/olympus/mnote-olympus-tag.c:113
msgid "Firmware Version"
msgstr "Wersja firmware"

#: libexif/canon/mnote-canon-tag.c:41
msgid "Image Number"
msgstr "Numer zdjęcia"

#: libexif/canon/mnote-canon-tag.c:42
msgid "Owner Name"
msgstr "Nazwa właściciela"

#: libexif/canon/mnote-canon-tag.c:43
msgid "Color Information"
msgstr "Informacja o kolorach"

#: libexif/canon/mnote-canon-tag.c:44 libexif/fuji/mnote-fuji-tag.c:37
#: libexif/olympus/mnote-olympus-tag.c:81
#: libexif/olympus/mnote-olympus-tag.c:146
msgid "Serial Number"
msgstr "Numer seryjny"

#: libexif/canon/mnote-canon-tag.c:45
msgid "Custom Functions"
msgstr "Funkcje własne"

#: libexif/canon/mnote-canon-tag.c:56 libexif/fuji/mnote-fuji-tag.c:45
msgid "Macro Mode"
msgstr "Tryb makro"

#: libexif/canon/mnote-canon-tag.c:57 libexif/canon/mnote-canon-tag.c:117
#: libexif/olympus/mnote-olympus-tag.c:175
#: libexif/pentax/mnote-pentax-tag.c:128
msgid "Self-timer"
msgstr "Samowyzwalacz"

#: libexif/canon/mnote-canon-tag.c:58 libexif/fuji/mnote-fuji-tag.c:38
#: libexif/olympus/mnote-olympus-tag.c:40
#: libexif/olympus/mnote-olympus-tag.c:94
#: libexif/olympus/mnote-olympus-tag.c:107
msgid "Quality"
msgstr "Jakość"

#: libexif/canon/mnote-canon-tag.c:59 libexif/fuji/mnote-fuji-tag.c:43
#: libexif/olympus/mnote-olympus-tag.c:45
#: libexif/olympus/mnote-olympus-tag.c:127
#: libexif/pentax/mnote-pentax-tag.c:38 libexif/pentax/mnote-pentax-tag.c:73
msgid "Flash Mode"
msgstr "Tryb flesza"

#: libexif/canon/mnote-canon-tag.c:60 libexif/pentax/mnote-pentax-tag.c:101
msgid "Drive Mode"
msgstr "Tryb działania"

#: libexif/canon/mnote-canon-tag.c:61 libexif/canon/mnote-canon-tag.c:82
#: libexif/olympus/mnote-olympus-tag.c:43
#: libexif/olympus/mnote-olympus-tag.c:134
#: libexif/olympus/mnote-olympus-tag.c:173
#: libexif/pentax/mnote-pentax-tag.c:37 libexif/pentax/mnote-pentax-tag.c:74
#: libexif/pentax/mnote-pentax-tag.c:130
msgid "Focus Mode"
msgstr "Tryb ogniskowania"

#: libexif/canon/mnote-canon-tag.c:62 libexif/pentax/mnote-pentax-tag.c:127
msgid "Record Mode"
msgstr "Tryb nagrywania"

#: libexif/canon/mnote-canon-tag.c:63 libexif/pentax/mnote-pentax-tag.c:71
msgid "Image Size"
msgstr "Rozmiar obrazu"

#: libexif/canon/mnote-canon-tag.c:64
msgid "Easy Shooting Mode"
msgstr "Tryb łatwego robienia zdjęć"

#: libexif/canon/mnote-canon-tag.c:65 libexif/olympus/mnote-olympus-tag.c:64
#: libexif/olympus/mnote-olympus-tag.c:101
#: libexif/olympus/mnote-olympus-tag.c:110
#: libexif/olympus/mnote-olympus-tag.c:180
#: libexif/pentax/mnote-pentax-tag.c:89
msgid "Digital Zoom"
msgstr "Powiększenie cyfrowe"

#: libexif/canon/mnote-canon-tag.c:66 libexif/exif-tag.c:828
#: libexif/fuji/mnote-fuji-tag.c:42 libexif/pentax/mnote-pentax-tag.c:46
#: libexif/pentax/mnote-pentax-tag.c:91
msgid "Contrast"
msgstr "Kontrastowość"

#: libexif/canon/mnote-canon-tag.c:67 libexif/exif-tag.c:832
#: libexif/olympus/mnote-olympus-tag.c:75
#: libexif/olympus/mnote-olympus-tag.c:87 libexif/pentax/mnote-pentax-tag.c:47
#: libexif/pentax/mnote-pentax-tag.c:90
msgid "Saturation"
msgstr "Nasycenie"

#: libexif/canon/mnote-canon-tag.c:68 libexif/exif-tag.c:836
#: libexif/fuji/mnote-fuji-tag.c:39 libexif/pentax/mnote-pentax-tag.c:45
#: libexif/pentax/mnote-pentax-tag.c:92
msgid "Sharpness"
msgstr "Ostrość"

#: libexif/canon/mnote-canon-tag.c:69
msgid "ISO"
msgstr "ISO"

#: libexif/canon/mnote-canon-tag.c:70 libexif/exif-tag.c:571
#: libexif/pentax/mnote-pentax-tag.c:82
msgid "Metering Mode"
msgstr "Tryb pomiaru"

#: libexif/canon/mnote-canon-tag.c:71 libexif/olympus/mnote-olympus-tag.c:133
msgid "Focus Range"
msgstr "Zakres ogniskowej"

#: libexif/canon/mnote-canon-tag.c:72 libexif/canon/mnote-canon-tag.c:105
msgid "AF Point"
msgstr "Punkt AF"

#: libexif/canon/mnote-canon-tag.c:73 libexif/exif-tag.c:795
msgid "Exposure Mode"
msgstr "Tryb ekspozycji"

#: libexif/canon/mnote-canon-tag.c:74 libexif/olympus/mnote-olympus-tag.c:61
#: libexif/pentax/mnote-pentax-tag.c:106
msgid "Lens Type"
msgstr "Rodzaj obiektywu"

#: libexif/canon/mnote-canon-tag.c:75
msgid "Long Focal Length of Lens"
msgstr "Długa ogniskowa obiektywu"

#: libexif/canon/mnote-canon-tag.c:76
msgid "Short Focal Length of Lens"
msgstr "Krótka ogniskowa obiektywu"

#: libexif/canon/mnote-canon-tag.c:77
msgid "Focal Units per mm"
msgstr "Jednostki ogniskowe na mm"

#: libexif/canon/mnote-canon-tag.c:78
msgid "Maximal Aperture"
msgstr "Maksymalna jasność"

#: libexif/canon/mnote-canon-tag.c:79
msgid "Minimal Aperture"
msgstr "Minimalna jasność"

#: libexif/canon/mnote-canon-tag.c:80
msgid "Flash Activity"
msgstr "Działanie flesza"

#: libexif/canon/mnote-canon-tag.c:81
msgid "Flash Details"
msgstr "Opis flesza"

#: libexif/canon/mnote-canon-tag.c:83
msgid "AE Setting"
msgstr "Ustawienie AE"

#: libexif/canon/mnote-canon-tag.c:84
msgid "Image Stabilization"
msgstr "Stabilizacja obrazu"

#: libexif/canon/mnote-canon-tag.c:85
msgid "Display Aperture"
msgstr "Jasność wyświetlacza"

#: libexif/canon/mnote-canon-tag.c:86
msgid "Zoom Source Width"
msgstr "Szerokość źródłowa powiększenia"

#: libexif/canon/mnote-canon-tag.c:87
msgid "Zoom Target Width"
msgstr "Szerokość docelowa powiększenia"

#: libexif/canon/mnote-canon-tag.c:88
msgid "Photo Effect"
msgstr "Efekt fotograficzny"

#: libexif/canon/mnote-canon-tag.c:89 libexif/canon/mnote-canon-tag.c:118
msgid "Manual Flash Output"
msgstr "Ręczne wyjście flesza"

#: libexif/canon/mnote-canon-tag.c:90
msgid "Color Tone"
msgstr "Tonacja kolorów"

#: libexif/canon/mnote-canon-tag.c:91
msgid "Focal Type"
msgstr "Rodzaj ogniskowania"

#: libexif/canon/mnote-canon-tag.c:93
msgid "Focal Plane X Size"
msgstr "Rozmiar płaszczyzny ogniskowej X"

#: libexif/canon/mnote-canon-tag.c:94
msgid "Focal Plane Y Size"
msgstr "Rozmiar płaszczyzny ogniskowej Y"

#: libexif/canon/mnote-canon-tag.c:95
msgid "Auto ISO"
msgstr "ISO auto"

#: libexif/canon/mnote-canon-tag.c:96
msgid "Shot ISO"
msgstr "ISO zdjęcia"

#: libexif/canon/mnote-canon-tag.c:97
msgid "Measured EV"
msgstr "Zmierzone EV"

#: libexif/canon/mnote-canon-tag.c:98
msgid "Target Aperture"
msgstr "Przysłona docelowa"

#: libexif/canon/mnote-canon-tag.c:99
msgid "Target Exposure Time"
msgstr "Docelowy czas ekspozycji"

#: libexif/canon/mnote-canon-tag.c:100 libexif/olympus/mnote-olympus-tag.c:129
#: libexif/pentax/mnote-pentax-tag.c:81
msgid "Exposure Compensation"
msgstr "Kompensacja ekspozycji"

#: libexif/canon/mnote-canon-tag.c:101 libexif/canon/mnote-canon-tag.c:123
#: libexif/exif-tag.c:800 libexif/fuji/mnote-fuji-tag.c:40
#: libexif/olympus/mnote-olympus-tag.c:41
#: libexif/olympus/mnote-olympus-tag.c:98 libexif/pentax/mnote-pentax-tag.c:41
#: libexif/pentax/mnote-pentax-tag.c:84 libexif/pentax/mnote-pentax-tag.c:124
msgid "White Balance"
msgstr "Balans bieli"

#: libexif/canon/mnote-canon-tag.c:102
msgid "Slow Shutter"
msgstr "Wolna migawka"

#: libexif/canon/mnote-canon-tag.c:103
msgid "Sequence Number"
msgstr "Numer sekwencyjny"

#: libexif/canon/mnote-canon-tag.c:104
msgid "Flash Guide Number"
msgstr "Liczba poprzedzających fleszy"

#: libexif/canon/mnote-canon-tag.c:106 libexif/olympus/mnote-olympus-tag.c:52
#: libexif/pentax/mnote-pentax-tag.c:109
msgid "Flash Exposure Compensation"
msgstr "Kompensacja ekspozycji flesza"

#: libexif/canon/mnote-canon-tag.c:107
msgid "AE Bracketing"
msgstr "Bracketing AE"

#: libexif/canon/mnote-canon-tag.c:108
msgid "AE Bracket Value"
msgstr "Wartość bracketingu AE"

#: libexif/canon/mnote-canon-tag.c:109
msgid "Focus Distance Upper"
msgstr "Górna odległość ogniska"

#: libexif/canon/mnote-canon-tag.c:110
msgid "Focus Distance Lower"
msgstr "Dolna odległość ogniska"

#: libexif/canon/mnote-canon-tag.c:111
msgid "FNumber"
msgstr "Liczba F"

#: libexif/canon/mnote-canon-tag.c:112 libexif/exif-tag.c:466
#: libexif/pentax/mnote-pentax-tag.c:78
msgid "Exposure Time"
msgstr "Czas ekspozycji"

#: libexif/canon/mnote-canon-tag.c:113
msgid "Bulb Duration"
msgstr "Okres żarówki"

#: libexif/canon/mnote-canon-tag.c:114
msgid "Camera Type"
msgstr "Rodzaj aparatu"

#: libexif/canon/mnote-canon-tag.c:115
msgid "Auto Rotate"
msgstr "Obrót automatyczny"

#: libexif/canon/mnote-canon-tag.c:116
msgid "ND Filter"
msgstr "Filtr ND"

#: libexif/canon/mnote-canon-tag.c:119
msgid "Panorama Frame"
msgstr "Ramka panoramy"

#: libexif/canon/mnote-canon-tag.c:120
msgid "Panorama Direction"
msgstr "Kierunek panoramy"

#: libexif/canon/mnote-canon-tag.c:121
msgid "Tone Curve"
msgstr "Krzywa tonalna"

#: libexif/canon/mnote-canon-tag.c:122
msgid "Sharpness Frequency"
msgstr "Częstotliwość ostrości"

#: libexif/canon/mnote-canon-tag.c:124
msgid "Picture Style"
msgstr "Styl zdjęcia"

#: libexif/exif-byte-order.c:33
msgid "Motorola"
msgstr "Motorola"

#: libexif/exif-byte-order.c:35
msgid "Intel"
msgstr "Intel"

#: libexif/exif-data.c:780
msgid "Size of data too small to allow for EXIF data."
msgstr "Rozmiar danych zbyt mały aby pozwalał na dane EXIF."

#: libexif/exif-data.c:841
msgid "EXIF marker not found."
msgstr "Nie znaleziono znacznika EXIF."

#: libexif/exif-data.c:868
msgid "EXIF header not found."
msgstr "Nie znaleziono nagłówka EXIF."

#: libexif/exif-data.c:893
msgid "Unknown encoding."
msgstr "Nieznane kodowanie."

#: libexif/exif-data.c:1178
msgid "Ignore unknown tags"
msgstr "Ignorowanie nieznanych znaczników"

#: libexif/exif-data.c:1179
msgid "Ignore unknown tags when loading EXIF data."
msgstr "Ignorowanie nieznanych znaczników przy wczytywaniu danych EXIF."

#: libexif/exif-data.c:1180
msgid "Follow specification"
msgstr "Zgodność ze specyfikacją"

#: libexif/exif-data.c:1181
msgid ""
"Add, correct and remove entries to get EXIF data that follows the "
"specification."
msgstr ""
"Dodawanie, poprawianie i usuwanie wpisów aby uzyskać dane EXIF zgodne ze "
"specyfikacją."

#: libexif/exif-data.c:1183
msgid "Do not change maker note"
msgstr "Nie zmieniaj oznaczenia twórcy"

#: libexif/exif-data.c:1184
msgid ""
"When loading and resaving Exif data, save the maker note unmodified. Be "
"aware that the maker note can get corrupted."
msgstr ""
"Przy wczytywaniu i zapisywaniu danych Exif pozostaw oznaczenie twórcy "
"niezmienione. Należy mieć świadomość, że oznaczenie twórcy może zostać "
"uszkodzone."

#: libexif/exif-entry.c:234 libexif/exif-entry.c:303 libexif/exif-entry.c:336
#, c-format
msgid ""
"Tag '%s' was of format '%s' (which is against specification) and has been "
"changed to format '%s'."
msgstr ""
"Znacznik '%s' był w formacie '%s' (co jest niezgodne ze specyfikacją) i "
"został zmieniony na format '%s'."

#: libexif/exif-entry.c:271
#, c-format
msgid ""
"Tag '%s' is of format '%s' (which is against specification) but cannot be "
"changed to format '%s'."
msgstr ""
"Znacznik '%s' jest w formacie '%s' (co jest niezgodne ze specyfikacją), ale "
"nie może być zmieniony na format '%s'."

#: libexif/exif-entry.c:354
#, c-format
msgid ""
"Tag 'UserComment' had invalid format '%s'. Format has been set to "
"'undefined'."
msgstr ""
"Znacznik 'UserComment' ma nieprawidłowy format '%s'. Format został ustawiony "
"na 'undefined'."

#: libexif/exif-entry.c:381
msgid ""
"Tag 'UserComment' has been expanded to at least 8 bytes in order to follow "
"the specification."
msgstr ""
"Znacznik 'UserComment' został rozszerzony do co najmniej 8 bajtów dla "
"zgodności ze specyfikacją."

#: libexif/exif-entry.c:396
msgid ""
"Tag 'UserComment' is not empty but does not start with a format identifier. "
"This has been fixed."
msgstr ""
"Znacznik 'UserComment' nie jest pusty, ale nie zaczynał się od "
"identyfikatora formatu. Poprawiono."

#: libexif/exif-entry.c:424
msgid ""
"Tag 'UserComment' did not start with a format identifier. This has been "
"fixed."
msgstr ""
"Znacznik 'UserComment' nie zaczynał się od identyfikatora formatu. "
"Poprawiono."

#: libexif/exif-entry.c:462
#, c-format
msgid "%i bytes undefined data"
msgstr "%i bajtów niezdefiniowanych danych"

#: libexif/exif-entry.c:585
#, c-format
msgid "%i bytes unsupported data type"
msgstr "%i bajtów nie obsługiwanego rodzaju danych"

#: libexif/exif-entry.c:642
#, c-format
msgid "The tag '%s' contains data of an invalid format ('%s', expected '%s')."
msgstr ""
"Znacznik '%s' zawiera dane w błędnym formacie ('%s', a oczekiwano '%s')."

#: libexif/exif-entry.c:655
#, c-format
msgid ""
"The tag '%s' contains an invalid number of components (%i, expected %i)."
msgstr "Znacznik '%s' zawiera błędną liczbę składników (%i, a oczekiwano %i)."

#: libexif/exif-entry.c:669
msgid "Chunky format"
msgstr "Format blokowy"

#: libexif/exif-entry.c:669
msgid "Planar format"
msgstr "Format płaski"

#: libexif/exif-entry.c:671 libexif/exif-entry.c:763
#: test/nls/test-codeset.c:54
msgid "Not defined"
msgstr "Nieokreślony"

#: libexif/exif-entry.c:671
msgid "One-chip color area sensor"
msgstr "Jednoukładowy czujnik obszaru koloru"

#: libexif/exif-entry.c:672
msgid "Two-chip color area sensor"
msgstr "Dwuukładowy czujnik obszaru koloru"

#: libexif/exif-entry.c:672
msgid "Three-chip color area sensor"
msgstr "Trzyukładowy czujnik obszaru koloru"

#: libexif/exif-entry.c:673
msgid "Color sequential area sensor"
msgstr "Czujnik sekwencyjny obszaru koloru"

#: libexif/exif-entry.c:673
msgid "Trilinear sensor"
msgstr "Czujnik trzyliniowy"

#: libexif/exif-entry.c:674
msgid "Color sequential linear sensor"
msgstr "Czujnik sekwencyjny liniowy koloru"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:233
msgid "Top-left"
msgstr "Lewy górny róg"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:235
msgid "Top-right"
msgstr "Prawy górny róg"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:241
msgid "Bottom-right"
msgstr "Prawy dolny róg"

#: libexif/exif-entry.c:677 libexif/pentax/mnote-pentax-entry.c:239
msgid "Bottom-left"
msgstr "Lewy dolny róg"

#: libexif/exif-entry.c:677
msgid "Left-top"
msgstr "Lewo - góra"

#: libexif/exif-entry.c:677
msgid "Right-top"
msgstr "Prawo - góra"

#: libexif/exif-entry.c:678
msgid "Right-bottom"
msgstr "Prawo - dół"

#: libexif/exif-entry.c:678
msgid "Left-bottom"
msgstr "Lewo - dół"

#: libexif/exif-entry.c:680
msgid "Centered"
msgstr "Wyśrodkowane"

#: libexif/exif-entry.c:680
msgid "Co-sited"
msgstr "Położone razem"

#: libexif/exif-entry.c:682
msgid "Reversed mono"
msgstr "Odwrócone mono"

#: libexif/exif-entry.c:682
msgid "Normal mono"
msgstr "Normalne mono"

#: libexif/exif-entry.c:682
msgid "RGB"
msgstr "RGB"

#: libexif/exif-entry.c:682
msgid "Palette"
msgstr "Paleta"

#: libexif/exif-entry.c:683
msgid "CMYK"
msgstr "CMYK"

#: libexif/exif-entry.c:683
msgid "YCbCr"
msgstr "YCbCr"

#: libexif/exif-entry.c:683
msgid "CieLAB"
msgstr "CieLAB"

#: libexif/exif-entry.c:685
msgid "Normal process"
msgstr "Przebieg zwykły"

#: libexif/exif-entry.c:685
msgid "Custom process"
msgstr "Przebieg własny"

#: libexif/exif-entry.c:687
msgid "Auto exposure"
msgstr "Ekspozycja automatyczna"

#: libexif/exif-entry.c:687 libexif/fuji/mnote-fuji-entry.c:139
msgid "Manual exposure"
msgstr "Ekspozycja ręczna"

#: libexif/exif-entry.c:687
msgid "Auto bracket"
msgstr "Auto bracket"

#: libexif/exif-entry.c:689
msgid "Auto white balance"
msgstr "Automatyczny balans bieli"

#: libexif/exif-entry.c:689
msgid "Manual white balance"
msgstr "Ręczny balans bieli"

#: libexif/exif-entry.c:694
msgid "Low gain up"
msgstr "Niskie wzmocnienie na górze"

#: libexif/exif-entry.c:694
msgid "High gain up"
msgstr "Wysokie wzmocnienie na górze"

#: libexif/exif-entry.c:695
msgid "Low gain down"
msgstr "Niskie wzmocnienie na dole"

#: libexif/exif-entry.c:695
msgid "High gain down"
msgstr "Wysokie wzmocnienie na dole"

#: libexif/exif-entry.c:697
msgid "Low saturation"
msgstr "Małe nasycenie"

#: libexif/exif-entry.c:697 test/nls/test-codeset.c:48
#: test/nls/test-codeset.c:61
msgid "High saturation"
msgstr "Duże nasycenie"

#: libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/fuji/mnote-fuji-entry.c:63
#: libexif/olympus/mnote-olympus-entry.c:208
#: libexif/olympus/mnote-olympus-entry.c:217
#: libexif/pentax/mnote-pentax-entry.c:106
#: libexif/pentax/mnote-pentax-entry.c:170
msgid "Soft"
msgstr "Mała"

#: libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/fuji/mnote-fuji-entry.c:65 libexif/fuji/mnote-fuji-entry.c:95
#: libexif/olympus/mnote-olympus-entry.c:207
#: libexif/olympus/mnote-olympus-entry.c:215
#: libexif/pentax/mnote-pentax-entry.c:107
msgid "Hard"
msgstr "Duża"

#: libexif/exif-entry.c:715 libexif/exif-entry.c:733 libexif/exif-entry.c:815
#: libexif/olympus/mnote-olympus-entry.c:595
#: libexif/olympus/mnote-olympus-entry.c:689
#: libexif/olympus/mnote-olympus-entry.c:744
#: libexif/pentax/mnote-pentax-entry.c:256
msgid "Unknown"
msgstr "Brak informacji"

#: libexif/exif-entry.c:716
msgid "Avg"
msgstr "Śr."

#: libexif/exif-entry.c:717
msgid "Center-weight"
msgstr "Centralnie ważony"

#: libexif/exif-entry.c:719
msgid "Multi spot"
msgstr "Wielopunktowy"

#: libexif/exif-entry.c:720
msgid "Pattern"
msgstr "Wzorzec"

#: libexif/exif-entry.c:725
msgid "Uncompressed"
msgstr "Bez kompresji"

#: libexif/exif-entry.c:726
msgid "LZW compression"
msgstr "Kompresja LZW"

#: libexif/exif-entry.c:727 libexif/exif-entry.c:728
msgid "JPEG compression"
msgstr "Kompresja JPEG"

#: libexif/exif-entry.c:729
msgid "Deflate/ZIP compression"
msgstr "Kompresja Deflate/ZIP"

#: libexif/exif-entry.c:730
msgid "PackBits compression"
msgstr "Kompresja PackBits"

#: libexif/exif-entry.c:736
msgid "Tungsten incandescent light"
msgstr "Żarówka wolframowa"

#: libexif/exif-entry.c:738
msgid "Fine weather"
msgstr "Dobra pogoda"

#: libexif/exif-entry.c:739
msgid "Cloudy weather"
msgstr "Pochmurna pogoda"

#: libexif/exif-entry.c:742 libexif/fuji/mnote-fuji-entry.c:77
#: libexif/pentax/mnote-pentax-entry.c:252
msgid "Day white fluorescent"
msgstr "Białe fluorescencyjne światło dzienne"

#: libexif/exif-entry.c:743
msgid "Cool white fluorescent"
msgstr "Zimne białe światło fluorescencyjne"

#: libexif/exif-entry.c:744 libexif/fuji/mnote-fuji-entry.c:78
#: libexif/pentax/mnote-pentax-entry.c:253
msgid "White fluorescent"
msgstr "Białe światło fluorescencyjne"

#: libexif/exif-entry.c:745
msgid "Standard light A"
msgstr "Światło standardowe A"

#: libexif/exif-entry.c:746
msgid "Standard light B"
msgstr "Światło standardowe B"

#: libexif/exif-entry.c:747
msgid "Standard light C"
msgstr "Światło standardowe C"

#: libexif/exif-entry.c:748
msgid "D55"
msgstr "D55"

#: libexif/exif-entry.c:749
msgid "D65"
msgstr "D65"

#: libexif/exif-entry.c:750
msgid "D75"
msgstr "D75"

#: libexif/exif-entry.c:751
msgid "ISO studio tungsten"
msgstr "Wolframowe oświetlenie studyjne ISO"

#: libexif/exif-entry.c:755 libexif/exif-entry.c:759
msgid "Inch"
msgstr "Cal"

#: libexif/exif-entry.c:755 libexif/exif-entry.c:759
msgid "in"
msgstr "in"

#: libexif/exif-entry.c:756 libexif/exif-entry.c:760
msgid "Centimeter"
msgstr "Centymetr"

#: libexif/exif-entry.c:756 libexif/exif-entry.c:760
msgid "cm"
msgstr "cm"

#: libexif/exif-entry.c:765
msgid "Normal program"
msgstr "Program zwykły"

#: libexif/exif-entry.c:766
msgid "Aperture priority"
msgstr "Priorytet przysłony"

#: libexif/exif-entry.c:766 libexif/exif-tag.c:550
msgid "Aperture"
msgstr "Przysłona"

#: libexif/exif-entry.c:767
msgid "Shutter priority"
msgstr "Priorytet migawki"

#: libexif/exif-entry.c:767
msgid "Shutter"
msgstr "Migawka"

#: libexif/exif-entry.c:768
msgid "Creative program (biased toward depth of field)"
msgstr "Program twórczy (nakierowany na głębię obrazu)"

#: libexif/exif-entry.c:769
msgid "Creative"
msgstr "Twórczy"

#: libexif/exif-entry.c:770
msgid "Creative program (biased toward fast shutter speed)"
msgstr "Program twórczy (nakierowany na szybką migawkę)"

#: libexif/exif-entry.c:771
msgid "Action"
msgstr "Akcja"

#: libexif/exif-entry.c:772
msgid "Portrait mode (for closeup photos with the background out of focus)"
msgstr "Tryb portretowy (do zbliżeń z tłem poza ogniskiem)"

#: libexif/exif-entry.c:774
msgid "Landscape mode (for landscape photos with the background in focus)"
msgstr "Tryb pejzażowy (do krajobrazów z tłem w ognisku)"

#: libexif/exif-entry.c:778 libexif/exif-entry.c:783
#: libexif/olympus/mnote-olympus-entry.c:100
msgid "Flash did not fire"
msgstr "Flesz się nie uruchomił"

#: libexif/exif-entry.c:778
msgid "No flash"
msgstr "Bez flesza"

#: libexif/exif-entry.c:779
msgid "Flash fired"
msgstr "Flesz się uruchomił"

#: libexif/exif-entry.c:779 libexif/olympus/mnote-olympus-entry.c:173
#: libexif/olympus/mnote-olympus-entry.c:178
#: libexif/olympus/mnote-olympus-entry.c:212
#: libexif/olympus/mnote-olympus-entry.c:221
#: libexif/olympus/mnote-olympus-entry.c:244
msgid "Yes"
msgstr "Tak"

#: libexif/exif-entry.c:780
msgid "Strobe return light not detected"
msgstr "Zwrotne światło stroboskopowe nie wykryte"

#: libexif/exif-entry.c:780
msgid "Without strobe"
msgstr "Bez światła stroboskopowego"

#: libexif/exif-entry.c:782
msgid "Strobe return light detected"
msgstr "Zwrotne światło stroboskopowe wykryte"

#: libexif/exif-entry.c:782
msgid "With strobe"
msgstr "Ze światłem stroboskopowym"

#: libexif/exif-entry.c:784
msgid "Flash fired, compulsory flash mode"
msgstr "Flesz się uruchomił w trybie pulsującym"

#: libexif/exif-entry.c:785
msgid "Flash fired, compulsory flash mode, return light not detected"
msgstr "Flesz się uruchomił w trybie pulsującym, światło zwrotne nie wykryte"

#: libexif/exif-entry.c:787
msgid "Flash fired, compulsory flash mode, return light detected"
msgstr "Flesz się uruchomił w trybie pulsującym, światło zwrotne wykryte"

#: libexif/exif-entry.c:789
msgid "Flash did not fire, compulsory flash mode"
msgstr "Flesz się nie uruchomił w trybie pulsującym"

#: libexif/exif-entry.c:790
msgid "Flash did not fire, auto mode"
msgstr "Flesz się nie uruchomił w trybie automatycznym"

#: libexif/exif-entry.c:791
msgid "Flash fired, auto mode"
msgstr "Flesz się uruchomił w trybie automatycznym"

#: libexif/exif-entry.c:792
msgid "Flash fired, auto mode, return light not detected"
msgstr ""
"Flesz się uruchomił w trybie automatycznym, światło zwrotne nie wykryte"

#: libexif/exif-entry.c:794
msgid "Flash fired, auto mode, return light detected"
msgstr "Flesz się uruchomił w trybie automatycznym, światło zwrotne wykryte"

#: libexif/exif-entry.c:795
msgid "No flash function"
msgstr "Brak flesza"

#: libexif/exif-entry.c:796
msgid "Flash fired, red-eye reduction mode"
msgstr "Flesz się uruchomił w trybie redukcji czerwonych oczu"

#: libexif/exif-entry.c:797
msgid "Flash fired, red-eye reduction mode, return light not detected"
msgstr ""
"Flesz się uruchomił w trybie redukcji czerwonych oczu, światło zwrotne nie "
"wykryte"

#: libexif/exif-entry.c:799
msgid "Flash fired, red-eye reduction mode, return light detected"
msgstr ""
"Flesz się uruchomił w trybie redukcji czerwonych oczu, światło zwrotne "
"wykryte"

#: libexif/exif-entry.c:801
msgid "Flash fired, compulsory flash mode, red-eye reduction mode"
msgstr "Flesz się uruchomił w trybie pulsującym z redukcją czerwonych oczu"

#: libexif/exif-entry.c:803
msgid ""
"Flash fired, compulsory flash mode, red-eye reduction mode, return light not "
"detected"
msgstr ""
"Flesz się uruchomił w trybie pulsującym z redukcją czerwonych oczu, światło "
"zwrotne nie wykryte"

#: libexif/exif-entry.c:805
msgid ""
"Flash fired, compulsory flash mode, red-eye reduction mode, return light "
"detected"
msgstr ""
"Flesz się uruchomił w trybie pulsującym z redukcją czerwonych oczu, światło "
"zwrotne wykryte"

#: libexif/exif-entry.c:807
msgid "Flash did not fire, auto mode, red-eye reduction mode"
msgstr ""
"Flesz się nie uruchomił w trybie automatycznym z redukcją czerwonych oczu"

#: libexif/exif-entry.c:808
msgid "Flash fired, auto mode, red-eye reduction mode"
msgstr "Flesz się uruchomił w trybie automatycznym z redukcją czerwonych oczu"

#: libexif/exif-entry.c:809
msgid ""
"Flash fired, auto mode, return light not detected, red-eye reduction mode"
msgstr ""
"Flesz się uruchomił w trybie automatycznym z redukcją czerwonych oczu, "
"światło zwrotne nie wykryte"

#: libexif/exif-entry.c:811
msgid "Flash fired, auto mode, return light detected, red-eye reduction mode"
msgstr ""
"Flesz się uruchomił w trybie automatycznym z redukcją czerwonych oczu, "
"światło zwrotne wykryte"

#: libexif/exif-entry.c:815
msgid "?"
msgstr "?"

#: libexif/exif-entry.c:817
msgid "Close view"
msgstr "Widok bliski"

#: libexif/exif-entry.c:818
msgid "Distant view"
msgstr "Widok daleki"

#: libexif/exif-entry.c:818
msgid "Distant"
msgstr "Daleko"

#: libexif/exif-entry.c:821
msgid "sRGB"
msgstr "sRGB"

#: libexif/exif-entry.c:822
msgid "Adobe RGB"
msgstr "RGB Adobe"

#: libexif/exif-entry.c:823
msgid "Uncalibrated"
msgstr "Nieskalibrowana"

#: libexif/exif-entry.c:878
#, c-format
msgid "Invalid size of entry (%i, expected %li x %i)."
msgstr "Błędny rozmiar wpisu (%i, a oczekiwano %li x %i)."

#: libexif/exif-entry.c:911
msgid "Unsupported UNICODE string"
msgstr "Nieobsługiwany łańcuch UNICODE"

#: libexif/exif-entry.c:919
msgid "Unsupported JIS string"
msgstr "Nieobsługiwany łańcuch JIS"

#: libexif/exif-entry.c:935
msgid "Tag UserComment contains data but is against specification."
msgstr "Znacznik UserComment zawiera dane, ale jest niezgodny ze specyfikacją."

#: libexif/exif-entry.c:939
#, c-format
msgid "Byte at position %i: 0x%02x"
msgstr "Bajt na pozycji %i: 0x%02x"

#: libexif/exif-entry.c:947
msgid "Unknown Exif Version"
msgstr "Nieznana wersja Exif"

#: libexif/exif-entry.c:951
#, c-format
msgid "Exif Version %d.%d"
msgstr "Exif w wersji %d.%d"

#: libexif/exif-entry.c:962
msgid "FlashPix Version 1.0"
msgstr "FlashPix w wersji 1.0"

#: libexif/exif-entry.c:964
msgid "FlashPix Version 1.01"
msgstr "FlashPIx w wersji 1.01"

#: libexif/exif-entry.c:966
msgid "Unknown FlashPix Version"
msgstr "Nieznana wersja FlashPix"

#: libexif/exif-entry.c:979 libexif/exif-entry.c:998 libexif/exif-entry.c:1666
#: libexif/exif-entry.c:1671 libexif/exif-entry.c:1675
#: libexif/exif-entry.c:1680 libexif/exif-entry.c:1681
msgid "[None]"
msgstr "[Brak]"

#: libexif/exif-entry.c:981
msgid "(Photographer)"
msgstr "(Fotograf)"

#: libexif/exif-entry.c:1000
msgid "(Editor)"
msgstr "(Redaktor)"

#: libexif/exif-entry.c:1024 libexif/exif-entry.c:1104
#: libexif/exif-entry.c:1121 libexif/exif-entry.c:1165
#, c-format
msgid "%.02f EV"
msgstr "%.02f EV"

#: libexif/exif-entry.c:1025
#, c-format
msgid " (f/%.01f)"
msgstr " (f/%.01f)"

#: libexif/exif-entry.c:1059
#, c-format
msgid " (35 equivalent: %d mm)"
msgstr " (odpowiednik 35: %d mm)"

#: libexif/exif-entry.c:1092 libexif/exif-entry.c:1093
msgid " sec."
msgstr " sek."

#: libexif/exif-entry.c:1107
#, c-format
msgid " (1/%d sec.)"
msgstr " (1/%d sek.)"

#: libexif/exif-entry.c:1109
#, c-format
msgid " (%d sec.)"
msgstr " (%d sek.)"

#: libexif/exif-entry.c:1122
#, c-format
msgid " (%.02f cd/m^2)"
msgstr " (%.02f cd/m^2)"

#: libexif/exif-entry.c:1132
msgid "DSC"
msgstr "DSC"

#: libexif/exif-entry.c:1134 libexif/exif-entry.c:1174
#: libexif/exif-entry.c:1261 libexif/exif-entry.c:1312
#: libexif/exif-entry.c:1321 libexif/exif-entry.c:1357
#: libexif/fuji/mnote-fuji-entry.c:236 libexif/fuji/mnote-fuji-entry.c:245
#: libexif/pentax/mnote-pentax-entry.c:350
#: libexif/pentax/mnote-pentax-entry.c:359
#, c-format
msgid "Internal error (unknown value %i)"
msgstr "Błąd wewnętrzny (nieznana wartość %i)"

#: libexif/exif-entry.c:1142
msgid "-"
msgstr "-"

#: libexif/exif-entry.c:1143
msgid "Y"
msgstr "Y"

#: libexif/exif-entry.c:1144
msgid "Cb"
msgstr "Cb"

#: libexif/exif-entry.c:1145
msgid "Cr"
msgstr "Cr"

#: libexif/exif-entry.c:1146
msgid "R"
msgstr "R"

#: libexif/exif-entry.c:1147
msgid "G"
msgstr "G"

#: libexif/exif-entry.c:1148
msgid "B"
msgstr "B"

#: libexif/exif-entry.c:1149
msgid "Reserved"
msgstr "Zarezerwowany"

#: libexif/exif-entry.c:1172
msgid "Directly photographed"
msgstr "Fotografowany bezpośrednio"

#: libexif/exif-entry.c:1185
msgid "YCbCr4:2:2"
msgstr "YCbCr4:2:2"

#: libexif/exif-entry.c:1187
msgid "YCbCr4:2:0"
msgstr "YCbCr4:2:0"

#: libexif/exif-entry.c:1204
#, c-format
msgid "Within distance %i of (x,y) = (%i,%i)"
msgstr "W odległości %i w (x,y) = (%i,%i)"

#: libexif/exif-entry.c:1213
#, c-format
msgid "Within rectangle (width %i, height %i) around (x,y) = (%i,%i)"
msgstr ""
"Wewnątrz prostokąta (szerokość %i, wysokość %i) w okolicy (x,y) = (%i,%i)"

#: libexif/exif-entry.c:1219
#, c-format
msgid "Unexpected number of components (%li, expected 2, 3, or 4)."
msgstr "Nieoczekiwana liczba składowych (%li, a oczekiwano 2, 3 lub 4)."

#: libexif/exif-entry.c:1257
msgid "Sea level"
msgstr "Poziom morza"

#: libexif/exif-entry.c:1259
msgid "Sea level reference"
msgstr "Odniesienie poziomu morza"

#: libexif/exif-entry.c:1367
#, c-format
msgid "Unknown value %i"
msgstr "Nieznana wartość %i"

#: libexif/exif-format.c:37
msgid "Short"
msgstr "Short"

#: libexif/exif-format.c:38
msgid "Rational"
msgstr "Rational"

#: libexif/exif-format.c:39
msgid "SRational"
msgstr "SRational"

#: libexif/exif-format.c:40
msgid "Undefined"
msgstr "Nieokreślony"

#: libexif/exif-format.c:41
msgid "ASCII"
msgstr "ASCII"

#: libexif/exif-format.c:42
msgid "Long"
msgstr "Long"

#: libexif/exif-format.c:43
msgid "Byte"
msgstr "Byte"

#: libexif/exif-format.c:44
msgid "SByte"
msgstr "SByte"

#: libexif/exif-format.c:45
msgid "SShort"
msgstr "SShort"

#: libexif/exif-format.c:46
msgid "SLong"
msgstr "SLong"

#: libexif/exif-format.c:47
msgid "Float"
msgstr "Float"

#: libexif/exif-format.c:48
msgid "Double"
msgstr "Double"

#: libexif/exif-loader.c:119
#, c-format
msgid "The file '%s' could not be opened."
msgstr "Nie udało się otworzyć pliku '%s'."

#: libexif/exif-loader.c:300
msgid "The data supplied does not seem to contain EXIF data."
msgstr "Podane dane nie wyglądają na zawierające dane EXIF."

#: libexif/exif-log.c:43
msgid "Debugging information"
msgstr "Informacje diagnostyczne"

#: libexif/exif-log.c:44
msgid "Debugging information is available."
msgstr "Dostępne są informacje diagnostyczne."

#: libexif/exif-log.c:45
msgid "Not enough memory"
msgstr "Zbyt mało pamięci"

#: libexif/exif-log.c:46
msgid "The system cannot provide enough memory."
msgstr "System nie może zapewnić wystarczająco dużo pamięci."

#: libexif/exif-log.c:47
msgid "Corrupt data"
msgstr "Uszkodzone dane"

#: libexif/exif-log.c:48
msgid "The data provided does not follow the specification."
msgstr "Dostarczone dane nie są zgodne ze specyfikacją."

#: libexif/exif-tag.c:62
msgid "GPS Tag Version"
msgstr "Wersja znacznika GPS"

#: libexif/exif-tag.c:63
msgid ""
"Indicates the version of <GPSInfoIFD>. The version is given as 2.0.0.0. This "
"tag is mandatory when <GPSInfo> tag is present. (Note: The <GPSVersionID> "
"tag is given in bytes, unlike the <ExifVersion> tag. When the version is "
"2.0.0.0, the tag value is 02000000.H)."
msgstr ""
"Oznaczenie wersji <GPSInfoIFD>. Wersja jest podawana jako 2.0.0.0. Ten "
"znacznik jest obowiązkowy, jeśli obecny jest znacznik <GPSInfo>. (Uwaga: "
"znacznik <GPSVersionID> jest podawany w bajtach, w przeciwieństwie do "
"znacznika <ExifVersion>. Kiedy wersja to 2.0.0.0, znacznik ma wartość "
"02000000.H)."

#: libexif/exif-tag.c:69
msgid "Interoperability Index"
msgstr "Indeks Interoperability"

#: libexif/exif-tag.c:70
msgid ""
"Indicates the identification of the Interoperability rule. Use \"R98\" for "
"stating ExifR98 Rules. Four bytes used including the termination code "
"(NULL). see the separate volume of Recommended Exif Interoperability Rules "
"(ExifR98) for other tags used for ExifR98."
msgstr ""
"Oznaczenie identyfikacji reguły współpracy. Należy użyć \"R98\" dla "
"oznaczenia reguł ExifR98. Używane są cztery bajty wraz ze znacznikiem końca "
"(NULL). Inne znaczniki ExifR98 są opisane w pozycji Recommended Exif "
"Interoperability Rules (ExifR98)."

#: libexif/exif-tag.c:76
msgid "North or South Latitude"
msgstr "Szerokość północna lub południowa"

#: libexif/exif-tag.c:77
msgid ""
"Indicates whether the latitude is north or south latitude. The ASCII value "
"'N' indicates north latitude, and 'S' is south latitude."
msgstr ""
"Oznaczenie, czy szerokość geograficzna jest północna, czy południowa. "
"Wartość ASCII 'N' oznacza szerokość północną, a 'S' południową."

#: libexif/exif-tag.c:81
msgid "Interoperability Version"
msgstr "Wersja Interoperability"

#: libexif/exif-tag.c:83
msgid "Latitude"
msgstr "Szerokość geograficzna"

#: libexif/exif-tag.c:84
msgid ""
"Indicates the latitude. The latitude is expressed as three RATIONAL values "
"giving the degrees, minutes, and seconds, respectively. When degrees, "
"minutes and seconds are expressed, the format is dd/1,mm/1,ss/1. When "
"degrees and minutes are used and, for example, fractions of minutes are "
"given up to two decimal places, the format is dd/1,mmmm/100,0/1."
msgstr ""
"Określenie szerokości geograficznej. Szerokość jest wyrażona jako trzy "
"wartości RATIONAL (wymierne) podające odpowiednio stopnie, minuty i sekundy. "
"Kiedy są wyrażone stopnie, minuty i sekundy, format to dd/1,mm/1,ss/1. Kiedy "
"są wyrażone stopnie i minuty oraz np. ułamki minut są podane z dokładnością "
"do dwóch miejsc po przecinku, format to dd/1,mmmm/100,0/1."

#: libexif/exif-tag.c:91
msgid "East or West Longitude"
msgstr "Długość wschodnia lub zachodnia"

#: libexif/exif-tag.c:92
msgid ""
"Indicates whether the longitude is east or west longitude. ASCII 'E' "
"indicates east longitude, and 'W' is west longitude."
msgstr ""
"Określenie, czy długość geograficzna jest wschodnia, czy zachodnia. Wartość "
"ASCII 'E' oznacza długość wschodnią, a 'W' zachodnią."

#: libexif/exif-tag.c:95
msgid "Longitude"
msgstr "Długość geograficzna"

#: libexif/exif-tag.c:96
msgid ""
"Indicates the longitude. The longitude is expressed as three RATIONAL values "
"giving the degrees, minutes, and seconds, respectively. When degrees, "
"minutes and seconds are expressed, the format is ddd/1,mm/1,ss/1. When "
"degrees and minutes are used and, for example, fractions of minutes are "
"given up to two decimal places, the format is ddd/1,mmmm/100,0/1."
msgstr ""
"Określenie długości geograficznej. Szerokość jest wyrażona jako trzy "
"wartości RATIONAL (wymierne) podające odpowiednio stopnie, minuty i sekundy. "
"Kiedy są wyrażone stopnie, minuty i sekundy, format to dd/1,mm/1,ss/1. Kiedy "
"są wyrażone stopnie i minuty oraz np. ułamki minut są podane z dokładnością "
"do dwóch miejsc po przecinku, format to dd/1,mmmm/100,0/1."

#: libexif/exif-tag.c:103
msgid "Altitude Reference"
msgstr "Odniesienie wysokości"

#: libexif/exif-tag.c:104
msgid ""
"Indicates the altitude used as the reference altitude. If the reference is "
"sea level and the altitude is above sea level, 0 is given. If the altitude "
"is below sea level, a value of 1 is given and the altitude is indicated as "
"an absolute value in the GSPAltitude tag. The reference unit is meters. Note "
"that this tag is BYTE type, unlike other reference tags."
msgstr ""
"Określenie wysokości użytej jako wysokość odniesienia. Jeśli odniesienie "
"jest poziomem morza i wysokość jest nad poziomem morza, podaje się 0. Jeśli "
"wysokość jest poniżej poziomu morza, podaje się wartość 1 i wysokość oznacza "
"się jako wartość bezwzględną w znaczniku GPSAltitude. Jednostką odniesienia "
"są metry. Ten znacznik jest typu BYTE w przeciwieństwie do innych znaczników "
"odniesienia."

#: libexif/exif-tag.c:110
msgid "Altitude"
msgstr "Wysokość"

#: libexif/exif-tag.c:111
msgid ""
"Indicates the altitude based on the reference in GPSAltitudeRef. Altitude is "
"expressed as one RATIONAL value. The reference unit is meters."
msgstr ""
"Określenie wysokości w oparciu o odniesienie w GPSAltitudeRef. Wysokość jest "
"wyrażona jako jedna wartość RATIONAL (wymierna). Jednostką odniesienia są "
"metry."

#: libexif/exif-tag.c:114
msgid "GPS Time (Atomic Clock)"
msgstr "Czas GPS (zegar atomowy)"

#: libexif/exif-tag.c:115
msgid ""
"Indicates the time as UTC (Coordinated Universal Time). TimeStamp is "
"expressed as three RATIONAL values giving the hour, minute, and second."
msgstr ""
"Określenie czasu jako UTC (Coordinated Universal Time). Znacznik jest "
"wyrażony jako trzy wartości RATIONAL (wymierne) określające godzinę, minuty "
"i sekundy."

#: libexif/exif-tag.c:118
msgid "GPS Satellites"
msgstr "Satelity GPS"

#: libexif/exif-tag.c:119
msgid ""
"Indicates the GPS satellites used for measurements. This tag can be used to "
"describe the number of satellites, their ID number, angle of elevation, "
"azimuth, SNR and other information in ASCII notation. The format is not "
"specified. If the GPS receiver is incapable of taking measurements, value of "
"the tag shall be set to NULL."
msgstr ""
"Określenie satelitów GPS użytych do pomiaru. Znacznik ten może opisywać "
"liczbę satelitów, ich numery identyfikacyjne, ich kąt podniesienia, azymut, "
"SNR i inne informacje w zapisie ASCII. Format nie jest w pełni określony. "
"Jeśli odbiornik GPS nie jest w stanie wykonać pomiaru, wartość tego "
"znacznika powinna wynosić NULL."

#: libexif/exif-tag.c:125
msgid "GPS Receiver Status"
msgstr "Stan odbiornika GPS"

#: libexif/exif-tag.c:126
msgid ""
"Indicates the status of the GPS receiver when the image is recorded. 'A' "
"means measurement is in progress, and 'V' means the measurement is "
"Interoperability."
msgstr ""
"Określenie stanu odbiornika GPS podczas zapisu zdjęcia. 'A' oznacza pomiar w "
"trakcie, 'V' oznacza pomiar współpracujący."

#: libexif/exif-tag.c:129
msgid "GPS Measurement Mode"
msgstr "Tryb pomiaru GPS"

#: libexif/exif-tag.c:130
msgid ""
"Indicates the GPS measurement mode. '2' means two-dimensional measurement "
"and '3' means three-dimensional measurement is in progress."
msgstr ""
"Określenie trybu pomiaru GPS. '2' oznacza wykonywanie pomiaru "
"dwuwymiarowego, '3' - pomiar trójwymiarowego."

#: libexif/exif-tag.c:133
msgid "Measurement Precision"
msgstr "Dokładność pomiaru"

#: libexif/exif-tag.c:134
msgid ""
"Indicates the GPS DOP (data degree of precision). An HDOP value is written "
"during two-dimensional measurement, and PDOP during three-dimensional "
"measurement."
msgstr ""
"Określenie GPS DOP (stopnia precyzji danych). W trakcie pomiaru "
"dwuwymiarowego zapisywana jest wartość HDOP, w trakcie trójwymiarowego - "
"PDOP."

#: libexif/exif-tag.c:137
msgid "Speed Unit"
msgstr "Jednostka prędkości"

#: libexif/exif-tag.c:138
msgid ""
"Indicates the unit used to express the GPS receiver speed of movement. 'K', "
"'M' and 'N' represent kilometers per hour, miles per hour, and knots."
msgstr ""
"Określenie jednostki używanej do wyrażenia prędkości ruchu odbiornika GPS. "
"'K', 'M' i 'N' oznaczają odpowiednio kilometry na godzinę, mile na godzinę i "
"węzły."

#: libexif/exif-tag.c:141
msgid "Speed of GPS Receiver"
msgstr "Prędkość odbiornika GPS"

#: libexif/exif-tag.c:142
msgid "Indicates the speed of GPS receiver movement."
msgstr "Określenie prędkości ruchu odbiornika GPS."

#: libexif/exif-tag.c:143
msgid "Reference for direction of movement"
msgstr "Odniesienie kierunku ruchu"

#: libexif/exif-tag.c:144
msgid ""
"Indicates the reference for giving the direction of GPS receiver movement. "
"'T' denotes true direction and 'M' is magnetic direction."
msgstr ""
"Określenie odniesienia wskazania kierunku ruchu odbiornika GPS. 'T' oznacza "
"kierunek prawdziwy, a 'M' - magnetyczny."

#: libexif/exif-tag.c:147
msgid "Direction of Movement"
msgstr "Kierunek ruchu"

#: libexif/exif-tag.c:148
msgid ""
"Indicates the direction of GPS receiver movement. The range of values is "
"from 0.00 to 359.99."
msgstr ""
"Określenie kierunku ruchu odbiornika GPS. Zakres wartości od 0.00 do 359.99."

#: libexif/exif-tag.c:150
msgid "GPS Image Direction Reference"
msgstr "Odniesienie kierunku wg GPS"

#: libexif/exif-tag.c:151
msgid ""
"Indicates the reference for giving the direction of the image when it is "
"captured. 'T' denotes true direction and 'M' is magnetic direction."
msgstr ""
"Określenie odniesienia wskazania kierunku zdjęcia w czasie jego robienia. "
"'T' oznacza kierunek prawdziwy, a 'M' - magnetyczny."

#: libexif/exif-tag.c:153
msgid "GPS Image Direction"
msgstr "Kierunek wg GPS"

#: libexif/exif-tag.c:154
msgid ""
"Indicates the direction of the image when it was captured. The range of "
"values is from 0.00 to 359.99."
msgstr ""
"Określenie kierunku zdjęcia w czasie jego robienia. Zakres wartości od 0.00 "
"do 359.99."

#: libexif/exif-tag.c:156
msgid "Geodetic Survey Data Used"
msgstr "Geodezyjny układ odniesienia"

#: libexif/exif-tag.c:157
msgid ""
"Indicates the geodetic survey data used by the GPS receiver. If the survey "
"data is restricted to Japan, the value of this tag is 'TOKYO' or 'WGS-84'. "
"If a GPS Info tag is recorded, it is strongly recommended that this tag be "
"recorded."
msgstr ""
"Określenie geodezyjnego układu odniesienia używanego przez odbiornik GPS. "
"Jeśli dane są ograniczone do Japonii, znacznik ten może mieć wartość 'TOKYO' "
"lub 'WGS-84'. Jeśli zapisany jest znacznik GPS Info, zdecydowanie zalecany "
"jest zapis tego znacznika."

#: libexif/exif-tag.c:161
msgid "Reference For Latitude of Destination"
msgstr "Odniesienie szerokości geograficznej obiektu"

#: libexif/exif-tag.c:162
msgid ""
"Indicates whether the latitude of the destination point is north or south "
"latitude. The ASCII value 'N' indicates north latitude, and 'S' is south "
"latitude."
msgstr ""
"Oznaczenie, czy szerokość geograficzna punktu docelowego jest północna, czy "
"południowa. Wartość ASCII 'N' oznacza szerokość północną, a 'S' południową."

#: libexif/exif-tag.c:165
msgid "Latitude of Destination"
msgstr "Szerokość geograficzna obiektu"

#: libexif/exif-tag.c:166
msgid ""
"Indicates the latitude of the destination point. The latitude is expressed "
"as three RATIONAL values giving the degrees, minutes, and seconds, "
"respectively. If latitude is expressed as degrees, minutes and seconds, a "
"typical format would be dd/1,mm/1,ss/1. When degrees and minutes are used "
"and, for example, fractions of minutes are given up to two decimal places, "
"the format would be dd/1,mmmm/100,0/1."
msgstr ""
"Określenie szerokości geograficznej punktu docelowego. Szerokość jest "
"wyrażona jako trzy wartości RATIONAL (wymierne) podające odpowiednio "
"stopnie, minuty i sekundy. Kiedy są wyrażone stopnie, minuty i sekundy, "
"format to dd/1,mm/1,ss/1. Kiedy są wyrażone stopnie i minuty oraz np. ułamki "
"minut są podane z dokładnością do dwóch miejsc po przecinku, format to dd/1,"
"mmmm/100,0/1."

#: libexif/exif-tag.c:173
msgid "Reference for Longitude of Destination"
msgstr "Odniesienie długości geograficznej obiektu"

#: libexif/exif-tag.c:174
msgid ""
"Indicates whether the longitude of the destination point is east or west "
"longitude. ASCII 'E' indicates east longitude, and 'W' is west longitude."
msgstr ""
"Określenie, czy długość geograficzna punktu docelowego jest wschodnia, czy "
"zachodnia. Wartość ASCII 'E' oznacza długość wschodnią, a 'W' zachodnią."

#: libexif/exif-tag.c:177
msgid "Longitude of Destination"
msgstr "Długość geograficzna obiektu"

#: libexif/exif-tag.c:178
msgid ""
"Indicates the longitude of the destination point. The longitude is expressed "
"as three RATIONAL values giving the degrees, minutes, and seconds, "
"respectively. If longitude is expressed as degrees, minutes and seconds, a "
"typical format would be ddd/1,mm/1,ss/1. When degrees and minutes are used "
"and, for example, fractions of minutes are given up to two decimal places, "
"the format would be ddd/1,mmmm/100,0/1."
msgstr ""
"Określenie długości geograficznej punktu docelowego. Szerokość jest wyrażona "
"jako trzy wartości RATIONAL (wymierne) podające odpowiednio stopnie, minuty "
"i sekundy. Kiedy są wyrażone stopnie, minuty i sekundy, format to dd/1,mm/1,"
"ss/1. Kiedy są wyrażone stopnie i minuty oraz np. ułamki minut są podane z "
"dokładnością do dwóch miejsc po przecinku, format to dd/1,mmmm/100,0/1."

#: libexif/exif-tag.c:186
msgid "Reference for Bearing of Destination"
msgstr "Odniesienie kierunku celu"

#: libexif/exif-tag.c:187
msgid ""
"Indicates the reference used for giving the bearing to the destination "
"point. 'T' denotes true direction and 'M' is magnetic direction."
msgstr ""
"Określenie odniesienia wskazania kierunku punktu docelowego. 'T' oznacza "
"kierunek prawdziwy, a 'M' - magnetyczny."

#: libexif/exif-tag.c:190
msgid "Bearing of Destination"
msgstr "Kierunek celu"

#: libexif/exif-tag.c:191
msgid ""
"Indicates the bearing to the destination point. The range of values is from "
"0.00 to 359.99."
msgstr ""
"Określenie kierunku punktu docelowego. Zakres wartości od 0.00 do 359.99."

#: libexif/exif-tag.c:193
msgid "Reference for Distance to Destination"
msgstr "Odniesienie odległości od celu"

#: libexif/exif-tag.c:194
msgid ""
"Indicates the unit used to express the distance to the destination point. "
"'K', 'M' and 'N' represent kilometers, miles and nautical miles."
msgstr ""
"Określenie jednostki użytej do wyrażenia odległości od punktu docelowego. "
"'K', 'M' i 'N' określają odpowiednio kilometry, mile i mile morskie."

#: libexif/exif-tag.c:197
msgid "Distance to Destination"
msgstr "Odległość od celu"

#: libexif/exif-tag.c:198
msgid "Indicates the distance to the destination point."
msgstr "Określenie odległości od punktu docelowego."

#: libexif/exif-tag.c:199
msgid "Name of GPS Processing Method"
msgstr "Nazwa metody przetwarzania GPS"

#: libexif/exif-tag.c:200
msgid ""
"A character string recording the name of the method used for location "
"finding. The first byte indicates the character code used, and this is "
"followed by the name of the method. Since the Type is not ASCII, NULL "
"termination is not necessary."
msgstr ""
"Łańcuch znaków zapisujący nazwę metody użytej do określenia lokalizacji. "
"Pierwszy bajt określa użyty kod znaków, następne - nazwę metody. Ponieważ "
"typ znacznika nie jest ASCII, zakończenie NULL nie jest wymagane."

#: libexif/exif-tag.c:205
msgid "Name of GPS Area"
msgstr "Nazwa obszaru GPS"

#: libexif/exif-tag.c:206
msgid ""
"A character string recording the name of the GPS area. The first byte "
"indicates the character code used, and this is followed by the name of the "
"GPS area. Since the Type is not ASCII, NULL termination is not necessary."
msgstr ""
"Łańcuch znaków zapisujący nazwę obszaru GPS. Pierwszy bajt określa użyty kod "
"znaków, następne - nazwę metody. Ponieważ typ znacznika nie jest ASCII, "
"zakończenie NULL nie jest wymagane."

#: libexif/exif-tag.c:210
msgid "GPS Date"
msgstr "Data GPS"

#: libexif/exif-tag.c:211
msgid ""
"A character string recording date and time information relative to UTC "
"(Coordinated Universal Time). The format is \"YYYY:MM:DD\". The length of "
"the string is 11 bytes including NULL."
msgstr ""
"Łańcuch znaków zapisujący informacje o dacie i czasie względem UTC "
"(Coordinated Universal Time). Format to \"RRRR:MM:DD\". Długość łańcucha to "
"11 bajtów wraz ze znakiem NULL."

#: libexif/exif-tag.c:215
msgid "GPS Differential Correction"
msgstr "Poprawka różnicowa GPS"

#: libexif/exif-tag.c:216
msgid ""
"Indicates whether differential correction is applied to the GPS receiver."
msgstr ""
"Określenie, czy do pomiaru odbiornika GPS została zastosowana poprawka "
"różnicowa."

#: libexif/exif-tag.c:220
msgid "New Subfile Type"
msgstr "Typ nowego podpliku"

#: libexif/exif-tag.c:220
msgid "A general indication of the kind of data contained in this subfile."
msgstr "Ogólne oznaczenie rodzaju danych zawartych w tym podpliku."

#: libexif/exif-tag.c:222
msgid "Image Width"
msgstr "Szerokość obrazu"

#: libexif/exif-tag.c:223
msgid ""
"The number of columns of image data, equal to the number of pixels per row. "
"In JPEG compressed data a JPEG marker is used instead of this tag."
msgstr ""
"Liczba kolumn danych obrazu, różna liczbie pikseli w wierszu. W danych "
"skompresowanych algorytmem JPEG zamiast tego znacznika używany jest znacznik "
"JPEG."

#: libexif/exif-tag.c:227
msgid "Image Length"
msgstr "Długość obrazu"

#: libexif/exif-tag.c:228
msgid ""
"The number of rows of image data. In JPEG compressed data a JPEG marker is "
"used instead of this tag."
msgstr ""
"Liczba wierszy danych obrazu. W danych skompresowanych algorytmem JPEG "
"zamiast tego znacznika używany jest znacznik JPEG."

#: libexif/exif-tag.c:231
msgid "Bits per Sample"
msgstr "Bitów na próbkę"

#: libexif/exif-tag.c:232
msgid ""
"The number of bits per image component. In this standard each component of "
"the image is 8 bits, so the value for this tag is 8. See also "
"<SamplesPerPixel>. In JPEG compressed data a JPEG marker is used instead of "
"this tag."
msgstr ""
"Liczba bitów na składową obrazu. W tym standardzie każda składowa obrazu ma "
"8 bitów, więc wartość tego znacznika to 8. Patrz także <SamplesPerPixel>. W "
"danych skompresowanych algorytmem JPEG zamiast tego znacznika używany jest "
"znacznik JPEG."

#: libexif/exif-tag.c:237
msgid "Compression"
msgstr "Kompresja"

#: libexif/exif-tag.c:238
msgid ""
"The compression scheme used for the image data. When a primary image is JPEG "
"compressed, this designation is not necessary and is omitted. When "
"thumbnails use JPEG compression, this tag value is set to 6."
msgstr ""
"Algorytm kompresji użyty dla danych obrazu. Jeśli główny obraz jest "
"skompresowany algorytmem JPEG, to oznaczenie nie jest potrzebne i jest "
"pomijane. Jeśli miniaturki używają kompresji JPEG, ten znacznik ma wartość 6."

#: libexif/exif-tag.c:244
msgid "Photometric Interpretation"
msgstr "Interpretacja fotometryczna"

#: libexif/exif-tag.c:245
msgid ""
"The pixel composition. In JPEG compressed data a JPEG marker is used instead "
"of this tag."
msgstr ""
"Składowe pikseli. W danych skompresowanych algorytmem JPEG zamiast tego "
"znacznika używany jest znacznik JPEG."

#: libexif/exif-tag.c:249
msgid "Fill Order"
msgstr "Kolejność wypełniania"

#: libexif/exif-tag.c:251
msgid "Document Name"
msgstr "Nazwa dokumentu"

#: libexif/exif-tag.c:253
msgid "Image Description"
msgstr "Opis obrazu"

#: libexif/exif-tag.c:254
msgid ""
"A character string giving the title of the image. It may be a comment such "
"as \"1988 company picnic\" or the like. Two-bytes character codes cannot be "
"used. When a 2-bytes code is necessary, the Exif Private tag <UserComment> "
"is to be used."
msgstr ""
"Łańcuch znaków nadający obrazowi tytuł. Może być komentarzem takim jak "
"\"piknik firmowy 1988\" lub podobnym. Nie można używać dwubajtowych kodów "
"znaków. Jeśli dwubajtowe kody znaków są potrzebne, należy użyć znacznika "
"Exif Private <UserComment>."

#: libexif/exif-tag.c:260
msgid "Manufacturer"
msgstr "Producent"

#: libexif/exif-tag.c:261
msgid ""
"The manufacturer of the recording equipment. This is the manufacturer of the "
"DSC, scanner, video digitizer or other equipment that generated the image. "
"When the field is left blank, it is treated as unknown."
msgstr ""
"Producent urządzenia nagrywającego. Jest to producent DSC, skanera, "
"digitalizera albo innego urządzenia, które wygenerowało obraz. Jeśli to pole "
"jest puste, jest traktowane jako nieznane."

#: libexif/exif-tag.c:267
msgid "Model"
msgstr "Model"

#: libexif/exif-tag.c:268
msgid ""
"The model name or model number of the equipment. This is the model name or "
"number of the DSC, scanner, video digitizer or other equipment that "
"generated the image. When the field is left blank, it is treated as unknown."
msgstr ""
"Nazwa lub numer modelu urządzenia. Jest to nazwa modelu lub numer DSC, "
"skanera, digitalizera albo innego urządzenia, które wygenerowało obraz. "
"Jeśli to pole jest puste, jest traktowane jako nieznane."

#: libexif/exif-tag.c:273
msgid "Strip Offsets"
msgstr "Przesunięcia pasów"

#: libexif/exif-tag.c:274
msgid ""
"For each strip, the byte offset of that strip. It is recommended that this "
"be selected so the number of strip bytes does not exceed 64 Kbytes. With "
"JPEG compressed data this designation is not needed and is omitted. See also "
"<RowsPerStrip> and <StripByteCounts>."
msgstr ""
"Bajtowe przesunięcie pasa dla każdego pasa. Zaleca się takie dobranie tej "
"wartości, by liczba bajtów pasa nie przekraczała 64kB. W danych "
"skompresowanych algorytmem JPEG to oznaczenie nie jest potrzebne i jest "
"pomijane. Patrz także <RowsPerStrip> i <StripByteCount>."

#: libexif/exif-tag.c:280
msgid "Orientation"
msgstr "Orientacja"

#: libexif/exif-tag.c:281
msgid "The image orientation viewed in terms of rows and columns."
msgstr "Orientacja obrazu widziana w kategoriach wierszy i kolumn."

#: libexif/exif-tag.c:284
msgid "Samples per Pixel"
msgstr "Próbek na piksel"

#: libexif/exif-tag.c:285
msgid ""
"The number of components per pixel. Since this standard applies to RGB and "
"YCbCr images, the value set for this tag is 3. In JPEG compressed data a "
"JPEG marker is used instead of this tag."
msgstr ""
"Liczba składowych na piksel. Ponieważ ten standard odnosi się do obrazów RGB "
"i YCbCr, wartość tego znacznika wynosi 3. W danych skompresowanych "
"algorytmem JPEG zamiast tego znacznika używany jest znacznik JPEG."

#: libexif/exif-tag.c:290
msgid "Rows per Strip"
msgstr "Wierszy na pas"

#: libexif/exif-tag.c:291
msgid ""
"The number of rows per strip. This is the number of rows in the image of one "
"strip when an image is divided into strips. With JPEG compressed data this "
"designation is not needed and is omitted. See also <StripOffsets> and "
"<StripByteCounts>."
msgstr ""
"Liczba wierszy na pas. Jest to liczba wierszy w obrazie jednego pasa kiedy "
"obraz jest podzielony na pasy. W danych skompresowanych algorytmem JPEG to "
"oznaczenie nie jest potrzebne i jest pomijane. Patrz także <StripOffsets> i "
"<StripByteCounts>."

#: libexif/exif-tag.c:297
msgid "Strip Byte Count"
msgstr "Liczba bajtów na pas"

#: libexif/exif-tag.c:298
msgid ""
"The total number of bytes in each strip. With JPEG compressed data this "
"designation is not needed and is omitted."
msgstr ""
"Całkowita liczba bajtów w każdym pasie. W danych skompresowanych algorytmem "
"JPEG to oznaczenie nie jest potrzebne i jest pomijane."

#: libexif/exif-tag.c:301
msgid "X-Resolution"
msgstr "Rozdzielczość X"

#: libexif/exif-tag.c:302
msgid ""
"The number of pixels per <ResolutionUnit> in the <ImageWidth> direction. "
"When the image resolution is unknown, 72 [dpi] is designated."
msgstr ""
"Liczba pikseli na jednostkę rozdzielczości (<ResolutionUnit>) w kierunku "
"szerokości (<ImageWidth>). Kiedy rozdzielczość obrazu jest nieznana, "
"przyjmuje się 72 [dpi]."

#: libexif/exif-tag.c:306
msgid "Y-Resolution"
msgstr "Rozdzielczość Y"

#: libexif/exif-tag.c:307
msgid ""
"The number of pixels per <ResolutionUnit> in the <ImageLength> direction. "
"The same value as <XResolution> is designated."
msgstr ""
"Liczba pikseli na jednostkę rozdzielczości (<ResolutionUnit>) w kierunku "
"długości (<ImageLength>). Zakładana jest taka sama wartość jak <XResolution>."

#: libexif/exif-tag.c:311
msgid "Planar Configuration"
msgstr "Konfiguracja powierzchni"

#: libexif/exif-tag.c:312
msgid ""
"Indicates whether pixel components are recorded in a chunky or planar "
"format. In JPEG compressed files a JPEG marker is used instead of this tag. "
"If this field does not exist, the TIFF default of 1 (chunky) is assumed."
msgstr ""
"Oznaczenie, czy składowe pikseli są zapisane w formacie blokowym czy "
"płaskim. W plikach skompresowanych algorytmem JPEG zamiast tego znacznika "
"używany jest znacznik JPEG. Jeśli to pole nie istnieje, domyślne dla TIFF "
"jest 1 (blokowy)."

#: libexif/exif-tag.c:317
msgid "Resolution Unit"
msgstr "Jednostka rozdzielczości"

#: libexif/exif-tag.c:318
msgid ""
"The unit for measuring <XResolution> and <YResolution>. The same unit is "
"used for both <XResolution> and <YResolution>. If the image resolution is "
"unknown, 2 (inches) is designated."
msgstr ""
"Jednostka do wyrażania <XResolution> i <YResolution>. Dla obu wielkości "
"używana jest ta sama jednostka. Jeśli rozdzielczość jest nieznana, "
"przyjmowane jest 2 (cale)."

#: libexif/exif-tag.c:323
msgid "Transfer Function"
msgstr "Funkcja przejścia"

#: libexif/exif-tag.c:324
msgid ""
"A transfer function for the image, described in tabular style. Normally this "
"tag is not necessary, since color space is specified in the color space "
"information tag (<ColorSpace>)."
msgstr ""
"Funkcja przejścia dla obrazu, opisana w postaci tabeli. Zwykle ten znacznik "
"nie jest potrzebny, ponieważ przestrzeń kolorów podana jest w znaczniku "
"informacji o przestrzeni kolorów (<ColorSpace>)."

#: libexif/exif-tag.c:328
msgid "Software"
msgstr "Oprogramowanie"

#: libexif/exif-tag.c:329
msgid ""
"This tag records the name and version of the software or firmware of the "
"camera or image input device used to generate the image. The detailed format "
"is not specified, but it is recommended that the example shown below be "
"followed. When the field is left blank, it is treated as unknown."
msgstr ""
"Ten znacznik przechowuje nazwę i wersję oprogramowania lub firmware kamery "
"albo innego urządzenia wejściowego obrazu użytego do wygenerowania obrazu. "
"Szczegółowy format nie jest określony, ale zaleca się naśladowanie "
"poniższego przykładu. Jeśli pole jest puste, jest traktowane jako nieznane."

#: libexif/exif-tag.c:336
msgid "Date and Time"
msgstr "Data i czas"

#: libexif/exif-tag.c:337
msgid ""
"The date and time of image creation. In this standard (EXIF-2.1) it is the "
"date and time the file was changed."
msgstr ""
"Data i czas stworzenia obrazu. W tym standardzie (EXIF-2.1) jest to data i "
"czas zmiany pliku."

#: libexif/exif-tag.c:340
msgid "Artist"
msgstr "Autor"

#: libexif/exif-tag.c:341
msgid ""
"This tag records the name of the camera owner, photographer or image "
"creator. The detailed format is not specified, but it is recommended that "
"the information be written as in the example below for ease of "
"Interoperability. When the field is left blank, it is treated as unknown."
msgstr ""
"Ten znacznik przechowuje nazwę właściciela aparatu, fotografa lub twórcy "
"obrazu. Szczegółowy format nie jest określony, ale zaleca się naśladowanie "
"poniższego przykładu dla ułatwienia współpracy. Jeśli pole jest puste, jest "
"traktowane jako nieznane."

#: libexif/exif-tag.c:347 libexif/pentax/mnote-pentax-tag.c:113
msgid "White Point"
msgstr "Biały punkt"

#: libexif/exif-tag.c:348
msgid ""
"The chromaticity of the white point of the image. Normally this tag is not "
"necessary, since color space is specified in the color space information tag "
"(<ColorSpace>)."
msgstr ""
"Barwa białego punktu obrazu. Zwykle ten znacznik nie jest potrzebny, "
"ponieważ przestrzeń kolorów podana jest w znaczniku informacji o przestrzeni "
"kolorów (<ColorSpace>)."

#: libexif/exif-tag.c:353
msgid "Primary Chromaticities"
msgstr "Barwy główne"

#: libexif/exif-tag.c:354
msgid ""
"The chromaticity of the three primary colors of the image. Normally this tag "
"is not necessary, since color space is specified in the color space "
"information tag (<ColorSpace>)."
msgstr ""
"Barwa trzech głównych kolorów obrazu. Zwykle ten znacznik nie jest "
"potrzebny, ponieważ przestrzeń kolorów podana jest w znaczniku informacji o "
"przestrzeni kolorów (<ColorSpace>)."

#: libexif/exif-tag.c:359
msgid "Defined by Adobe Corporation to enable TIFF Trees within a TIFF file."
msgstr ""
"Zdefiniowane przez Adobe Corporation, aby pozwolić na drzewa TIFF w plikach "
"TIFF."

#: libexif/exif-tag.c:362
msgid "Transfer Range"
msgstr "Zakres przejścia"

#: libexif/exif-tag.c:366
msgid "JPEG Interchange Format"
msgstr "Format JPEG"

#: libexif/exif-tag.c:367
msgid ""
"The offset to the start byte (SOI) of JPEG compressed thumbnail data. This "
"is not used for primary image JPEG data."
msgstr ""
"Położenie początkowego bajtu (SOI) danych miniaturki skompresowanej JPEG. "
"Nie jest używane dla danych JPEG głównego obrazu."

#: libexif/exif-tag.c:372
msgid "JPEG Interchange Format Length"
msgstr "Długość formatu JPEG"

#: libexif/exif-tag.c:373
msgid ""
"The number of bytes of JPEG compressed thumbnail data. This is not used for "
"primary image JPEG data. JPEG thumbnails are not divided but are recorded as "
"a continuous JPEG bitstream from SOI to EOI. Appn and COM markers should not "
"be recorded. Compressed thumbnails must be recorded in no more than 64 "
"Kbytes, including all other data to be recorded in APP1."
msgstr ""
"Liczba bajtów danych miniaturki skompresowanej JPEG. Nie jest używana dla "
"danych JPEG głównego obrazu. Miniaturki JPEG nie są dzielone, ale zapisywane "
"jako ciągły strumień JPEG od SOI do EOI. Znaczniki Appn i COM nie powinny "
"być używane. Skompresowane miniaturki muszą być zapisane w najwyżej 64kB, "
"włącznie ze wszystkimi innymi danymi zapisanymi w APP1."

#: libexif/exif-tag.c:382
msgid "YCbCr Coefficients"
msgstr "Współczynniki YCbCr"

#: libexif/exif-tag.c:383
msgid ""
"The matrix coefficients for transformation from RGB to YCbCr image data. No "
"default is given in TIFF; but here the value given in \"Color Space "
"Guidelines\", is used as the default. The color space is declared in a color "
"space information tag, with the default being the value that gives the "
"optimal image characteristics Interoperability this condition."
msgstr ""
"Macierz współczynników przekształcenia danych obrazu z RGB do YCbCr. Dla "
"TIFF nie ma wartości domyślnych, ale wartości podane w \"Color Space "
"Guidelines\" są używane jako domyślne. Przestrzeń kolorów jest określona w "
"znaczniku informacji o przestrzeni kolorów z wartością domyślną będącą tą, "
"która daje optymalną współpracę charakterystyki obrazu w danym przypadku."

#: libexif/exif-tag.c:392
msgid "YCbCr Sub-Sampling"
msgstr "Podpróbkowanie YCbCr"

#: libexif/exif-tag.c:393
msgid ""
"The sampling ratio of chrominance components in relation to the luminance "
"component. In JPEG compressed data a JPEG marker is used instead of this tag."
msgstr ""
"Współczynnik próbkowania składowych chrominancji w stosunku do składowej "
"luminancji. W danych skompresowanych algorytmem JPEG zamiast tego znacznika "
"używany jest znacznik JPEG."

#: libexif/exif-tag.c:398
msgid "YCbCr Positioning"
msgstr "Rozmieszczenie YCbCr"

#: libexif/exif-tag.c:399
msgid ""
"The position of chrominance components in relation to the luminance "
"component. This field is designated only for JPEG compressed data or "
"uncompressed YCbCr data. The TIFF default is 1 (centered); but when Y:Cb:Cr "
"= 4:2:2 it is recommended in this standard that 2 (co-sited) be used to "
"record data, in order to improve the image quality when viewed on TV "
"systems. When this field does not exist, the reader shall assume the TIFF "
"default. In the case of Y:Cb:Cr = 4:2:0, the TIFF default (centered) is "
"recommended. If the reader does not have the capability of supporting both "
"kinds of <YCbCrPositioning>, it shall follow the TIFF default regardless of "
"the value in this field. It is preferable that readers be able to support "
"both centered and co-sited positioning."
msgstr ""
"Rozmieszczenie składowych chrominancji w stosunku do składowej luminancji. "
"To pole ma znaczenie tylko dla danych skompresowanych algorytmem JPEG lub "
"nieskompresowanych danych YCbCr. Domyślne dla TIFF jest 1 (wyśrodkowane); "
"ale kiedy Y:Cb:Cr = 4:2:2, zaleca się w tym standardzie 2 (położone razem) w "
"celu poprawienia jakości obrazu w przypadku oglądania na telewizorze. Kiedy "
"to pole nie istnieje, czytający powinien założyć wartość domyślną dla TIFF. "
"W przypadku Y:Cb:Cr = 4:2:0, zalecana jest wartość domyślna dla TIFF "
"(wyśrodkowane). Jeśli czytający nie ma możliwości obsługi obu rodzajów "
"rozmieszczenia, powinien używać domyślnej wartości TIFF niezależnie od "
"wartości tego pola. Zaleca się, żeby czytający byli w stanie obsłużyć oba "
"rodzaje rozmieszczenia."

#: libexif/exif-tag.c:414
msgid "Reference Black/White"
msgstr "Czerń/biel odniesienia"

#: libexif/exif-tag.c:415
msgid ""
"The reference black point value and reference white point value. No defaults "
"are given in TIFF, but the values below are given as defaults here. The "
"color space is declared in a color space information tag, with the default "
"being the value that gives the optimal image characteristics "
"Interoperability these conditions."
msgstr ""
"Wartości czarnego i białego punktu odniesienia. W formacie TIFF nie ma "
"wartości domyślnych, ale poniższe są podane tutaj jako domyślne. Przestrzeń "
"kolorów jest określona w znaczniku informacji o przestrzeni kolorów, z "
"wartością domyślną dającą optymalną charakterystykę obrazu w danych "
"warunkach."

#: libexif/exif-tag.c:423
msgid "XML Packet"
msgstr "Pakiet XML"

#: libexif/exif-tag.c:423
msgid "XMP Metadata"
msgstr "Metadane XML"

#: libexif/exif-tag.c:438 libexif/exif-tag.c:784
msgid "CFA Pattern"
msgstr "Wzór CFA"

#: libexif/exif-tag.c:439 libexif/exif-tag.c:785
msgid ""
"Indicates the color filter array (CFA) geometric pattern of the image sensor "
"when a one-chip color area sensor is used. It does not apply to all sensing "
"methods."
msgstr ""
"Oznaczenie wzoru geometrycznego CFA (color filter array - tablicy filtrów "
"kolorów) czujnika obrazu w przypadku użycia jednoukładowego czujnika obszaru "
"koloru. Nie odnosi się to do wszystkich metod próbkowania."

#: libexif/exif-tag.c:443
msgid "Battery Level"
msgstr "Poziom baterii"

#: libexif/exif-tag.c:444
msgid "Copyright"
msgstr "Prawa autorskie"

#: libexif/exif-tag.c:445
msgid ""
"Copyright information. In this standard the tag is used to indicate both the "
"photographer and editor copyrights. It is the copyright notice of the person "
"or organization claiming rights to the image. The Interoperability copyright "
"statement including date and rights should be written in this field; e.g., "
"\"Copyright, John Smith, 19xx. All rights reserved.\". In this standard the "
"field records both the photographer and editor copyrights, with each "
"recorded in a separate part of the statement. When there is a clear "
"distinction between the photographer and editor copyrights, these are to be "
"written in the order of photographer followed by editor copyright, separated "
"by NULL (in this case, since the statement also ends with a NULL, there are "
"two NULL codes) (see example 1). When only the photographer is given, it is "
"terminated by one NULL code (see example 2). When only the editor copyright "
"is given, the photographer copyright part consists of one space followed by "
"a terminating NULL code, then the editor copyright is given (see example 3). "
"When the field is left blank, it is treated as unknown."
msgstr ""
"Informacje o prawach autorskich. Jest to standardowy znacznik używany do "
"określenia praw autorskich zarówno fotografa, jak i redaktora. Jest to "
"informacja o osobie lub organizacji mającej prawa do obrazu. Standardowe "
"oświadczenie o prawach autorskich wraz z datą i prawami powinno być zapisane "
"w tym polu, np. \"Copyright, John Smith, 19xx. All rights reserved.\". W tym "
"standardzie pola opisują prawa zarówno fotografa, jak i redaktora, z których "
"każdy jest opisywany w oddzielnej części oświadczenia. Jeśli jest jasne "
"rozróżnienie między prawami fotografa i redaktora, powinny być zapisane w "
"kolejności najpierw fotograf, a następnie redaktor, oddzielone znakiem NULL "
"(w tym przypadku, jeśli oświadczenie także kończy się znakiem NULL, powinny "
"być dwa kody NULL; p. przykład 1). Jeśli podano tylko fotografa, jest on "
"kończony kodem NULL (p. przykład 2). Jeśli podano tylko prawa redaktora, "
"część przeznaczona dla fotografa składa się z jednej spacji i następującego "
"po niej kodu NULL, a następnie podane są prawa redaktora (p. przykład 3). "
"Jeśli pole jest puste, jest traktowane jako nieznane."

#: libexif/exif-tag.c:467
msgid "Exposure time, given in seconds (sec)."
msgstr "Czas ekspozycji podany w sekundach (sek)."

#: libexif/exif-tag.c:469 libexif/pentax/mnote-pentax-tag.c:79
msgid "F-Number"
msgstr "Liczba F"

#: libexif/exif-tag.c:470
msgid "The F number."
msgstr "Liczba F."

#: libexif/exif-tag.c:475
msgid "Image Resources Block"
msgstr "Blok zasobów obrazu"

#: libexif/exif-tag.c:477
msgid ""
"A pointer to the Exif IFD. Interoperability, Exif IFD has the same structure "
"as that of the IFD specified in TIFF. ordinarily, however, it does not "
"contain image data as in the case of TIFF."
msgstr ""
"Wskaźnik na Exif IFD. Exif IFD ma tę samą strukturę co IFD określone w TIFF, "
"oczywiście nie zawiera jednak danych obrazu jak w przypadku pliku TIFF."

#: libexif/exif-tag.c:485
msgid "Exposure Program"
msgstr "Program ekspozycji"

#: libexif/exif-tag.c:486
msgid ""
"The class of the program used by the camera to set exposure when the picture "
"is taken."
msgstr ""
"Klasa programu użytego przez aparat do ustawienia ekspozycji przy robieniu "
"zdjęcia."

#: libexif/exif-tag.c:490
msgid "Spectral Sensitivity"
msgstr "Czułość widmowa"

#: libexif/exif-tag.c:491
msgid ""
"Indicates the spectral sensitivity of each channel of the camera used. The "
"tag value is an ASCII string compatible with the standard developed by the "
"ASTM Technical Committee."
msgstr ""
"Oznaczenie czułości widmowej każdego kanału używanego przez aparat. Wartość "
"znacznika to łańcuch znaków ASCII kompatybilny ze standardem stworzonym "
"przez ASTM Technical Committee."

#: libexif/exif-tag.c:496
msgid "GPS Info IFD Pointer"
msgstr "Wkaźnik IFD informacji GPS"

#: libexif/exif-tag.c:497
msgid ""
"A pointer to the GPS Info IFD. The Interoperability structure of the GPS "
"Info IFD, like that of Exif IFD, has no image data."
msgstr ""
"Wskaźnik na GPS Info IFD. Struktura GPS Info IFD jest taka, jak Exif IFD, "
"ale bez danych obrazu."

#: libexif/exif-tag.c:503
msgid "ISO Speed Ratings"
msgstr "Oszacowania szybkości ISO"

#: libexif/exif-tag.c:504
msgid ""
"Indicates the ISO Speed and ISO Latitude of the camera or input device as "
"specified in ISO 12232."
msgstr ""
"Określenie szybkości ISO i szerokości ISO aparatu lub urządzenia wejściowego "
"zgodne ze specyfikacją ISO 12232."

#: libexif/exif-tag.c:507
msgid "Opto-Electronic Conversion Function"
msgstr "Funkcja przekształcenia optoelektronicznego"

#: libexif/exif-tag.c:508
msgid ""
"Indicates the Opto-Electronic Conversion Function (OECF) specified in ISO "
"14524. <OECF> is the relationship between the camera optical input and the "
"image values."
msgstr ""
"Określenie funkcji konwersji optoelektrycznej (OECF - Opto-Electric "
"Conversion Function) opisanej w ISO 14524. <OECF> to powiązanie między "
"wejściem optycznym aparatu a wartościami obrazu."

#: libexif/exif-tag.c:513
msgid "Time Zone Offset"
msgstr "Przesunięcie strefy czasowej"

#: libexif/exif-tag.c:514
msgid "Encodes time zone of camera clock relative to GMT."
msgstr "Zapis strefy czasowej zegara aparatu względem GMT."

#: libexif/exif-tag.c:515
msgid "Exif Version"
msgstr "Wersja Exif"

#: libexif/exif-tag.c:516
msgid ""
"The version of this standard supported. Nonexistence of this field is taken "
"to mean nonconformance to the standard."
msgstr ""
"Obsługiwana wersja tego standardu. Brak tego pola jest uznawany za "
"niezgodność ze standardem."

#: libexif/exif-tag.c:520
msgid "Date and Time (Original)"
msgstr "Data i czas (oryginału)"

#: libexif/exif-tag.c:521
msgid ""
"The date and time when the original image data was generated. For a digital "
"still camera the date and time the picture was taken are recorded."
msgstr ""
"Data i czas wygenerowania oryginalnych danych obrazu. Dla aparatu cyfrowego "
"zapisywana jest data i czas zrobienia zdjęcia."

#: libexif/exif-tag.c:526
msgid "Date and Time (Digitized)"
msgstr "Data i czas (obrazu cyfrowego)"

#: libexif/exif-tag.c:527
msgid "The date and time when the image was stored as digital data."
msgstr "Data i czas zapisania obrazu jako danych cyfrowych. "

#: libexif/exif-tag.c:530
msgid "Components Configuration"
msgstr "Konfiguracja składowych"

#: libexif/exif-tag.c:531
msgid ""
"Information specific to compressed data. The channels of each component are "
"arranged in order from the 1st component to the 4th. For uncompressed data "
"the data arrangement is given in the <PhotometricInterpretation> tag. "
"However, since <PhotometricInterpretation> can only express the order of Y, "
"Cb and Cr, this tag is provided for cases when compressed data uses "
"components other than Y, Cb, and Cr and to enable support of other sequences."
msgstr ""
"Informacje specyficzne dla skompresowanych danych. Kanały każdej składowej "
"są układane w kolejności od 1. do 4. Dla danych nieskompresowanych ułożenie "
"danych jest podane w znaczniku <PhotometricInterpretation>. Jednak ponieważ "
"<PhotometricInterpretation> może wyrazić jedynie kolejność Y, Cb i Cr, ten "
"znacznik został dodany dla przypadków, kiedy skompresowane dane używają "
"składowych innych niż Y, Cb i Cr oraz aby umożliwić obsługę innych sekwencji."

#: libexif/exif-tag.c:541
msgid "Compressed Bits per Pixel"
msgstr "Skompresowane bity na piksel"

#: libexif/exif-tag.c:542
msgid ""
"Information specific to compressed data. The compression mode used for a "
"compressed image is indicated in unit bits per pixel."
msgstr ""
"Informacja specyficzna dla skompresowanych danych. Rodzaj kompresji użyty "
"dla skompresowanego obrazu jest określony w jednostkach bitów na piksel."

#: libexif/exif-tag.c:546 libexif/olympus/mnote-olympus-tag.c:123
msgid "Shutter Speed"
msgstr "Szybkość migawki"

#: libexif/exif-tag.c:547
msgid ""
"Shutter speed. The unit is the APEX (Additive System of Photographic "
"Exposure) setting."
msgstr ""
"Szybkość migawki. Jednostką jest ustawienie APEX (Additive System of "
"Photographic Exposure)."

#: libexif/exif-tag.c:551
msgid "The lens aperture. The unit is the APEX value."
msgstr "Przysłona obiektywu. Jednostką jest wartość APEX."

#: libexif/exif-tag.c:553
msgid "Brightness"
msgstr "Jasność"

#: libexif/exif-tag.c:554
msgid ""
"The value of brightness. The unit is the APEX value. Ordinarily it is given "
"in the range of -99.99 to 99.99."
msgstr ""
"Wartość jasności. Jednostką jest wartość APEX. Zwykle jest podana w "
"przedziale od -99.99 do 99.99."

#: libexif/exif-tag.c:558
msgid "Exposure Bias"
msgstr "Odchylenie ekspozycji"

#: libexif/exif-tag.c:559
msgid ""
"The exposure bias. The units is the APEX value. Ordinarily it is given in "
"the range of -99.99 to 99.99."
msgstr ""
"Odchylenie ekspozycji. Jednostką jest wartość APEX. Zwykle jest podana w "
"przedziale od -99.99 do 99.99."

#: libexif/exif-tag.c:562
msgid "Maximum Aperture Value"
msgstr "Maksymalna wartość przysłony"

#: libexif/exif-tag.c:563
msgid ""
"The smallest F number of the lens. The unit is the APEX value. Ordinarily it "
"is given in the range of 00.00 to 99.99, but it is not limited to this range."
msgstr ""
"Najmniejsza liczba F obiektywu. Jednostką jest wartość APEX. Zwykle jest "
"podana w przedziale od 00.00 do 99.99, ale nie ma ograniczenia do tego "
"zakresu."

#: libexif/exif-tag.c:568
msgid "Subject Distance"
msgstr "Odległość obiektu"

#: libexif/exif-tag.c:569
msgid "The distance to the subject, given in meters."
msgstr "Odległość obiektu podana w metrach"

#: libexif/exif-tag.c:572
msgid "The metering mode."
msgstr "Tryb pomiaru."

#: libexif/exif-tag.c:574
msgid "Light Source"
msgstr "Źródło światła"

#: libexif/exif-tag.c:575
msgid "The kind of light source."
msgstr "Rodzaj źródła światła."

#: libexif/exif-tag.c:578
msgid ""
"This tag is recorded when an image is taken using a strobe light (flash)."
msgstr ""
"Ten znacznik jest zapisywany kiedy zdjęcie było robione z użyciem światła "
"stroboskopowego (flesza)."

#: libexif/exif-tag.c:582
msgid ""
"The actual focal length of the lens, in mm. Conversion is not made to the "
"focal length of a 35 mm film camera."
msgstr ""
"Rzeczywista ogniskowa obiektywu w mm, bez przekształcenia do ogniskowej dla "
"aparatu na film 35 mm."

#: libexif/exif-tag.c:585
msgid "Subject Area"
msgstr "Obszar obiektu"

#: libexif/exif-tag.c:586
msgid ""
"This tag indicates the location and area of the main subject in the overall "
"scene."
msgstr ""
"Ten znacznik określa położenie i obszar głównego obiektu na całej scenie."

#: libexif/exif-tag.c:590
msgid "TIFF/EP Standard ID"
msgstr "Standardowy ID TIFF/EP"

#: libexif/exif-tag.c:591
msgid "Maker Note"
msgstr "Uwaga producenta"

#: libexif/exif-tag.c:592
msgid ""
"A tag for manufacturers of Exif writers to record any desired information. "
"The contents are up to the manufacturer."
msgstr ""
"Znacznik dla producentów urządzeń zapisujących Exif do zapisywania dowolnie "
"wybranych informacji. Zawartość zależy od producenta."

#: libexif/exif-tag.c:595
msgid "User Comment"
msgstr "Komentarz użytkownika"

#: libexif/exif-tag.c:596
msgid ""
"A tag for Exif users to write keywords or comments on the image besides "
"those in <ImageDescription>, and without the character code limitations of "
"the <ImageDescription> tag. The character code used in the <UserComment> tag "
"is identified based on an ID code in a fixed 8-byte area at the start of the "
"tag data area. The unused portion of the area is padded with NULL (\"00.h"
"\"). ID codes are assigned by means of registration. The designation method "
"and references for each character code are defined in the specification. The "
"value of CountN is determined based on the 8 bytes in the character code "
"area and the number of bytes in the user comment part. Since the TYPE is not "
"ASCII, NULL termination is not necessary. The ID code for the <UserComment> "
"area may be a Defined code such as JIS or ASCII, or may be Undefined. The "
"Undefined name is UndefinedText, and the ID code is filled with 8 bytes of "
"all \"NULL\" (\"00.H\"). An Exif reader that reads the <UserComment> tag "
"must have a function for determining the ID code. This function is not "
"required in Exif readers that do not use the <UserComment> tag. When a "
"<UserComment> area is set aside, it is recommended that the ID code be ASCII "
"and that the following user comment part be filled with blank characters [20."
"H]."
msgstr ""
"Znacznik dla użytkowników formatu Exif do zapisywania słów kluczowych lub "
"komentarzy do obrazu poza tymi w <ImageDescription> i bez ograniczeń co do "
"kodów znaków w znaczniku <ImageDescription>. Kody znaków używane w znaczniku "
"<UserComment> są określane w oparciu o kod ID w stałym polu 8-bajtowym na "
"początku obszaru danych znacznika. Nieużywana część tego obszaru jest "
"wypełniana znakami NULL (\"00.h\"). Kody ID są przypisywane poprzez "
"rejestrację. Metody określania i odniesienia dla każdego zestawu znaków są "
"podane w specyfikacji. Wartość CountN jest określana w oparciu o 8 bajtów z "
"obszaru kodowania znaków i liczbę bajtów w części zawierającej komentarz "
"użytkownika. Ponieważ typ pola nie jest ASCII, nie jest potrzebne kończenie "
"łańcucha znakiem NULL. Kod ID dla obszaru <UserComment> może być "
"zdefiniowanym kodem takim jak JIS lub ASCII, albo może być nieokreślony. "
"Nazwa pola nieokreślonego (Undefined) to UndefinedText, a jego kod ID jest "
"wypełniany 8 bajtami znaków NULL (\"00.H\"). Czytający Exif, który ma czytać "
"znacznik <UserComment>, musi mieć funkcję określania kodu ID. Funkcja ta nie "
"jest wymagana dla czytających Exif nie używających znacznika <UserComment>. "
"Kiedy znacznik <UserComment> jest pozostawiony nie używany, zaleca się żeby "
"kod ID był ASCII, a następująca po nim część z komentarzem użytkownika była "
"wypełniona pustymi znakami [20.H]."

#: libexif/exif-tag.c:619
msgid "Sub-second Time"
msgstr "Czas - ułamki sekund"

#: libexif/exif-tag.c:620
msgid "A tag used to record fractions of seconds for the <DateTime> tag."
msgstr ""
"Znacznik używany do zapisywania ułamków sekund dla znacznika <DateTime>."

#: libexif/exif-tag.c:624
msgid "Sub-second Time (Original)"
msgstr "Czas - ułamki sekund (oryginału)"

#: libexif/exif-tag.c:625
msgid ""
"A tag used to record fractions of seconds for the <DateTimeOriginal> tag."
msgstr ""
"Znacznik używany do zapisywania ułamków sekund dla znacznika "
"<DateTimeOriginal>."

#: libexif/exif-tag.c:629
msgid "Sub-second Time (Digitized)"
msgstr "Czas - ułamki sekund (obrazu cyfrowego)"

#: libexif/exif-tag.c:630
msgid ""
"A tag used to record fractions of seconds for the <DateTimeDigitized> tag."
msgstr ""
"Znacznik używany do zapisywania ułamków sekund dla znacznika "
"<DateTimeDigitized>."

#: libexif/exif-tag.c:634
msgid "XP Title"
msgstr "Tytuł XP"

#: libexif/exif-tag.c:635
msgid "A character string giving the title of the image, encoded in UTF-16LE."
msgstr "Łańcuch znaków zawierający tytuł obrazu, zapisany w UTF-16LE."

#: libexif/exif-tag.c:639
msgid "XP Comment"
msgstr "Komentarz XP"

#: libexif/exif-tag.c:640
msgid ""
"A character string containing a comment about the image, encoded in UTF-16LE."
msgstr "Łańcuch znaków zawierający komentarz do obrazu, zapisany w UTF-16LE."

#: libexif/exif-tag.c:644
msgid "XP Author"
msgstr "Autor XP"

#: libexif/exif-tag.c:645
msgid ""
"A character string containing the name of the image creator, encoded in "
"UTF-16LE."
msgstr "Łańcuch znaków zawierający nazwę twórcy obrazu, zapisany w UTF-16LE."

#: libexif/exif-tag.c:649
msgid "XP Keywords"
msgstr "Słowa kluczowe XP"

#: libexif/exif-tag.c:650
msgid ""
"A character string containing key words describing the image, encoded in "
"UTF-16LE."
msgstr ""
"Łańcuch znaków zawierający słowa kluczowe opisujące obraz, zapisane w "
"UTF-16LE."

#: libexif/exif-tag.c:654
msgid "XP Subject"
msgstr "Temat XP"

#: libexif/exif-tag.c:655
msgid "A character string giving the image subject, encoded in UTF-16LE."
msgstr "Łańcuch znaków zawierający temat obrazu, zakodowany w UTF-16LE."

#: libexif/exif-tag.c:659
msgid "The FlashPix format version supported by a FPXR file."
msgstr "Wersja formatu FlashPix obsługiwana przez plik FPXR."

#: libexif/exif-tag.c:661 libexif/pentax/mnote-pentax-tag.c:102
msgid "Color Space"
msgstr "Przestrzeń kolorów"

#: libexif/exif-tag.c:662
msgid ""
"The color space information tag is always recorded as the color space "
"specifier. Normally sRGB (=1) is used to define the color space based on the "
"PC monitor conditions and environment. If a color space other than sRGB is "
"used, Uncalibrated (=FFFF.H) is set. Image data recorded as Uncalibrated can "
"be treated as sRGB when it is converted to FlashPix."
msgstr ""
"Znacznik informacji o przestrzeni kolorów jest zawsze zapisywany w celu "
"określenia przestrzeni kolorów. Zwykle używane jest sRGB (=1) do określenia "
"przestrzeni kolorów w oparciu o warunki i środowisko monitora PC. Jeśli "
"użyta jest inna przestrzeń kolorów niż sRGB, ustawiona jest wartość "
"\"nieskalibrowana\" (Uncalibrated, =FFFF.H). Dane obrazu zapisane jako "
"nieskalibrowane mogą być traktowane jako sRGB przy konwersji do FlashPix."

#: libexif/exif-tag.c:670
msgid "Pixel X Dimension"
msgstr "Wymiar X w pikselach"

#: libexif/exif-tag.c:671
msgid ""
"Information specific to compressed data. When a compressed file is recorded, "
"the valid width of the meaningful image must be recorded in this tag, "
"whether or not there is padding data or a restart marker. This tag should "
"not exist in an uncompressed file."
msgstr ""
"Informacje specyficzne dla skompresowanych danych. Kiedy zapisywany jest "
"skompresowany plik, w tym znaczniku musi być zapisana poprawna szerokość "
"znaczącego obrazu, niezależnie od istnienia danych dopełniających czy "
"znacznika restartu. Ten znacznik nie powinien istnieć w pliku "
"nieskompresowanym."

#: libexif/exif-tag.c:677
msgid "Pixel Y Dimension"
msgstr "Wymiar Y w pikselach"

#: libexif/exif-tag.c:678
msgid ""
"Information specific to compressed data. When a compressed file is recorded, "
"the valid height of the meaningful image must be recorded in this tag, "
"whether or not there is padding data or a restart marker. This tag should "
"not exist in an uncompressed file. Since data padding is unnecessary in the "
"vertical direction, the number of lines recorded in this valid image height "
"tag will in fact be the same as that recorded in the SOF."
msgstr ""
"Informacje specyficzne dla skompresowanych danych. Kiedy zapisywany jest "
"skompresowany plik, w tym znaczniku musi być zapisana poprawna wysokość "
"znaczącego obrazu, niezależnie od istnienia danych dopełniających czy "
"znacznika restartu. Ten znacznik nie powinien istnieć w pliku "
"nieskompresowanym. Ponieważ dopełnianie danych w kierunku pionowym nie jest "
"potrzebne, liczba linii zapisana w tym polu będzie w praktyce równa tej "
"zapisanej w SOF."

#: libexif/exif-tag.c:688
msgid "Related Sound File"
msgstr "Powiązany plik dźwiękowy"

#: libexif/exif-tag.c:689
msgid ""
"This tag is used to record the name of an audio file related to the image "
"data. The only relational information recorded here is the Exif audio file "
"name and extension (an ASCII string consisting of 8 characters + '.' + 3 "
"characters). The path is not recorded. Stipulations on audio and file naming "
"conventions are defined in the specification. When using this tag, audio "
"files must be recorded in conformance to the Exif audio format. Writers are "
"also allowed to store the data such as Audio within APP2 as FlashPix "
"extension stream data. The mapping of Exif image files and audio files is "
"done in any of three ways, [1], [2] and [3]. If multiple files are mapped to "
"one file as in [2] or [3], the above format is used to record just one audio "
"file name. If there are multiple audio files, the first recorded file is "
"given. In the case of [3], for example, for the Exif image file \"DSC00001."
"JPG\" only  \"SND00001.WAV\" is given as the related Exif audio file. When "
"there are three Exif audio files \"SND00001.WAV\", \"SND00002.WAV\" and "
"\"SND00003.WAV\", the Exif image file name for each of them, \"DSC00001.JPG"
"\", is indicated. By combining multiple relational information, a variety of "
"playback possibilities can be supported. The method of using relational "
"information is left to the implementation on the playback side. Since this "
"information is an ASCII character string, it is terminated by NULL. When "
"this tag is used to map audio files, the relation of the audio file to image "
"data must also be indicated on the audio file end."
msgstr ""
"Ten znacznik służy do zapisywania nazwy pliku dźwiękowego związanego z "
"danymi obrazu. Jedyną informacją relacyjną zapisywaną tutaj jest nazwa pliku "
"dźwiękowego Exif i rozszerzenie (łańcuch ASCII składający się z 8 znaków + "
"'.' + 3 znaków). Ścieżka nie jest zapisywana. Zastrzeżenia odnośnie dźwięku "
"i konwencje nazywania plików są podane w specyfikacji. Kiedy używany jest "
"ten znacznik, pliki dźwiękowe muszą być zapisane zgodnie z formatem dźwięku "
"Exif. Zapisujący mogą także zapisywać dane takie jak dźwięk wewnątrz danych "
"strumieni rozszerzeń APP2 lub FlashPix. Odwzorowanie między plikami obrazów "
"Exif a plikami dźwiękowymi Exif jest wykonywane na trzy sposoby: [1], [2] i "
"[3]. Jeśli wiele plików jest odwzorowywanych na jeden plik, jak w przypadku "
"[2] lub [3], powyższy format służy do zapisywania tylko jednej nazwy pliku "
"dźwiękowego. Jeśli jest wiele plików dźwiękowych, podawany jest pierwszy "
"plik. W przypadku [3] na przykład dla pliku obrazu Exif \"DSC00001.JPG\" "
"jako powiązany plik dźwiękowy Exif podany jest jedynie \"SND00001.WAV\". "
"Kiedy są trzy pliki dźwiękowe \"SND00001.WAV\", \"SND00002.WAV\" i "
"\"SND00003.WAV\", dla każdego z nich podawana jest nazwa pliku obrazu Exif "
"\"DSC00001.JPG\". Poprzez łączenie wielu informacji relacyjnych obsługiwane "
"jest wiele możliwości odtwarzania. Sposób używania informacji relacyjnych "
"jest pozostawiony implementacji po stronie odtwarzania. Ponieważ ta "
"informacja jest łańcuchem ASCII, jest zakończona znakiem NULL. Kiedy ten "
"znacznik jest używany do przypisywania plików dźwiękowych do plików obrazu, "
"relacja pliku dźwiękowego do danych obrazu musi być określona także po "
"stronie pliku dźwiękowego."

#: libexif/exif-tag.c:719
msgid "Interoperability IFD Pointer"
msgstr "Wskaźnik IFD Interoperability"

#: libexif/exif-tag.c:720
msgid ""
"Interoperability IFD is composed of tags which stores the information to "
"ensure the Interoperability and pointed by the following tag located in Exif "
"IFD. The Interoperability structure of Interoperability IFD is the same as "
"TIFF defined IFD structure but does not contain the image data "
"characteristically compared with normal TIFF IFD."
msgstr ""
"Interoperability IFD jest złożony ze znaczników przechowujących informacje "
"zapewniające współpracę i wskazywane przez ten znacznik umieszczony w Exif "
"IFD. Struktura Interoperability w Interoperability IFD jest taka sama jak "
"struktra IFD zdefiniowana w TIFF, ale w porównaniu do normalnego TIFF IFD "
"nie zawiera danych obrazu."

#: libexif/exif-tag.c:729
msgid "Flash Energy"
msgstr "Energia Flesza"

#: libexif/exif-tag.c:730
msgid ""
"Indicates the strobe energy at the time the image is captured, as measured "
"in Beam Candle Power Seconds (BCPS)."
msgstr ""
"Określenie energii błysku w czasie robienia zdjęcia mierzonej w jednostkach "
"BCPS (Beam Candle Power Seconds)."

#: libexif/exif-tag.c:734
msgid "Spatial Frequency Response"
msgstr "Odpowiedź częstotliwości przestrzennej"

#: libexif/exif-tag.c:735
msgid ""
"This tag records the camera or input device spatial frequency table and SFR "
"values in the direction of image width, image height, and diagonal "
"direction, as specified in ISO 12233."
msgstr ""
"Ten znacznik zapisuje tabelę częstotliwości przestrzennych aparatu lub "
"urządzenia wejściowego oraz wartości SFR w kierunku szerokości obrazu, "
"wysokości obrazu i przekątnej zgodnie ze specyfikacją ISO 12233."

#: libexif/exif-tag.c:741
msgid "Focal Plane X-Resolution"
msgstr "Rozdzielczość X płaszczyzny ogniskowej"

#: libexif/exif-tag.c:742
msgid ""
"Indicates the number of pixels in the image width (X) direction per "
"<FocalPlaneResolutionUnit> on the camera focal plane."
msgstr ""
"Określenie liczby pikseli w kierunku szerokości obrazu (X) na "
"<FocalPlaneResolutionUnit> w płaszczyźnie ogniskowej aparatu."

#: libexif/exif-tag.c:746
msgid "Focal Plane Y-Resolution"
msgstr "Rozdzielczość Y płaszczyzny ogniskowej"

#: libexif/exif-tag.c:747
msgid ""
"Indicates the number of pixels in the image height (V) direction per "
"<FocalPlaneResolutionUnit> on the camera focal plane."
msgstr ""
"Określenie liczby pikseli w kierunku wysokości obrazu (Y) na "
"<FocalPlaneResolutionUnit> w płaszczyźnie ogniskowej aparatu."

#: libexif/exif-tag.c:751
msgid "Focal Plane Resolution Unit"
msgstr "Jednostka rozdzielczości płaszczyzny ogniskowej"

#: libexif/exif-tag.c:752
msgid ""
"Indicates the unit for measuring <FocalPlaneXResolution> and "
"<FocalPlaneYResolution>. This value is the same as the <ResolutionUnit>."
msgstr ""
"Określenie jednostki miary <FocalPlaneXResolution> i "
"<FocalPlaneYResolution>. Ta wartość jest taka sama jak <ResolutionUnit>."

#: libexif/exif-tag.c:757
msgid "Subject Location"
msgstr "Położenie obiektu"

#: libexif/exif-tag.c:758
msgid ""
"Indicates the location of the main subject in the scene. The value of this "
"tag represents the pixel at the center of the main subject relative to the "
"left edge, prior to rotation processing as per the <Rotation> tag. The first "
"value indicates the X column number and the second indicates the Y row "
"number."
msgstr ""
"Określenie położenia głównego obiektu na scenie. Wartość tego znacznika "
"reprezentuje piksel w środku głównego obiektu względem lewej krawędzi, przed "
"wykonaniem obrotu opisanego znacznikiem <Rotation>. Pierwsza wartość określa "
"numer kolumny X, a druga numer wiersza Y."

#: libexif/exif-tag.c:765
msgid "Exposure Index"
msgstr "Indeks ekspozycji"

#: libexif/exif-tag.c:766
msgid ""
"Indicates the exposure index selected on the camera or input device at the "
"time the image is captured."
msgstr ""
"Określenie indeksu ekspozycji wybranego przez aparat lub urządzenie "
"wejściowe w czasie robienia zdjęcia."

#: libexif/exif-tag.c:769
msgid "Sensing Method"
msgstr "Rodzaj czujnika"

#: libexif/exif-tag.c:770
msgid "Indicates the image sensor type on the camera or input device."
msgstr ""
"Określenie rodzaju czujnika obrazu w aparacie lub urządzeniu wejściowym."

#: libexif/exif-tag.c:773 libexif/fuji/mnote-fuji-tag.c:64
msgid "File Source"
msgstr "Źródło pliku"

#: libexif/exif-tag.c:774
msgid ""
"Indicates the image source. If a DSC recorded the image, the tag value of "
"this tag always be set to 3, indicating that the image was recorded on a DSC."
msgstr ""
"Określenie źródła obrazu. Jeśli obraz był zapisany przez DSC, wartość tego "
"znacznika zawsze wynosi 3, oznaczając, że obraz był zapisany na DSC."

#: libexif/exif-tag.c:778
msgid "Scene Type"
msgstr "Rodzaj sceny"

#: libexif/exif-tag.c:779
msgid ""
"Indicates the type of scene. If a DSC recorded the image, this tag value "
"must always be set to 1, indicating that the image was directly photographed."
msgstr ""
"Określenie rodzaju sceny. Jeśli obraz był zapisany przez DSC, wartość tego "
"znacznika zawsze musi być ustawiona na 1, oznaczając, że obraz był "
"bezpośrednio sfotografowany."

#: libexif/exif-tag.c:789
msgid "Custom Rendered"
msgstr "Własny rendering"

#: libexif/exif-tag.c:790
msgid ""
"This tag indicates the use of special processing on image data, such as "
"rendering geared to output. When special processing is performed, the reader "
"is expected to disable or minimize any further processing."
msgstr ""
"Ten znacznik określa użycie specjalnego przetwarzania danych obrazu, takiego "
"jak rendering zastosowany na wyjściu. Jeśli jest wykonane specjalne "
"przetwarzanie, czytający powinien wyłączyć albo zminimalizować dalsze "
"przetwarzanie."

#: libexif/exif-tag.c:796
msgid ""
"This tag indicates the exposure mode set when the image was shot. In auto-"
"bracketing mode, the camera shoots a series of frames of the same scene at "
"different exposure settings."
msgstr ""
"Ten znacznik określa tryb ekspozycji ustawiony przy robieniu zdjęcia. W "
"trybie auto bracket aparat pstryka serię klatek tej samej sceny z różnymi "
"ustawieniami ekspozycji."

#: libexif/exif-tag.c:801
msgid "This tag indicates the white balance mode set when the image was shot."
msgstr ""
"Ten znacznik określa tryb balansu bieli ustawiony przy robieniu zdjęcia."

#: libexif/exif-tag.c:805
msgid "Digital Zoom Ratio"
msgstr "Współczynnik powiększenia cyfrowego"

#: libexif/exif-tag.c:806
msgid ""
"This tag indicates the digital zoom ratio when the image was shot. If the "
"numerator of the recorded value is 0, this indicates that digital zoom was "
"not used."
msgstr ""
"Ten znacznik określa współczynnik powiększenia cyfrowego w czasie robienia "
"zdjęcia. Jeśli licznik wartości znacznika jest równy 0, oznacza to, że nie "
"użyto cyfrowego powiększenia."

#: libexif/exif-tag.c:811
msgid "Focal Length in 35mm Film"
msgstr "Ogniskowa dla filmu 35mm"

#: libexif/exif-tag.c:812
msgid ""
"This tag indicates the equivalent focal length assuming a 35mm film camera, "
"in mm. A value of 0 means the focal length is unknown. Note that this tag "
"differs from the FocalLength tag."
msgstr ""
"Ten znacznik określa odpowiednik ogniskowej w mm przy założeniu aparatu na "
"film 35 mm. Wartość 0 oznacza, że ogniskowa jest nieznana. Należy zauważyć, "
"że ten znacznik różni się od znacznika FocalLength."

#: libexif/exif-tag.c:818
msgid "Scene Capture Type"
msgstr "Rodzaj uchwycenia sceny"

#: libexif/exif-tag.c:819
msgid ""
"This tag indicates the type of scene that was shot. It can also be used to "
"record the mode in which the image was shot. Note that this differs from the "
"scene type <SceneType> tag."
msgstr ""
"Ten znacznik określa rodzaj sceny na zdjęciu. Może być także wykorzystany do "
"zapisania trybu w którym było robione zdjęcie. Należy zauważyć, że ten "
"znacznik różni się od znacznika rodzaju sceny <SceneType>."

#: libexif/exif-tag.c:824
msgid "Gain Control"
msgstr "Regulacja wzmocnienia"

#: libexif/exif-tag.c:825
msgid "This tag indicates the degree of overall image gain adjustment."
msgstr "Ten znacznik określa stopień wzmocnienia całego obrazu."

#: libexif/exif-tag.c:829
msgid ""
"This tag indicates the direction of contrast processing applied by the "
"camera when the image was shot."
msgstr ""
"Ten znacznik określa kierunek przetwarzania kontrastu wykonanego przez "
"aparat przy robieniu zdjęcia."

#: libexif/exif-tag.c:833
msgid ""
"This tag indicates the direction of saturation processing applied by the "
"camera when the image was shot."
msgstr ""
"Ten znacznik określa kierunek przetwarzania nasycenia wykonanego przez "
"aparat przy robieniu zdjęcia."

#: libexif/exif-tag.c:837
msgid ""
"This tag indicates the direction of sharpness processing applied by the "
"camera when the image was shot."
msgstr ""
"Ten znacznik określa kierunek przetwarzania ostrości wykonanego przez aparat "
"przy robieniu zdjęcia."

#: libexif/exif-tag.c:841
msgid "Device Setting Description"
msgstr "Opis ustawień urządzenia"

#: libexif/exif-tag.c:842
msgid ""
"This tag indicates information on the picture-taking conditions of a "
"particular camera model. The tag is used only to indicate the picture-taking "
"conditions in the reader."
msgstr ""
"Ten znacznik określa informacje o warunkach robienia zdjęcia dla konkretnego "
"modelu aparatu. Jest on używany tylko do określenia warunków robienia "
"zdjęcia przy odczycie."

#: libexif/exif-tag.c:848
msgid "Subject Distance Range"
msgstr "Zakres odległości obiektu"

#: libexif/exif-tag.c:849
msgid "This tag indicates the distance to the subject."
msgstr "Ten znacznik określa odległość od obiektu."

#: libexif/exif-tag.c:851
msgid "Image Unique ID"
msgstr "Unikalny identyfikator obrazu"

#: libexif/exif-tag.c:852
msgid ""
"This tag indicates an identifier assigned uniquely to each image. It is "
"recorded as an ASCII string equivalent to hexadecimal notation and 128-bit "
"fixed length."
msgstr ""
"Ten znacznik określa unikalny identyfikator przypisany każdemu zdjęciu. Jest "
"on zapisany jako łańcuch ASCII odpowiadający notacji szesnastkowej o stałej "
"długości 128 bitów."

#: libexif/exif-tag.c:857
msgid "Gamma"
msgstr "Gamma"

#: libexif/exif-tag.c:858
msgid "Indicates the value of coefficient gamma."
msgstr "Określenie wartości współczynnika gamma."

#: libexif/exif-tag.c:860
msgid "PRINT Image Matching"
msgstr "Dopasowywanie obrazu PRINT"

#: libexif/exif-tag.c:861
msgid "Related to Epson's PRINT Image Matching technology"
msgstr "Nieznany (związany z technologią Epsona PRINT Image Matching)"

#: libexif/exif-tag.c:863
msgid "Padding"
msgstr "Wyrównanie"

#: libexif/exif-tag.c:864
msgid ""
"This tag reserves space that can be reclaimed later when additional metadata "
"are added. New metadata can be written in place by replacing this tag with a "
"smaller data element and using the reclaimed space to store the new or "
"expanded metadata tags."
msgstr ""
"Ten znacznik rezerwuje miejsce, które może być później użyte przy dodawaniu "
"dodatkowych metadanych. Nowe metadane mogą być zapisane w tym miejscu "
"poprzez zastąpienie tego znacznika mniejszym elementem danych i użycie "
"odzyskanego miejsca w celu zapisania nowych lub powiększonych znaczników "
"metadanych."

#: libexif/fuji/mnote-fuji-entry.c:62
msgid "Softest"
msgstr "Najmniejsza"

#: libexif/fuji/mnote-fuji-entry.c:66
msgid "Hardest"
msgstr "Największa"

#: libexif/fuji/mnote-fuji-entry.c:67 libexif/fuji/mnote-fuji-entry.c:96
msgid "Medium soft"
msgstr "Średnio mała"

#: libexif/fuji/mnote-fuji-entry.c:68 libexif/fuji/mnote-fuji-entry.c:94
msgid "Medium hard"
msgstr "Średnio duża"

#: libexif/fuji/mnote-fuji-entry.c:69 libexif/fuji/mnote-fuji-entry.c:90
#: libexif/fuji/mnote-fuji-entry.c:98 libexif/fuji/mnote-fuji-entry.c:182
msgid "Film simulation mode"
msgstr "Tryb symulacji filmu"

#: libexif/fuji/mnote-fuji-entry.c:79
msgid "Incandescent"
msgstr "Żarówka"

#: libexif/fuji/mnote-fuji-entry.c:85
msgid "Medium high"
msgstr "Średnio duże"

#: libexif/fuji/mnote-fuji-entry.c:87
msgid "Medium low"
msgstr "Średnio małe"

#: libexif/fuji/mnote-fuji-entry.c:88 libexif/fuji/mnote-fuji-entry.c:97
msgid "Original"
msgstr "Oryginalne"

#: libexif/fuji/mnote-fuji-entry.c:124 libexif/pentax/mnote-pentax-entry.c:164
#: libexif/pentax/mnote-pentax-entry.c:299
msgid "Program AE"
msgstr "Program AE"

#: libexif/fuji/mnote-fuji-entry.c:125
msgid "Natural photo"
msgstr "Zdjęcie naturalne"

#: libexif/fuji/mnote-fuji-entry.c:126
msgid "Vibration reduction"
msgstr "Redukcja drgań"

#: libexif/fuji/mnote-fuji-entry.c:127
msgid "Sunset"
msgstr "Wschód"

#: libexif/fuji/mnote-fuji-entry.c:128 libexif/pentax/mnote-pentax-entry.c:181
msgid "Museum"
msgstr "Muzeum"

#: libexif/fuji/mnote-fuji-entry.c:129
msgid "Party"
msgstr "Impreza"

#: libexif/fuji/mnote-fuji-entry.c:130
msgid "Flower"
msgstr "Kwiaty"

#: libexif/fuji/mnote-fuji-entry.c:131 libexif/pentax/mnote-pentax-entry.c:176
msgid "Text"
msgstr "Tekst"

#: libexif/fuji/mnote-fuji-entry.c:132
msgid "NP & flash"
msgstr "Zdjęcie naturalne z fleszem"

#: libexif/fuji/mnote-fuji-entry.c:137
msgid "Aperture priority AE"
msgstr "AE z priorytetem przysłony"

#: libexif/fuji/mnote-fuji-entry.c:138
msgid "Shutter priority AE"
msgstr "AE z priorytetem migawki"

#: libexif/fuji/mnote-fuji-entry.c:146
msgid "F-Standard"
msgstr "F-Standardowy"

#: libexif/fuji/mnote-fuji-entry.c:147
msgid "F-Chrome"
msgstr "F-Klor"

#: libexif/fuji/mnote-fuji-entry.c:148
msgid "F-B&W"
msgstr "F-Cz-b"

#: libexif/fuji/mnote-fuji-entry.c:151
msgid "No blur"
msgstr "Bez rozmycia"

#: libexif/fuji/mnote-fuji-entry.c:152
msgid "Blur warning"
msgstr "Ostrzeżenie o rozmyciu"

#: libexif/fuji/mnote-fuji-entry.c:155
msgid "Focus good"
msgstr "Dobra ogniskowa"

#: libexif/fuji/mnote-fuji-entry.c:156
msgid "Out of focus"
msgstr "Poza ogniskową"

#: libexif/fuji/mnote-fuji-entry.c:159
msgid "AE good"
msgstr "Dobra AE"

#: libexif/fuji/mnote-fuji-entry.c:160
msgid "Over exposed"
msgstr "Prześwietlone"

#: libexif/fuji/mnote-fuji-entry.c:164
msgid "Wide"
msgstr "Szeroki"

#: libexif/fuji/mnote-fuji-entry.c:167
msgid "F0/Standard"
msgstr "F0/Standardowy"

#: libexif/fuji/mnote-fuji-entry.c:168
msgid "F1/Studio portrait"
msgstr "F1/Portret studyjny"

#: libexif/fuji/mnote-fuji-entry.c:169
msgid "F1a/Professional portrait"
msgstr "F1a/Portret profesjonalny"

#: libexif/fuji/mnote-fuji-entry.c:170
msgid "F1b/Professional portrait"
msgstr "F1b/Portret profesjonalny"

#: libexif/fuji/mnote-fuji-entry.c:171
msgid "F1c/Professional portrait"
msgstr "F1c/Portret profesjonalny"

#: libexif/fuji/mnote-fuji-entry.c:172
msgid "F2/Fujichrome"
msgstr "F2/Fujichrome"

#: libexif/fuji/mnote-fuji-entry.c:173
msgid "F3/Studio portrait Ex"
msgstr "F3/Portret studyjny Ex"

#: libexif/fuji/mnote-fuji-entry.c:174
msgid "F4/Velvia"
msgstr "F4/Velvia"

#: libexif/fuji/mnote-fuji-entry.c:177
msgid "Auto (100-400%)"
msgstr "Auto (100-400%)"

#: libexif/fuji/mnote-fuji-entry.c:179
msgid "Standard (100%)"
msgstr "Standardowy (100%)"

#: libexif/fuji/mnote-fuji-entry.c:180
msgid "Wide1 (230%)"
msgstr "Szeroki 1 (230%)"

#: libexif/fuji/mnote-fuji-entry.c:181
msgid "Wide2 (400%)"
msgstr "Szeroki 2 (400%)"

#: libexif/fuji/mnote-fuji-entry.c:263
#, c-format
msgid "%2.2f mm"
msgstr "%2.2f mm"

#: libexif/fuji/mnote-fuji-entry.c:298 libexif/pentax/mnote-pentax-entry.c:399
#: libexif/pentax/mnote-pentax-entry.c:451
#, c-format
msgid "%i bytes unknown data"
msgstr "%i bajtów nieznanych danych"

#: libexif/fuji/mnote-fuji-tag.c:36
msgid "Maker Note Version"
msgstr "Wersja oznaczenia twórcy"

#: libexif/fuji/mnote-fuji-tag.c:37
msgid "This number is unique and based on the date of manufacture."
msgstr "Ten numer jest unikalny i oparty na dacie wykonania."

#: libexif/fuji/mnote-fuji-tag.c:41
msgid "Chromaticity Saturation"
msgstr "Nasycenie kolorów"

#: libexif/fuji/mnote-fuji-tag.c:44
msgid "Flash Firing Strength Compensation"
msgstr "Kompensacja siły flesza"

#: libexif/fuji/mnote-fuji-tag.c:46
msgid "Focusing Mode"
msgstr "Tryb ogniskowania"

#: libexif/fuji/mnote-fuji-tag.c:47
msgid "Focus Point"
msgstr "Punkt ogniskowania"

#: libexif/fuji/mnote-fuji-tag.c:48
msgid "Slow Synchro Mode"
msgstr "Tryb powolnej synchronizacji"

#: libexif/fuji/mnote-fuji-tag.c:49 libexif/pentax/mnote-pentax-tag.c:72
msgid "Picture Mode"
msgstr "Tryb zdjęcia"

#: libexif/fuji/mnote-fuji-tag.c:50
msgid "Continuous Taking"
msgstr "Ciągłe zdjęcia"

#: libexif/fuji/mnote-fuji-tag.c:51
msgid "Continuous Sequence Number"
msgstr "Numer sekwencji ciągłej"

#: libexif/fuji/mnote-fuji-tag.c:52
msgid "FinePix Color"
msgstr "Kolor FinePix"

#: libexif/fuji/mnote-fuji-tag.c:53
msgid "Blur Check"
msgstr "Kontrola rozmycia"

#: libexif/fuji/mnote-fuji-tag.c:54
msgid "Auto Focus Check"
msgstr "Kontrola ogniskowania"

#: libexif/fuji/mnote-fuji-tag.c:55
msgid "Auto Exposure Check"
msgstr "Kontrola automatycznej ekspozycji"

#: libexif/fuji/mnote-fuji-tag.c:56
msgid "Dynamic Range"
msgstr "Zakres dynamiczny"

#: libexif/fuji/mnote-fuji-tag.c:57
msgid "Film Simulation Mode"
msgstr "Tryb symulacji filmu"

#: libexif/fuji/mnote-fuji-tag.c:58
msgid "Dynamic Range Wide Mode"
msgstr "Tryb szerokiego zakresu dynamicznego"

#: libexif/fuji/mnote-fuji-tag.c:59
msgid "Development Dynamic Range Wide Mode"
msgstr "Rozwojowy tryb szerokiego zakresu dynamicznego"

#: libexif/fuji/mnote-fuji-tag.c:60
msgid "Minimum Focal Length"
msgstr "Minimalna ogniskowa"

#: libexif/fuji/mnote-fuji-tag.c:61
msgid "Maximum Focal Length"
msgstr "Maksymalna ogniskowa"

#: libexif/fuji/mnote-fuji-tag.c:62
msgid "Maximum Aperture at Minimum Focal"
msgstr "Maksymalna przysłona przy minimalnej ogniskowej"

#: libexif/fuji/mnote-fuji-tag.c:63
msgid "Maximum Aperture at Maximum Focal"
msgstr "Maksymalna przysłona przy maksymalnej ogniskowej"

#: libexif/fuji/mnote-fuji-tag.c:65
msgid "Order Number"
msgstr "Numer kolejny"

#: libexif/fuji/mnote-fuji-tag.c:66 libexif/pentax/mnote-pentax-tag.c:98
msgid "Frame Number"
msgstr "Numer ramki"

#: libexif/olympus/mnote-olympus-entry.c:49
#, c-format
msgid "Invalid format '%s', expected '%s' or '%s'."
msgstr "Błędny format '%s', oczekiwano '%s' lub '%s'."

#: libexif/olympus/mnote-olympus-entry.c:92
msgid "AF non D lens"
msgstr "Obiektyw AF nie D"

#: libexif/olympus/mnote-olympus-entry.c:94
msgid "AF-D or AF-S lens"
msgstr "Obiektyw AF-D lub AF-S"

#: libexif/olympus/mnote-olympus-entry.c:95
msgid "AF-D G lens"
msgstr "Obiektyw AF-D G"

#: libexif/olympus/mnote-olympus-entry.c:96
msgid "AF-D VR lens"
msgstr "Obiektyw AF-D VR"

#: libexif/olympus/mnote-olympus-entry.c:97
msgid "AF-D G VR lens"
msgstr "Obiektyw AF-D G VR"

#: libexif/olympus/mnote-olympus-entry.c:101
msgid "Flash unit unknown"
msgstr "Nieznany flesz"

#: libexif/olympus/mnote-olympus-entry.c:102
msgid "Flash is external"
msgstr "Flesz zewnętrzny"

#: libexif/olympus/mnote-olympus-entry.c:103
msgid "Flash is on camera"
msgstr "Flesz na aparacie"

#: libexif/olympus/mnote-olympus-entry.c:106
msgid "VGA basic"
msgstr "Podstawowa VGA"

#: libexif/olympus/mnote-olympus-entry.c:107
msgid "VGA normal"
msgstr "Normalna VGA"

#: libexif/olympus/mnote-olympus-entry.c:108
msgid "VGA fine"
msgstr "Dobra VGA"

#: libexif/olympus/mnote-olympus-entry.c:109
msgid "SXGA basic"
msgstr "Podstawowa SXGA"

#: libexif/olympus/mnote-olympus-entry.c:110
msgid "SXGA normal"
msgstr "Normalna SXGA"

#: libexif/olympus/mnote-olympus-entry.c:111
msgid "SXGA fine"
msgstr "Dobra SXGA"

#: libexif/olympus/mnote-olympus-entry.c:112
msgid "2 Mpixel basic"
msgstr "Podstawowa 2 MPiksele"

#: libexif/olympus/mnote-olympus-entry.c:113
msgid "2 Mpixel normal"
msgstr "Normalna 2 MPiksele"

#: libexif/olympus/mnote-olympus-entry.c:114
msgid "2 Mpixel fine"
msgstr "Dobra 2 MPiksele"

#: libexif/olympus/mnote-olympus-entry.c:117
msgid "Color"
msgstr "Kolorowy"

#: libexif/olympus/mnote-olympus-entry.c:122
msgid "Bright+"
msgstr "Jasność+"

#: libexif/olympus/mnote-olympus-entry.c:123
msgid "Bright-"
msgstr "Jasność-"

#: libexif/olympus/mnote-olympus-entry.c:124
msgid "Contrast+"
msgstr "Kontrast+"

#: libexif/olympus/mnote-olympus-entry.c:125
msgid "Contrast-"
msgstr "Kontrast-"

#: libexif/olympus/mnote-olympus-entry.c:128
msgid "ISO 80"
msgstr "ISO 80"

#: libexif/olympus/mnote-olympus-entry.c:129
msgid "ISO 160"
msgstr "ISO 160"

#: libexif/olympus/mnote-olympus-entry.c:130
msgid "ISO 320"
msgstr "ISO 320"

#: libexif/olympus/mnote-olympus-entry.c:131
#: libexif/olympus/mnote-olympus-entry.c:249
msgid "ISO 100"
msgstr "ISO 100"

#: libexif/olympus/mnote-olympus-entry.c:135
msgid "Preset"
msgstr "Predefiniowany"

#: libexif/olympus/mnote-olympus-entry.c:137
msgid "Incandescence"
msgstr "Żarówka"

#: libexif/olympus/mnote-olympus-entry.c:138
msgid "Fluorescence"
msgstr "Świetlówka"

#: libexif/olympus/mnote-olympus-entry.c:140
msgid "SpeedLight"
msgstr "SpeedLight"

#: libexif/olympus/mnote-olympus-entry.c:143
msgid "No fisheye"
msgstr "Bez rybiego oka"

#: libexif/olympus/mnote-olympus-entry.c:144
msgid "Fisheye on"
msgstr "Z rybim okiem"

#: libexif/olympus/mnote-olympus-entry.c:147
msgid "Normal, SQ"
msgstr "Normalna, SQ"

#: libexif/olympus/mnote-olympus-entry.c:148
msgid "Normal, HQ"
msgstr "Normalna, HQ"

#: libexif/olympus/mnote-olympus-entry.c:149
msgid "Normal, SHQ"
msgstr "Normalna, SHQ"

#: libexif/olympus/mnote-olympus-entry.c:150
msgid "Normal, RAW"
msgstr "Normalna, RAW"

#: libexif/olympus/mnote-olympus-entry.c:151
msgid "Normal, SQ1"
msgstr "Normalna, SQ1"

#: libexif/olympus/mnote-olympus-entry.c:152
msgid "Normal, SQ2"
msgstr "Normalna, SQ2"

#: libexif/olympus/mnote-olympus-entry.c:153
msgid "Normal, super high"
msgstr "Normalna, bardzo wysoka"

#: libexif/olympus/mnote-olympus-entry.c:154
msgid "Normal, standard"
msgstr "Normalna, standardowa"

#: libexif/olympus/mnote-olympus-entry.c:155
msgid "Fine, SQ"
msgstr "Dobra, SQ"

#: libexif/olympus/mnote-olympus-entry.c:156
msgid "Fine, HQ"
msgstr "Dobra, HQ"

#: libexif/olympus/mnote-olympus-entry.c:157
msgid "Fine, SHQ"
msgstr "Dobra, SHQ"

#: libexif/olympus/mnote-olympus-entry.c:158
msgid "Fine, RAW"
msgstr "Dobra, RAW"

#: libexif/olympus/mnote-olympus-entry.c:159
msgid "Fine, SQ1"
msgstr "Dobra, SQ1"

#: libexif/olympus/mnote-olympus-entry.c:160
msgid "Fine, SQ2"
msgstr "Dobra, SQ2"

#: libexif/olympus/mnote-olympus-entry.c:161
msgid "Fine, super high"
msgstr "Dobra, bardzo wysoka"

#: libexif/olympus/mnote-olympus-entry.c:162
msgid "Super fine, SQ"
msgstr "Bardzo dobra, SQ"

#: libexif/olympus/mnote-olympus-entry.c:163
msgid "Super fine, HQ"
msgstr "Bardzo dobra, HQ"

#: libexif/olympus/mnote-olympus-entry.c:164
msgid "Super fine, SHQ"
msgstr "Bardzo dobra, SHQ"

#: libexif/olympus/mnote-olympus-entry.c:165
msgid "Super fine, RAW"
msgstr "Bardzo dobra, RAW"

#: libexif/olympus/mnote-olympus-entry.c:166
msgid "Super fine, SQ1"
msgstr "Bardzo dobra, SQ1"

#: libexif/olympus/mnote-olympus-entry.c:167
msgid "Super fine, SQ2"
msgstr "Bardzo dobra, SQ2"

#: libexif/olympus/mnote-olympus-entry.c:168
msgid "Super fine, super high"
msgstr "Bardzo dobra, bardzo wysoka"

#: libexif/olympus/mnote-olympus-entry.c:169
msgid "Super fine, high"
msgstr "Bardzo dobra, wysoka"

#: libexif/olympus/mnote-olympus-entry.c:172
#: libexif/olympus/mnote-olympus-entry.c:177
#: libexif/olympus/mnote-olympus-entry.c:211
#: libexif/olympus/mnote-olympus-entry.c:220
#: libexif/olympus/mnote-olympus-entry.c:243
msgid "No"
msgstr "Nie"

#: libexif/olympus/mnote-olympus-entry.c:183
msgid "On (Preset)"
msgstr "Włączony (predefiniowany)"

#: libexif/olympus/mnote-olympus-entry.c:188
msgid "Fill"
msgstr "Pełny"

#: libexif/olympus/mnote-olympus-entry.c:195
msgid "Internal + external"
msgstr "Wewnętrzny i zewnętrzny"

#: libexif/olympus/mnote-olympus-entry.c:224
msgid "Interlaced"
msgstr "Przeplatany"

#: libexif/olympus/mnote-olympus-entry.c:225
msgid "Progressive"
msgstr "Progresywny"

#: libexif/olympus/mnote-olympus-entry.c:231
#: libexif/pentax/mnote-pentax-entry.c:85
#: libexif/pentax/mnote-pentax-entry.c:139
msgid "Best"
msgstr "Najlepsza"

#: libexif/olympus/mnote-olympus-entry.c:232
msgid "Adjust exposure"
msgstr "Poprawka ekspozycji"

#: libexif/olympus/mnote-olympus-entry.c:235
msgid "Spot focus"
msgstr "Ogniskowanie punktowe"

#: libexif/olympus/mnote-olympus-entry.c:236
msgid "Normal focus"
msgstr "Ogniskowanie zwykłe"

#: libexif/olympus/mnote-olympus-entry.c:239
msgid "Record while down"
msgstr "Nagrywanie przy naciśnięciu"

#: libexif/olympus/mnote-olympus-entry.c:240
msgid "Press start, press stop"
msgstr "Naciśnięcie start, potem stop"

#: libexif/olympus/mnote-olympus-entry.c:248
msgid "ISO 50"
msgstr "ISO 50"

#: libexif/olympus/mnote-olympus-entry.c:250
msgid "ISO 200"
msgstr "ISO 200"

#: libexif/olympus/mnote-olympus-entry.c:251
msgid "ISO 400"
msgstr "ISO 400"

#: libexif/olympus/mnote-olympus-entry.c:255
#: libexif/pentax/mnote-pentax-entry.c:168
msgid "Sport"
msgstr "Sport"

#: libexif/olympus/mnote-olympus-entry.c:256
msgid "TV"
msgstr "TV"

#: libexif/olympus/mnote-olympus-entry.c:258
msgid "User 1"
msgstr "Użytkownika 1"

#: libexif/olympus/mnote-olympus-entry.c:259
msgid "User 2"
msgstr "Użytkownika 2"

#: libexif/olympus/mnote-olympus-entry.c:260
msgid "Lamp"
msgstr "Lampa"

#: libexif/olympus/mnote-olympus-entry.c:263
msgid "5 frames/sec"
msgstr "5 klatek/sek"

#: libexif/olympus/mnote-olympus-entry.c:264
msgid "10 frames/sec"
msgstr "10 klatek/sek"

#: libexif/olympus/mnote-olympus-entry.c:265
msgid "15 frames/sec"
msgstr "15 klatek/sek"

#: libexif/olympus/mnote-olympus-entry.c:266
msgid "20 frames/sec"
msgstr "20 klatek/sek"

#: libexif/olympus/mnote-olympus-entry.c:381
#, c-format
msgid "Red Correction %f, blue Correction %f"
msgstr "Korekcja czerwieni %f, korekcja błękitu %f"

#: libexif/olympus/mnote-olympus-entry.c:388
msgid "No manual focus selection"
msgstr "Brak ręcznego wyboru ogniska"

#: libexif/olympus/mnote-olympus-entry.c:391
#, c-format
msgid "%2.2f meters"
msgstr "%2.2f metrów"

#: libexif/olympus/mnote-olympus-entry.c:417
msgid "AF position: center"
msgstr "Położenie AF: środek"

#: libexif/olympus/mnote-olympus-entry.c:418
msgid "AF position: top"
msgstr "Położenie AF: góra"

#: libexif/olympus/mnote-olympus-entry.c:419
msgid "AF position: bottom"
msgstr "Położenie AF: dół"

#: libexif/olympus/mnote-olympus-entry.c:420
msgid "AF position: left"
msgstr "Położenie AF: lewo"

#: libexif/olympus/mnote-olympus-entry.c:421
msgid "AF position: right"
msgstr "Położenie AF: prawo"

#: libexif/olympus/mnote-olympus-entry.c:422
msgid "AF position: upper-left"
msgstr "Położenie AF: lewa góra"

#: libexif/olympus/mnote-olympus-entry.c:423
msgid "AF position: upper-right"
msgstr "Położenie AF: prawa góra"

#: libexif/olympus/mnote-olympus-entry.c:424
msgid "AF position: lower-left"
msgstr "Położenie AF: lewy dół"

#: libexif/olympus/mnote-olympus-entry.c:425
msgid "AF position: lower-right"
msgstr "Położenie AF: prawy dół"

#: libexif/olympus/mnote-olympus-entry.c:426
msgid "AF position: far left"
msgstr "Położenie AF: dalekie lewo"

#: libexif/olympus/mnote-olympus-entry.c:427
msgid "AF position: far right"
msgstr "Położenie AF: dalekie prawo"

#: libexif/olympus/mnote-olympus-entry.c:428
msgid "Unknown AF position"
msgstr "Nieznane położenie AF"

#: libexif/olympus/mnote-olympus-entry.c:439
#: libexif/olympus/mnote-olympus-entry.c:509
#, c-format
msgid "Internal error (unknown value %hi)"
msgstr "Błąd wewnętrzny (nieznana wartość %hi)"

#: libexif/olympus/mnote-olympus-entry.c:447
#: libexif/olympus/mnote-olympus-entry.c:517
#, c-format
msgid "Unknown value %hi"
msgstr "Nieznana wartość %hi"

#: libexif/olympus/mnote-olympus-entry.c:542
#: libexif/olympus/mnote-olympus-entry.c:562
#, c-format
msgid "Unknown %hu"
msgstr "Nieznany %hu"

#: libexif/olympus/mnote-olympus-entry.c:559
msgid "2 sec."
msgstr "2 sek."

#: libexif/olympus/mnote-olympus-entry.c:598
msgid "Fast"
msgstr "Szybki"

#: libexif/olympus/mnote-olympus-entry.c:702
msgid "Automatic"
msgstr "Automatyczny"

#: libexif/olympus/mnote-olympus-entry.c:732
#, c-format
msgid "Manual: %liK"
msgstr "Ręczny: %liK"

#: libexif/olympus/mnote-olympus-entry.c:735
msgid "Manual: unknown"
msgstr "Ręczny: nieznany"

#: libexif/olympus/mnote-olympus-entry.c:741
msgid "One-touch"
msgstr "One-touch"

#: libexif/olympus/mnote-olympus-entry.c:797
#: libexif/olympus/mnote-olympus-entry.c:807
msgid "Infinite"
msgstr "Nieskończoność"

#: libexif/olympus/mnote-olympus-entry.c:815
#, c-format
msgid "%i bytes unknown data: "
msgstr "%i bajtów nieznanych danych: "

#: libexif/olympus/mnote-olympus-tag.c:38
#: libexif/olympus/mnote-olympus-tag.c:53
msgid "ISO Setting"
msgstr "Ustawienie ISO"

#: libexif/olympus/mnote-olympus-tag.c:39
msgid "Color Mode (?)"
msgstr "Tryb koloru (?)"

#: libexif/olympus/mnote-olympus-tag.c:42
msgid "Image Sharpening"
msgstr "Wyostrzanie obrazu"

#: libexif/olympus/mnote-olympus-tag.c:44
msgid "Flash Setting"
msgstr "Ustawienie flesza"

#: libexif/olympus/mnote-olympus-tag.c:46
msgid "White Balance Fine Adjustment"
msgstr "Dokładne ustawienie balansu bieli"

#: libexif/olympus/mnote-olympus-tag.c:47
msgid "White Balance RB"
msgstr "Balans bieli RB"

#: libexif/olympus/mnote-olympus-tag.c:49
msgid "ISO Selection"
msgstr "Ustawienie ISO"

#: libexif/olympus/mnote-olympus-tag.c:50
msgid "Preview Image IFD"
msgstr "Podgląd IFD zdjęcia"

#: libexif/olympus/mnote-olympus-tag.c:50
msgid "Offset of the preview image directory (IFD) inside the file."
msgstr "Offset katalogu podglądu obrazu (IFD) wewnątrz pliku."

#: libexif/olympus/mnote-olympus-tag.c:51
msgid "Exposurediff ?"
msgstr "Różnica ekspozycji ?"

#: libexif/olympus/mnote-olympus-tag.c:54
msgid "Image Boundary"
msgstr "Obramowanie zdjęcia"

#: libexif/olympus/mnote-olympus-tag.c:56
msgid "Flash Exposure Bracket Value"
msgstr "Wartość bracketingu ekspozycji flesza"

#: libexif/olympus/mnote-olympus-tag.c:57
msgid "Exposure Bracket Value"
msgstr "Wartość bracketingu ekspozycji"

#: libexif/olympus/mnote-olympus-tag.c:58
#: libexif/olympus/mnote-olympus-tag.c:96
msgid "Image Adjustment"
msgstr "Regulacja obrazu"

#: libexif/olympus/mnote-olympus-tag.c:59
msgid "Tone Compensation"
msgstr "Kompensacja tonów"

#: libexif/olympus/mnote-olympus-tag.c:60
msgid "Adapter"
msgstr "Przetwornik"

#: libexif/olympus/mnote-olympus-tag.c:62
msgid "Lens"
msgstr "Obiektyw"

#: libexif/olympus/mnote-olympus-tag.c:63
#: libexif/olympus/mnote-olympus-tag.c:135
#: libexif/olympus/mnote-olympus-tag.c:185
msgid "Manual Focus Distance"
msgstr "Ręczna odległość ogniska"

#: libexif/olympus/mnote-olympus-tag.c:65
msgid "Flash Used"
msgstr "Użyto flesza"

#: libexif/olympus/mnote-olympus-tag.c:66
msgid "AF Focus Position"
msgstr "Położenie ogniska AF"

#: libexif/olympus/mnote-olympus-tag.c:67
msgid "Bracketing"
msgstr "Bracketing"

#: libexif/olympus/mnote-olympus-tag.c:69
msgid "Lens F Stops"
msgstr "Przesłony F obiektywu"

#: libexif/olympus/mnote-olympus-tag.c:70
msgid "Contrast Curve"
msgstr "Krzywa kontrastu"

#: libexif/olympus/mnote-olympus-tag.c:71
#: libexif/olympus/mnote-olympus-tag.c:95
#: libexif/pentax/mnote-pentax-tag.c:134
msgid "Color Mode"
msgstr "Tryb koloru"

#: libexif/olympus/mnote-olympus-tag.c:72
msgid "Light Type"
msgstr "Rodzaj oświetlenia"

#: libexif/olympus/mnote-olympus-tag.c:74
msgid "Hue Adjustment"
msgstr "Regulacja barwy"

#: libexif/olympus/mnote-olympus-tag.c:76
#: libexif/olympus/mnote-olympus-tag.c:163
#: libexif/pentax/mnote-pentax-tag.c:108
msgid "Noise Reduction"
msgstr "Redukcja szumów"

#: libexif/olympus/mnote-olympus-tag.c:79
msgid "Sensor Pixel Size"
msgstr "Rozmiar piksela sensora"

#: libexif/olympus/mnote-olympus-tag.c:82
msgid "Image Data Size"
msgstr "Rozmiar danych obrazu"

#: libexif/olympus/mnote-olympus-tag.c:82
msgid "Size of compressed image data in bytes."
msgstr "Rozmiar danych skompresowanego obrazu w bajtach."

#: libexif/olympus/mnote-olympus-tag.c:84
msgid "Total Number of Pictures Taken"
msgstr "Całkowita liczba zrobionych zdjęć"

#: libexif/olympus/mnote-olympus-tag.c:86
msgid "Optimize Image"
msgstr "Optymalizacja obrazu"

#: libexif/olympus/mnote-olympus-tag.c:88
msgid "Vari Program"
msgstr "Program Vari"

#: libexif/olympus/mnote-olympus-tag.c:89
msgid "Capture Editor Data"
msgstr "Dane edytora zdjęć"

#: libexif/olympus/mnote-olympus-tag.c:90
msgid "Capture Editor Version"
msgstr "Wersja edytora zdjęć"

#: libexif/olympus/mnote-olympus-tag.c:97
#: libexif/olympus/mnote-olympus-tag.c:183
msgid "CCD Sensitivity"
msgstr "Czułość CCD"

#: libexif/olympus/mnote-olympus-tag.c:99
msgid "Focus"
msgstr "Ogniskowa"

#: libexif/olympus/mnote-olympus-tag.c:102
msgid "Converter"
msgstr "Konwerter"

#: libexif/olympus/mnote-olympus-tag.c:105
msgid "Thumbnail Image"
msgstr "Miniaturka"

#: libexif/olympus/mnote-olympus-tag.c:106
msgid "Speed/Sequence/Panorama Direction"
msgstr "Kierunek szybkość/sekwencja/panorama"

#: libexif/olympus/mnote-olympus-tag.c:109
msgid "Black & White Mode"
msgstr "Tryb czarno-biały"

#: libexif/olympus/mnote-olympus-tag.c:111
msgid "Focal Plane Diagonal"
msgstr "Przekątna płaszczyzny ogniskowej"

#: libexif/olympus/mnote-olympus-tag.c:112
msgid "Lens Distortion Parameters"
msgstr "Parametry zniekształcenia obiektywu"

#: libexif/olympus/mnote-olympus-tag.c:114
msgid "Info"
msgstr "Informacje"

#: libexif/olympus/mnote-olympus-tag.c:115
msgid "Camera ID"
msgstr "ID aparatu"

#: libexif/olympus/mnote-olympus-tag.c:116
msgid "Precapture Frames"
msgstr "Początkowe klatki"

#: libexif/olympus/mnote-olympus-tag.c:117
msgid "White Board"
msgstr "Balans bieli"

#: libexif/olympus/mnote-olympus-tag.c:118
msgid "One Touch White Balance"
msgstr "Jednorazowy balans bieli"

#: libexif/olympus/mnote-olympus-tag.c:119
msgid "White Balance Bracket"
msgstr "Bracketing balansu bieli"

#: libexif/olympus/mnote-olympus-tag.c:120
#: libexif/pentax/mnote-pentax-tag.c:123
msgid "White Balance Bias"
msgstr "Nakierowanie balansu bieli"

#: libexif/olympus/mnote-olympus-tag.c:121
msgid "Data Dump"
msgstr "Zrzut danych"

#: libexif/olympus/mnote-olympus-tag.c:124
msgid "ISO Value"
msgstr "Wartość ISO"

#: libexif/olympus/mnote-olympus-tag.c:125
msgid "Aperture Value"
msgstr "Wartość przysłony"

#: libexif/olympus/mnote-olympus-tag.c:126
msgid "Brightness Value"
msgstr "Wartość jasności"

#: libexif/olympus/mnote-olympus-tag.c:128
msgid "Flash Device"
msgstr "Flesz"

#: libexif/olympus/mnote-olympus-tag.c:130
msgid "Sensor Temperature"
msgstr "Temperatura matrycy"

#: libexif/olympus/mnote-olympus-tag.c:131
msgid "Lens Temperature"
msgstr "Temperatura obiektywu"

#: libexif/olympus/mnote-olympus-tag.c:132
msgid "Light Condition"
msgstr "Warunki oświetlenia"

#: libexif/olympus/mnote-olympus-tag.c:136
msgid "Zoom Step Count"
msgstr "Liczba stopni powiększenia"

#: libexif/olympus/mnote-olympus-tag.c:137
msgid "Focus Step Count"
msgstr "Liczba stopni ogniskowej"

#: libexif/olympus/mnote-olympus-tag.c:138
msgid "Sharpness Setting"
msgstr "Ustawienie ostrości"

#: libexif/olympus/mnote-olympus-tag.c:139
msgid "Flash Charge Level"
msgstr "Poziom ładowania flesza"

#: libexif/olympus/mnote-olympus-tag.c:140
msgid "Color Matrix"
msgstr "Macierz kolorów"

#: libexif/olympus/mnote-olympus-tag.c:141
msgid "Black Level"
msgstr "Poziom czerni"

#: libexif/olympus/mnote-olympus-tag.c:142
msgid "White Balance Setting"
msgstr "Ustawienie balansu bieli"

#: libexif/olympus/mnote-olympus-tag.c:143
#: libexif/pentax/mnote-pentax-tag.c:87
msgid "Red Balance"
msgstr "Balans czerwieni"

#: libexif/olympus/mnote-olympus-tag.c:144
#: libexif/pentax/mnote-pentax-tag.c:86
msgid "Blue Balance"
msgstr "Balans błękitu"

#: libexif/olympus/mnote-olympus-tag.c:145
msgid "Color Matrix Number"
msgstr "Numer macierzy kolorów"

#: libexif/olympus/mnote-olympus-tag.c:147
msgid "Flash Exposure Comp"
msgstr "Kompensacja ekspozycji flesza"

#: libexif/olympus/mnote-olympus-tag.c:148
msgid "Internal Flash Table"
msgstr "Tablica wewnętrznego flesza"

#: libexif/olympus/mnote-olympus-tag.c:149
msgid "External Flash G Value"
msgstr "Wartość G zewnętrznego flesza"

#: libexif/olympus/mnote-olympus-tag.c:150
msgid "External Flash Bounce"
msgstr "Odbicie zewnętrznego flesza"

#: libexif/olympus/mnote-olympus-tag.c:151
msgid "External Flash Zoom"
msgstr "Powiększenie zewnętrznego flesza"

#: libexif/olympus/mnote-olympus-tag.c:152
msgid "External Flash Mode"
msgstr "Tryb zewnętrznego flesza"

#: libexif/olympus/mnote-olympus-tag.c:153
msgid "Contrast Setting"
msgstr "Ustawienie kontrastu"

#: libexif/olympus/mnote-olympus-tag.c:154
msgid "Sharpness Factor"
msgstr "Wskaźnik ostrości"

#: libexif/olympus/mnote-olympus-tag.c:155
msgid "Color Control"
msgstr "Kontrola kolorów"

#: libexif/olympus/mnote-olympus-tag.c:156
msgid "Olympus Image Width"
msgstr "Szerokość obrazu (Olympus)"

#: libexif/olympus/mnote-olympus-tag.c:157
msgid "Olympus Image Height"
msgstr "Wysokość obrazu (Olympus)"

#: libexif/olympus/mnote-olympus-tag.c:158
msgid "Scene Detect"
msgstr "Wykrywanie sceny"

#: libexif/olympus/mnote-olympus-tag.c:159
msgid "Compression Ratio"
msgstr "Stopień kompresji"

#: libexif/olympus/mnote-olympus-tag.c:160
msgid "Preview Image Valid"
msgstr "Poprawność podglądu zdjęcia"

#: libexif/olympus/mnote-olympus-tag.c:161
msgid "AF Result"
msgstr "Wynik AF"

#: libexif/olympus/mnote-olympus-tag.c:162
msgid "CCD Scan Mode"
msgstr "Tryb skanowania CCD"

#: libexif/olympus/mnote-olympus-tag.c:164
msgid "Infinity Lens Step"
msgstr "Krok obiektywu w nieskończoności"

#: libexif/olympus/mnote-olympus-tag.c:165
msgid "Near Lens Step"
msgstr "Krok obiektywu w bliży"

#: libexif/olympus/mnote-olympus-tag.c:166
msgid "Light Value Center"
msgstr "Środek wartości światła"

#: libexif/olympus/mnote-olympus-tag.c:167
msgid "Light Value Periphery"
msgstr "Granica wartości światła"

#: libexif/olympus/mnote-olympus-tag.c:170
msgid "Sequential Shot"
msgstr "Zdjęcia sekwencyjne"

#: libexif/olympus/mnote-olympus-tag.c:171
msgid "Wide Range"
msgstr "Zakres szeroki"

#: libexif/olympus/mnote-olympus-tag.c:172
msgid "Color Adjustment Mode"
msgstr "Tryb regulacji koloru"

#: libexif/olympus/mnote-olympus-tag.c:174
msgid "Quick Shot"
msgstr "Szybkie zdjęcie"

#: libexif/olympus/mnote-olympus-tag.c:176
msgid "Voice Memo"
msgstr "Notatka głosowa"

#: libexif/olympus/mnote-olympus-tag.c:177
msgid "Record Shutter Release"
msgstr "Migawka dla nagrywania"

#: libexif/olympus/mnote-olympus-tag.c:178
msgid "Flicker Reduce"
msgstr "Redukcja migotania"

#: libexif/olympus/mnote-olympus-tag.c:179
msgid "Optical Zoom"
msgstr "Powiększenie optyczne"

#: libexif/olympus/mnote-olympus-tag.c:181
msgid "Light Source Special"
msgstr "Specjalne źródło światła"

#: libexif/olympus/mnote-olympus-tag.c:182
msgid "Resaved"
msgstr "Ponownie zapisane"

#: libexif/olympus/mnote-olympus-tag.c:184
msgid "Scene Select"
msgstr "Wybór sceny"

#: libexif/olympus/mnote-olympus-tag.c:186
msgid "Sequence Shot Interval"
msgstr "Odstęp zdjęć sekwencyjnych"

#: libexif/olympus/mnote-olympus-tag.c:189
msgid "Epson Image Width"
msgstr "Szerokość obrazu (Epson)"

#: libexif/olympus/mnote-olympus-tag.c:190
msgid "Epson Image Height"
msgstr "Wysokość obrazu (Epson)"

#: libexif/olympus/mnote-olympus-tag.c:191
msgid "Epson Software Version"
msgstr "Wersja oprogramowania Epsona"

#: libexif/pentax/mnote-pentax-entry.c:80
#: libexif/pentax/mnote-pentax-entry.c:134
msgid "Multi-exposure"
msgstr "Ekspozycja wielokrotna"

#: libexif/pentax/mnote-pentax-entry.c:83
#: libexif/pentax/mnote-pentax-entry.c:137
msgid "Good"
msgstr "Dobra"

#: libexif/pentax/mnote-pentax-entry.c:84
#: libexif/pentax/mnote-pentax-entry.c:138
msgid "Better"
msgstr "Lepsza"

#: libexif/pentax/mnote-pentax-entry.c:92
msgid "Flash on"
msgstr "Flesz włączony"

#: libexif/pentax/mnote-pentax-entry.c:140
msgid "TIFF"
msgstr "TIFF"

#: libexif/pentax/mnote-pentax-entry.c:150
msgid "2560x1920 or 2304x1728"
msgstr "2560x1920 lub 2304x1728"

#: libexif/pentax/mnote-pentax-entry.c:156
msgid "2304x1728 or 2592x1944"
msgstr "2304x1728 lub 2592x1944"

#: libexif/pentax/mnote-pentax-entry.c:158
msgid "2816x2212 or 2816x2112"
msgstr "2816x2212 lub 2816x2112"

#: libexif/pentax/mnote-pentax-entry.c:171
msgid "Surf & snow"
msgstr "Surfing i śnieg"

#: libexif/pentax/mnote-pentax-entry.c:172
msgid "Sunset or candlelight"
msgstr "Wschód słońca lub światło świecy"

#: libexif/pentax/mnote-pentax-entry.c:173
msgid "Autumn"
msgstr "Jesień"

#: libexif/pentax/mnote-pentax-entry.c:178
msgid "Self portrait"
msgstr "Autoportret"

#: libexif/pentax/mnote-pentax-entry.c:179
msgid "Illustrations"
msgstr "Ilustracje"

#: libexif/pentax/mnote-pentax-entry.c:180
msgid "Digital filter"
msgstr "Filtr cyfrowy"

#: libexif/pentax/mnote-pentax-entry.c:182
msgid "Food"
msgstr "Jedzenie"

#: libexif/pentax/mnote-pentax-entry.c:183
msgid "Green mode"
msgstr "Tryb zieleni"

#: libexif/pentax/mnote-pentax-entry.c:184
msgid "Light pet"
msgstr "Jasne zwierzę"

#: libexif/pentax/mnote-pentax-entry.c:185
msgid "Dark pet"
msgstr "Ciemne zwierzę"

#: libexif/pentax/mnote-pentax-entry.c:186
msgid "Medium pet"
msgstr "Pośrednie zwierzę"

#: libexif/pentax/mnote-pentax-entry.c:188
#: libexif/pentax/mnote-pentax-entry.c:296
msgid "Candlelight"
msgstr "Światło świecy"

#: libexif/pentax/mnote-pentax-entry.c:189
msgid "Natural skin tone"
msgstr "Naturalna tonacja skóry"

#: libexif/pentax/mnote-pentax-entry.c:190
msgid "Synchro sound record"
msgstr "Synchroniczny zapis dźwięku"

#: libexif/pentax/mnote-pentax-entry.c:191
msgid "Frame composite"
msgstr "Złożenie klatek"

#: libexif/pentax/mnote-pentax-entry.c:194
msgid "Auto, did not fire"
msgstr "Automatyczny, nie uruchomił się"

#: libexif/pentax/mnote-pentax-entry.c:196
msgid "Auto, did not fire, red-eye reduction"
msgstr "Automatyczny, nie uruchomił się, redukcja czerwonych oczu"

#: libexif/pentax/mnote-pentax-entry.c:197
msgid "Auto, fired"
msgstr "Automatyczny, uruchomił się"

#: libexif/pentax/mnote-pentax-entry.c:199
msgid "Auto, fired, red-eye reduction"
msgstr "Automatyczny, uruchomił się, redukcja czerwonych oczu"

#: libexif/pentax/mnote-pentax-entry.c:201
msgid "On, wireless"
msgstr "Włączony, bezprzewodowy"

#: libexif/pentax/mnote-pentax-entry.c:202
msgid "On, soft"
msgstr "Włączony, miękki"

#: libexif/pentax/mnote-pentax-entry.c:203
msgid "On, slow-sync"
msgstr "Włączony, powolna synchronizacja"

#: libexif/pentax/mnote-pentax-entry.c:204
msgid "On, slow-sync, red-eye reduction"
msgstr "Włączony, powolna synchronizacja, redukcja czerwonych oczu"

#: libexif/pentax/mnote-pentax-entry.c:205
msgid "On, trailing-curtain sync"
msgstr "Włączony, synchronizacja z końcem migawki"

#: libexif/pentax/mnote-pentax-entry.c:213
msgid "AF-S"
msgstr "AF-S"

#: libexif/pentax/mnote-pentax-entry.c:214
msgid "AF-C"
msgstr "AF-C"

#: libexif/pentax/mnote-pentax-entry.c:217
msgid "Upper-left"
msgstr "Lewy górny róg"

#: libexif/pentax/mnote-pentax-entry.c:218
msgid "Top"
msgstr "Góra"

#: libexif/pentax/mnote-pentax-entry.c:219
msgid "Upper-right"
msgstr "Prawy górny róg"

#: libexif/pentax/mnote-pentax-entry.c:221
msgid "Mid-left"
msgstr "Lewy środek"

#: libexif/pentax/mnote-pentax-entry.c:223
msgid "Mid-right"
msgstr "Prawy środek"

#: libexif/pentax/mnote-pentax-entry.c:225
msgid "Lower-left"
msgstr "Lewy dolny róg"

#: libexif/pentax/mnote-pentax-entry.c:226
msgid "Bottom"
msgstr "Dół"

#: libexif/pentax/mnote-pentax-entry.c:227
msgid "Lower-right"
msgstr "Prawy dolny róg"

#: libexif/pentax/mnote-pentax-entry.c:228
msgid "Fixed center"
msgstr "Sztywny środek"

#: libexif/pentax/mnote-pentax-entry.c:232
msgid "Multiple"
msgstr "Wielopunktowy"

#: libexif/pentax/mnote-pentax-entry.c:234
msgid "Top-center"
msgstr "Górny środek"

#: libexif/pentax/mnote-pentax-entry.c:240
msgid "Bottom-center"
msgstr "Dolny środek"

#: libexif/pentax/mnote-pentax-entry.c:257
msgid "User selected"
msgstr "Wybrane przez użytkownika"

#: libexif/pentax/mnote-pentax-entry.c:282
msgid "3008x2008 or 3040x2024"
msgstr "3008x2008 lub 3040x2024"

#: libexif/pentax/mnote-pentax-entry.c:293
msgid "Digital filter?"
msgstr "Filtr cyfrowy?"

#: libexif/pentax/mnote-pentax-entry.c:374
#: libexif/pentax/mnote-pentax-entry.c:383
#, c-format
msgid "Internal error (unknown value %i %i)"
msgstr "Błąd wewnętrzny (nieznana wartość %i %i)"

#: libexif/pentax/mnote-pentax-tag.c:35 libexif/pentax/mnote-pentax-tag.c:63
msgid "Capture Mode"
msgstr "Tryb zdjęcia"

#: libexif/pentax/mnote-pentax-tag.c:36 libexif/pentax/mnote-pentax-tag.c:70
#: libexif/pentax/mnote-pentax-tag.c:129
msgid "Quality Level"
msgstr "Jakość"

#: libexif/pentax/mnote-pentax-tag.c:54
msgid "ISO Speed"
msgstr "Szybkość ISO"

#: libexif/pentax/mnote-pentax-tag.c:56
msgid "Colors"
msgstr "Kolory"

#: libexif/pentax/mnote-pentax-tag.c:59
msgid "PrintIM Settings"
msgstr "Ustawienia PrintIM"

#: libexif/pentax/mnote-pentax-tag.c:60 libexif/pentax/mnote-pentax-tag.c:131
msgid "Time Zone"
msgstr "Strefa czasowa"

#: libexif/pentax/mnote-pentax-tag.c:61
msgid "Daylight Savings"
msgstr "Zmiana czasu"

#: libexif/pentax/mnote-pentax-tag.c:64
msgid "Preview Size"
msgstr "Rozmiar podglądu"

#: libexif/pentax/mnote-pentax-tag.c:65
msgid "Preview Length"
msgstr "Długość podglądu"

#: libexif/pentax/mnote-pentax-tag.c:66 libexif/pentax/mnote-pentax-tag.c:122
msgid "Preview Start"
msgstr "Start podglądu"

#: libexif/pentax/mnote-pentax-tag.c:67
msgid "Model Identification"
msgstr "Identyfikacja modelu"

#: libexif/pentax/mnote-pentax-tag.c:68
msgid "Date"
msgstr "Data"

#: libexif/pentax/mnote-pentax-tag.c:69
msgid "Time"
msgstr "Czas"

#: libexif/pentax/mnote-pentax-tag.c:75
msgid "AF Point Selected"
msgstr "Wybrany punkt AF"

#: libexif/pentax/mnote-pentax-tag.c:76
msgid "Auto AF Point"
msgstr "Automatyczny wybór punktu AF"

#: libexif/pentax/mnote-pentax-tag.c:77
msgid "Focus Position"
msgstr "Położenie ogniska"

#: libexif/pentax/mnote-pentax-tag.c:80
msgid "ISO Number"
msgstr "Liczba ISO"

#: libexif/pentax/mnote-pentax-tag.c:83
msgid "Auto Bracketing"
msgstr "Auto bracketing"

#: libexif/pentax/mnote-pentax-tag.c:85
msgid "White Balance Mode"
msgstr "Tryb balansu bieli"

#: libexif/pentax/mnote-pentax-tag.c:93
msgid "World Time Location"
msgstr "Strefa czasowa"

#: libexif/pentax/mnote-pentax-tag.c:94
msgid "Hometown City"
msgstr "Miasto zamieszkania"

#: libexif/pentax/mnote-pentax-tag.c:95
msgid "Destination City"
msgstr "Miasto docelowe"

#: libexif/pentax/mnote-pentax-tag.c:96
msgid "Hometown DST"
msgstr "Zmiana czasu w miejscu zamieszkania"

#: libexif/pentax/mnote-pentax-tag.c:96
msgid "Home Daylight Savings Time"
msgstr "Rodzaj czasu w miejscu zamieszkania"

#: libexif/pentax/mnote-pentax-tag.c:97
msgid "Destination DST"
msgstr "Zmiana czasu w miejscu docelowym"

#: libexif/pentax/mnote-pentax-tag.c:97
msgid "Destination Daylight Savings Time"
msgstr "Rodzaj czasu w miejscu docelowym"

#: libexif/pentax/mnote-pentax-tag.c:99
msgid "Image Processing"
msgstr "Przetwarzanie obrazu"

#: libexif/pentax/mnote-pentax-tag.c:100
msgid "Picture Mode (2)"
msgstr "Tryb zdjęć (2)"

#: libexif/pentax/mnote-pentax-tag.c:103
msgid "Image Area Offset"
msgstr "Przesunięcie obszaru zdjęcia"

#: libexif/pentax/mnote-pentax-tag.c:104
msgid "Raw Image Size"
msgstr "Rozmiar surowego zdjęcia"

#: libexif/pentax/mnote-pentax-tag.c:105
msgid "Autofocus Points Used"
msgstr "Liczba punktów AF"

#: libexif/pentax/mnote-pentax-tag.c:107
msgid "Camera Temperature"
msgstr "Temperatura aparatu"

#: libexif/pentax/mnote-pentax-tag.c:110
msgid "Image Tone"
msgstr "Tonacja zdjęcia"

#: libexif/pentax/mnote-pentax-tag.c:111
msgid "Shake Reduction Info"
msgstr "Informacja o redukcji wstrząsów"

#: libexif/pentax/mnote-pentax-tag.c:112
msgid "Black Point"
msgstr "Czarny punkt"

#: libexif/pentax/mnote-pentax-tag.c:114
msgid "AE Info"
msgstr "Informacje o AE"

#: libexif/pentax/mnote-pentax-tag.c:115
msgid "Lens Info"
msgstr "Informacje o obiektywie"

#: libexif/pentax/mnote-pentax-tag.c:116
msgid "Flash Info"
msgstr "Informacje o fleszu"

#: libexif/pentax/mnote-pentax-tag.c:117
msgid "Camera Info"
msgstr "Informacje o aparacie"

#: libexif/pentax/mnote-pentax-tag.c:118
msgid "Battery Info"
msgstr "Informacje o baterii"

#: libexif/pentax/mnote-pentax-tag.c:119
msgid "Hometown City Code"
msgstr "Kod miejsca zamieszkania"

#: libexif/pentax/mnote-pentax-tag.c:120
msgid "Destination City Code"
msgstr "Kod miejsca docelowego"

#: libexif/pentax/mnote-pentax-tag.c:125
msgid "Object Distance"
msgstr "Odległość obiektu"

#: libexif/pentax/mnote-pentax-tag.c:125
msgid "Distance of photographed object in millimeters."
msgstr "Odległość fotografowanego obiektu w milimetrach."

#: libexif/pentax/mnote-pentax-tag.c:126
msgid "Flash Distance"
msgstr "Odległość flesza"

#: libexif/pentax/mnote-pentax-tag.c:132
msgid "Bestshot Mode"
msgstr "Tryb Bestshot"

#: libexif/pentax/mnote-pentax-tag.c:133
msgid "CCS ISO Sensitivity"
msgstr "Czułość ISO CCD"

#: libexif/pentax/mnote-pentax-tag.c:135
msgid "Enhancement"
msgstr "Rozszerzona"

#: libexif/pentax/mnote-pentax-tag.c:136
msgid "Finer"
msgstr "Lepsza"

#: test/nls/test-nls.c:20 test/nls/test-nls.c:23 test/nls/test-nls.c:24
msgid "[DO_NOT_TRANSLATE_THIS_MARKER]"
msgstr "[DO_NOT_TRANSLATE_THIS_MARKER]"
