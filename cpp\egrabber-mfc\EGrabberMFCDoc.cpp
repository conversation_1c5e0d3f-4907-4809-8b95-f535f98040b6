
// EGrabberMFCDoc.cpp : implementation of the CEGrabberMFCDoc class
//

#include "stdafx.h"
// SHARED_HANDLERS can be defined in an ATL project implementing preview, thumbnail
// and search filter handlers and allows sharing of document code with that project.
#ifndef SHARED_HANDLERS
#include "EGrabberMFC.h"
#endif

#include "EGrabberMFCDoc.h"
#include "EGrabberMFCView.h"
#include <propkey.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CEGrabberMFCDoc

IMPLEMENT_DYNCREATE(CEGrabberMFCDoc, CDocument)

BEGIN_MESSAGE_MAP(CEGrabberMFCDoc, CDocument)
    ON_UPDATE_COMMAND_UI(ID_STARTSTREAM, &CEGrabberMFCDoc::OnUpdateStartstream)
    ON_UPDATE_COMMAND_UI(ID_STOPSTREAM, &CEGrabberMFCDoc::OnUpdateStopstream)
END_MESSAGE_MAP()

CEGrabberMFCDoc::~CEGrabberMFCDoc()
{
}

BOOL CEGrabberMFCDoc::OnNewDocument()
{
    if (!CDocument::OnNewDocument()) return FALSE;
    SetTitle("EGrabber");
    return TRUE;
}

// CEGrabberMFCDoc serialization
void CEGrabberMFCDoc::Serialize(CArchive& ar)
{
    if (ar.IsStoring())
    {
    // TODO: add storing code here
    }
    else
    {
    // TODO: add loading code here
    }
}

#ifdef _DEBUG
void CEGrabberMFCDoc::AssertValid() const
{
    CDocument::AssertValid();
}

void CEGrabberMFCDoc::Dump(CDumpContext& dc) const
{
    CDocument::Dump(dc);
}
#endif //_DEBUG

// CEGrabberMFCDoc commands
void CEGrabberMFCDoc::OnUpdateStartstream(CCmdUI *pCmdUI) {
    globalEGrabber.startStream();
}

void CEGrabberMFCDoc::OnUpdateStopstream(CCmdUI *pCmdUI) {
    globalEGrabber.stopStream();
}

// EGrabber implementations
void MyEGrabber::startStream() {
    cs.Lock();
    try {
        start();
    }
    catch (...) {}
    cs.Unlock();
}

void MyEGrabber::stopStream() {
    cs.Lock();
    try {
        stop();
    }
    catch (...) {}
    cs.Unlock();
}

void MyEGrabber::initBitmap() {
    bitmapInfo = (LPBITMAPINFO)malloc(sizeof(BITMAPINFOHEADER) + sizeof(RGBQUAD));
    bitmapInfo->bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bitmapInfo->bmiHeader.biPlanes = 1;
    bitmapInfo->bmiHeader.biBitCount = 24;
    bitmapInfo->bmiHeader.biCompression = BI_RGB;
    bitmapInfo->bmiHeader.biWidth = (LONG)getWidth();
    bitmapInfo->bmiHeader.biHeight = -(LONG)getHeight();
    bitmapInfo->bmiHeader.biSizeImage = 0;
    bitmapInfo->bmiHeader.biXPelsPerMeter = 0;
    bitmapInfo->bmiHeader.biYPelsPerMeter = 0;
    bitmapInfo->bmiHeader.biClrUsed = 0;
    bitmapInfo->bmiHeader.biClrImportant = 0;
    RGBQUAD* bmiColors = (RGBQUAD*)(bitmapInfo->bmiColors);
    for (size_t index = 0; index < 1; ++index) {
        bmiColors[index].rgbBlue = (BYTE)index;
        bmiColors[index].rgbGreen = (BYTE)index;
        bmiColors[index].rgbRed = (BYTE)index;
        bmiColors[index].rgbReserved = 0;
    }
}

void MyEGrabber::onNewBufferEvent(const NewBufferData& data) {
    cs.Lock();
    try {
        if (currentBufferData.bh) {
            push(currentBufferData);
        }
        currentBufferData = data;
        if (!pendingUpdate) {
            CMainFrame *pMain = (CMainFrame *)AfxGetApp()->m_pMainWnd;
            CView *pView = (CView *)pMain->GetActiveView();
            if (pView && pView->PostMessage(UPDATE_IMAGE)) {
                pendingUpdate = true;
            }
        }
    }
    catch (...) {}
    cs.Unlock();
}
