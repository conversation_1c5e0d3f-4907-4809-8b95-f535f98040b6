#ifndef JFIF_HEADER_FILE
#define JFIF_HEADER_FILE

#include <cstring>
#include <stdexcept>

namespace Jfif {

namespace {

static const unsigned char jfif_header[] = { 0xff, 0xd8, 0xff, 0xe0 };

static inline uint8_t *skipJFIFHeader(uint8_t *image, size_t &size) {
    if (image == 0 || size <= sizeof(jfif_header)) {
        throw std::runtime_error("Not enough data");
    }
    if (memcmp(image, jfif_header, sizeof(jfif_header))) {
        throw std::runtime_error("APP0 marker not found");
    }
    uint8_t *p = image + sizeof(jfif_header);
    size -= sizeof(jfif_header);
    if (size > 2) {
        size_t len = (*p++) << 8;
        len += *p++;
        p += len - 2;
        if (size >= len) {
            size -= len;
        } else {
            throw std::runtime_error("Unexpected segment length");
        }
    } else {
        throw std::runtime_error("Unexpected end of JFIF header");
    }
    return p;
}

}

}

#endif
