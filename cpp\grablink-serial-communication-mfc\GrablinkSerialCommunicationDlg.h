/*
+-------------------------------- DISCLAIMER ---------------------------------+
|                                                                             |
| This application program is provided to you free of charge as an example.   |
| Despite the considerable efforts of Euresys personnel to create a usable    |
| example, you should not assume that this program is error-free or suitable  |
| for any purpose whatsoever.                                                 |
|                                                                             |
| EURESYS does not give any representation, warranty or undertaking that this |
| program is free of any defect or error or suitable for any purpose. EURESYS |
| shall not be liable, in contract, in torts or otherwise, for any damages,   |
| loss, costs, expenses or other claims for compensation, including those     |
| asserted by third parties, arising out of or in connection with the use of  |
| this program.                                                               |
|                                                                             |
+-----------------------------------------------------------------------------+
*/

// GrablinkSerialCommunicationDlg.h : header file
//

#pragma once

#include "clserialLibrary.h"

// CGrablinkSerialCommunicationDlg dialog
class CGrablinkSerialCommunicationDlg : public CDialogEx
{
// Construction
public:
    CGrablinkSerialCommunicationDlg(CWnd* pParent = NULL);    // standard constructor
    // Camera Link serial library object
    Euresys::clseregl::ClSerialLibrary cl;
    // Handle to the serial port
    Euresys::clseregl::hSerRef serialRef;
    // Index of the serial port
    UINT32 serialIndex;
    // Previous baud rate selected
    int previousSelectedBaudRate;

// Dialog Data
#ifdef AFX_DESIGN_TIME
    enum { IDD = IDD_GRABLINKSERIALCOMMUNICATION_DIALOG };
#endif

protected:
    virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support


// Implementation
protected:
    HICON m_hIcon;
    CComboBox availablePorts;
    CComboBox baudRates;
    CButton openButton;
    CButton closeButton;
    CButton sendButton;
    CButton readButton;
    CStatic sendCommand;
    CStatic readMessage;

    // Generated message map functions
    virtual BOOL OnInitDialog();
    afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
    afx_msg void OnPaint();
    afx_msg HCURSOR OnQueryDragIcon();
    DECLARE_MESSAGE_MAP()
public:
    afx_msg void OnCbnSelchangeComboPorts();
    afx_msg void OnBnClickedOpenbutton();
    afx_msg void OnBnClickedClosebutton();
    afx_msg void OnBnClickedSendbutton();
    afx_msg void OnBnClickedReadbutton();
    afx_msg void OnCbnSelchangeBaudrate();
};
