﻿Public Class Configurator
  Private Shared Function fpsToMicroseconds(fps As UInt64) As UInt64
    If fps = 0 Then
      Return 0
    Else
      Return (1000000 + fps - 1) / fps
    End If
  End Function

  Shared Sub Configure(ByRef grabber As Euresys.Managed.CoaxlinkGrabber)
    grabber.SetString(Of Euresys.Managed.DevicePort)("CameraControlMethod", "RG")
    grabber.SetInteger(Of Euresys.Managed.DevicePort)("ExposureReadoutOverlap", 1)
    grabber.SetString(Of Euresys.Managed.DevicePort)("CycleTriggerSource", "Immediate")
    grabber.SetInteger(Of Euresys.Managed.DevicePort)("ExposureTime", 4000)
    grabber.SetInteger(Of Euresys.Managed.DevicePort)("CycleTargetPeriod", fpsToMicroseconds(70))
    grabber.SetInteger(Of Euresys.Managed.DevicePort)("StrobeDuration", 0)
    grabber.SetInteger(Of Euresys.Managed.DevicePort)("StrobeDelay", 100)

    grabber.SetString(Of Euresys.Managed.RemotePort)("ExposureMode", "TriggerWidth")
    grabber.SetString(Of Euresys.Managed.RemotePort)("TriggerSource", "Trigger")
    grabber.SetString(Of Euresys.Managed.RemotePort)("TriggerActivation", "RisingEdge")
    grabber.SetString(Of Euresys.Managed.RemotePort)("PixelFormat", "Mono8")
    grabber.SetString(Of Euresys.Managed.RemotePort)("LinkConfig", "Link1Speed6250")
    grabber.SetInteger(Of Euresys.Managed.RemotePort)("Width", 2048)
    grabber.SetInteger(Of Euresys.Managed.RemotePort)("Height", 2048)
  End Sub
End Class
