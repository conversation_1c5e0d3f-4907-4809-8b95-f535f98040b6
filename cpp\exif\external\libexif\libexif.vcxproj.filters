﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-byte-order.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-content.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-data.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-entry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-format.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-ifd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-loader.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-log.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-mem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-mnote-data.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\exif-mnote-data-canon.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\mnote-canon-entry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\mnote-canon-tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\exif-mnote-data-fuji.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-entry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\exif-mnote-data-olympus.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-entry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\exif-mnote-data-pentax.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-entry.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-byte-order.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-content.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-data-type.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-data.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-entry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-format.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-ifd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-loader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mnote-data-priv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mnote-data.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\exif.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\i18n.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\exif-mnote-data-canon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\mnote-canon-entry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\mnote-canon-tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\exif-mnote-data-fuji.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-entry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\exif-mnote-data-olympus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-entry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\exif-mnote-data-pentax.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-entry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>