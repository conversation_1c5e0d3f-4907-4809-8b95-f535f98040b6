﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Samples">
      <UniqueIdentifier>{e07480d1-7627-410d-97df-1ee09a911add}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tools">
      <UniqueIdentifier>{24815b70-bbf5-4646-97fe-6270f41bf66c}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="samples\100-grabn.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\101-singleframe.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\102-action-grab.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\105-area-scan-grabn.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\106-line-scan-grabn.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\110-get-string-list.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\120-converter.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\130-using-buffer.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\140-genapi-command.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\150-discover.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\200-grabn-callbacks.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\201-grabn-pop-oneof.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\210-show-all-grabbers.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\211-show-all-grabbers-ro.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\212-create-all-grabbers.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\213-egrabbers.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\220-get-announced-handles.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\221-queue-buffer-ranges.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\230-script-vars.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\231-script-var.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\240-user-memory.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\241-multi-part.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\250-using-lut.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\260-recorder-read-write.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\261-recorder-parameters.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\270-multicast-master.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\271-multicast-receiver.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\300-events-mt-cic.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\301-events-st-all.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\302-cxp-connector-detection.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\310-high-frame-rate.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\311-high-frame-rate.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\312-part-timestamps.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\320-cl-serial-cli.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\321-gencp-serial.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\330-metadata-insertion.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\340-dma-roi.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\341-dma-deinterlace.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\342-dma-roi-deinterlace.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\500-grabn-cuda-process.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\501-all-grabbers-cuda-process.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\502-grabn-cuda-copy-and-process.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\600-thread-start-stop-callbacks.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\610-line-scan-array.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\620-multiple-camera.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\650-multistream.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\700-memento.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\800-process-latest-buffer.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="tools\main.cpp">
      <Filter>Tools</Filter>
    </ClCompile>
    <ClCompile Include="tools\tools.cpp">
      <Filter>Tools</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="samples\101-singleframe.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\102-action-grab.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\200-grabn-callbacks.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\200-grabn-callbacks.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\201-grabn-pop-oneof.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\201-grabn-pop-oneof.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\210-show-all-grabbers.show.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\211-show-all-grabbers-ro.show.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\212-create-all-grabbers.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\213-egrabbers.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\221-queue-buffer-ranges.bufferSet1.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\221-queue-buffer-ranges.bufferSet2.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\231-script-var.greetingsFromCpp.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\231-script-var.numbersFromScript.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\250-using-lut.userLUT.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\270-multicast-master.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\271-multicast-receiver.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\300-events-mt-cic.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\300-events-mt-cic.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\301-events-st-all.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\301-events-st-all.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\302-cxp-connector-detection.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\302-cxp-connector-detection.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\330-metadata-insertion.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\330-metadata-insertion.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\500-grabn-cuda-process.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\501-all-grabbers-cuda-process.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\502-grabn-cuda-copy-and-process.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\600-thread-start-stop-callbacks.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\600-thread-start-stop-callbacks.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\610-line-scan-array.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\610-line-scan-array.teardown.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\650-multistream.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\config-rg.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\config-sc.js">
      <Filter>Samples</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="samples\500-grabn-cuda-process.h">
      <Filter>Samples</Filter>
    </ClInclude>
    <ClInclude Include="samples\501-all-grabbers-cuda-process.h">
      <Filter>Samples</Filter>
    </ClInclude>
    <ClInclude Include="samples\502-grabn-cuda-copy-and-process.h">
      <Filter>Samples</Filter>
    </ClInclude>
    <ClInclude Include="tools\tools.h">
      <Filter>Tools</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="samples\500-grabn-cuda-process.cu">
      <Filter>Samples</Filter>
    </CudaCompile>
    <CudaCompile Include="samples\501-all-grabbers-cuda-process.cu">
      <Filter>Samples</Filter>
    </CudaCompile>
    <CudaCompile Include="samples\502-grabn-cuda-copy-and-process.cu">
      <Filter>Samples</Filter>
    </CudaCompile>
  </ItemGroup>
</Project>