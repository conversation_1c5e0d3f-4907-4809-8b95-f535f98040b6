#include <EGrabber.h>
#include <string.h>

#include "../tools/tools.h"
#include "../exif/exif.h"
#include "../jfif/jfif.h"

/*
 * Dependency: libexif
 * Ubuntu/Debian: sudo apt install libexif-dev
 * macOS: brew install libexif
 */

using namespace Euresys;

namespace {

static const int jpegStreamIndex = 1;

class MyGrabber: public EGrabber<CallbackSingleThread> {
    public:
        MyGrabber(EGenTL &gentl, int interfaceIndex, int deviceIndex)
        : EGrabber<CallbackSingleThread>(gentl, interfaceIndex, deviceIndex, jpegStreamIndex)
        , interfaceIndex(interfaceIndex)
        , deviceIndex(deviceIndex)
        , outputPath(Tools::getEnv("sample-output-path"))
        , frameCounter(0)
        , deviceVendor("Unknown")
        , deviceModel("Unknown")
        {
            runScript(Tools::getScriptPath("setup"));
            reallocBuffers(5);
            try {
                deviceVendor = getString<Euresys::DeviceModule>("DeviceVendorName");
            } catch (const gentl_error &) {
            }
            try {
                deviceModel = getString<Euresys::DeviceModule>("DeviceModelName");
            } catch (const gentl_error &) {
            }
        }
        ~MyGrabber() {
            stop();
        }

    private:
        int interfaceIndex;
        int deviceIndex;
        const std::string outputPath;
        unsigned int frameCounter;
        std::string deviceVendor;
        std::string deviceModel;
        Exif::ExifMemory exifMemory;

        std::string getFrameName() {
            std::ostringstream sstr;
            sstr << "frame-" << interfaceIndex << "-" << deviceIndex << "." << frameCounter++ << ".jpg";
            std::string imageName(sstr.str());
            return Tools::join2Path(outputPath, imageName);
        }


        void createExifFile(const std::string &fileName, ScopedBuffer &buffer) {
            Tools::log(fileName);
            size_t width = buffer.getInfo<size_t>(gc::BUFFER_INFO_WIDTH);
            size_t height = buffer.getInfo<size_t>(gc::BUFFER_INFO_HEIGHT);
            uint8_t *image = buffer.getInfo<uint8_t *>(gc::BUFFER_INFO_BASE);
            size_t size = buffer.getInfo<size_t>(gc::BUFFER_INFO_DATA_SIZE);
            uint64_t ts = buffer.getInfo<uint64_t>(gc::BUFFER_INFO_TIMESTAMP);
            uint8_t *jpeg = Jfif::skipJFIFHeader(image, size);

            Exif::ExifResources e(exifMemory, jpeg, size);

            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_IMAGE_WIDTH, static_cast<ExifShort>(width));
            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_IMAGE_LENGTH, static_cast<ExifShort>(height));

            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_PIXEL_X_DIMENSION, static_cast<ExifLong>(width));
            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_PIXEL_Y_DIMENSION, static_cast<ExifLong>(height));

            e.addTag(EXIF_IFD_0, EXIF_TAG_MAKE, deviceVendor);
            e.addTag(EXIF_IFD_0, EXIF_TAG_MODEL, deviceModel);

            std::string strTs("TIMESTAMP=");
            strTs += Tools::formatTimestamp(ts);
            e.addUserComment(strTs);

            e.writeFile(fileName);
        }

        virtual void onNewBufferEvent(const NewBufferData& data) {
            ScopedBuffer buffer(*this, data); // re-queues buffer automatically
            try {
                createExifFile(getFrameName(), buffer);
            } catch(const std::exception & ex) {
                Tools::log(std::string("createExifFile failed: ") + ex.what());
            } catch (...) {
                Tools::log("createExifFile failed");
            }
        }
};

}

const unsigned int DURATION = 10;

static void sample() {
    EGenTL genTL;
    MyGrabber s0(genTL, 0, 0);
    MyGrabber s1(genTL, 0, 1);
    MyGrabber s2(genTL, 0, 2);
    MyGrabber s3(genTL, 0, 3);
    s0.start();
    s1.start();
    s2.start();
    s3.start();
    Tools::log("Grabbing for " + Tools::toString(DURATION) + " seconds");
    Tools::sleepMs(1000 * DURATION);
}

static Tools::Sample addSample(__FILE__, "Acquire data from 4 JPEG encoded data streams and produce EXIF files", sample);
