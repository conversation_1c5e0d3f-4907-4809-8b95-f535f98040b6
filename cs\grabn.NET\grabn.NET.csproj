﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="EGrabber.NET">
      <HintPath Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))'">C:\Program Files\Euresys\eGrabber\clr\anycpu\EGrabber.NET.dll</HintPath>
      <HintPath Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::OSX)))'">/usr/local/opt/euresys/egrabber/clr/anycpu/EGrabber.NET.dll</HintPath>
      <HintPath Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Linux)))'">/opt/euresys/egrabber/clr/anycpu/EGrabber.NET.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
