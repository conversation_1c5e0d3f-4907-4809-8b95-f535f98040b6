
// EGrabberMFC.h : main header file for the EGrabberMFC application
//
#pragma once

#ifndef __AFXWIN_H__
	#error "include 'stdafx.h' before including this file for PCH"
#endif

#include "resource.h"       // main symbols


// CEGrabberMFCApp:
// See EGrabberMFC.cpp for the implementation of this class
//

class CEGrabberMFCApp : public CWinApp
{
public:
    CEGrabberMFCApp();


// Overrides
public:
	   virtual BOOL InitInstance();
	   virtual int ExitInstance();

    // Implementation
	   afx_msg void OnAppAbout();
	   DECLARE_MESSAGE_MAP()
};

extern CEGrabberMFCApp theApp;
