#include <stdexcept>
#include "window.h"

#ifndef WINDOW_TITLE
#define WINDOW_TITLE "eGrabber sample"
#endif

Window::Window(int width, int height) 
    : window(0)
    , renderer(0)
    , texture(0) {
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        throw std::runtime_error("SDL_Init failed\n");
    }
    window = SDL_CreateWindow(WINDOW_TITLE, SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED, width, height, 0);
    if (!window) {
        cleanup();
        throw std::runtime_error("SDL_CreateWindow failed\n");
    }
    renderer = SDL_CreateRenderer(window, -1, 0);
    if (!renderer) {
        cleanup();
        throw std::runtime_error("SDL_CreateRenderer failed\n");
    }
    texture = SDL_CreateTexture(renderer, SDL_PIXELFORMAT_RGB24, SDL_TEXTUREACCESS_STREAMING, width, height);
    if (!texture) {
        cleanup();
        throw std::runtime_error("SDL_CreateTexture failed\n");
    }
}

Window::~Window() {
    cleanup();
}

void Window::updateImage(void *data, size_t size) {
    void *pixels;
    int pitch;
    SDL_LockTexture(texture, NULL, &pixels, &pitch);
    memcpy(pixels, data, size);
    SDL_UnlockTexture(texture);
    SDL_RenderCopy(renderer, texture, NULL, NULL);
    SDL_RenderPresent(renderer);
}

bool Window::isAlive() {
    SDL_Event event;
    if (!SDL_PollEvent(&event)) {
        return true;
    }
    if (event.type == SDL_QUIT || event.key.keysym.sym == SDLK_ESCAPE) {
        return false;
    }
    return true;
}

void Window::cleanup() {
    if (texture) {
        SDL_DestroyTexture(texture);
    }
    if (renderer) {
        SDL_DestroyRenderer(renderer);
    }
    if (window) {
        SDL_DestroyWindow(window);
    }
    SDL_Quit();
}
