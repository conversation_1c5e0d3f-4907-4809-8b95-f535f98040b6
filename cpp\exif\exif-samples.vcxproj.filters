﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Samples">
      <UniqueIdentifier>{562a0f56-05a7-49e3-a46b-7abc22080821}</UniqueIdentifier>
    </Filter>
    <Filter Include="Exif">
      <UniqueIdentifier>{7affa383-9cdf-4872-b575-705d0777e2d9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Jfif">
      <UniqueIdentifier>{02fa8ae3-9104-4786-842e-50312fb4e699}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="tools\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tools\tools.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="samples\100-jpeg-exif.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
    <ClCompile Include="samples\200-jpeg-preview-exif.cpp">
      <Filter>Samples</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="tools\tools.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="exif\exif.h">
      <Filter>Exif</Filter>
    </ClInclude>
    <ClInclude Include="jfif\jfif.h">
      <Filter>Jfif</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="samples\100-jpeg-exif.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\200-jpeg-preview-exif.setup.js">
      <Filter>Samples</Filter>
    </None>
    <None Include="samples\config-sc.js">
      <Filter>Samples</Filter>
    </None>
  </ItemGroup>
</Project>