
// EGrabberMFCDoc.h : interface of the CEGrabberMFCDoc class
//

#pragma once
#include <afxwin.h>
#include "tools/tools.h"
#include <EGrabber.h>
#include <FormatConverter.h>
#include "MainFrm.h"

#define UPDATE_IMAGE WM_USER + 100

using namespace Euresys;

class CEGrabberMFCDoc;

class MyEGrabber: public EGrabberCallbackSingleThread {
    public:
        MyEGrabber(EGenTL &eGentl)
        : EGrabberCallbackSingleThread(eGentl)
        , converter(eGentl)
        , currentBufferData({0})
        , pendingUpdate(false)
        , bitmapInfo(NULL)
        {
            initBitmap();
            runScript("config.js");
            reallocBuffers(10);
        }
        ~MyEGrabber() {
            stopStream();
            shutdown();
            delete bitmapInfo;
        }
        void startStream();
        void stopStream();
    private:
        void initBitmap();
        virtual void onNewBufferEvent(const NewBufferData& data);
    public:
        CCriticalSection cs;
        FormatConverter converter;
        NewBufferData currentBufferData;
        bool pendingUpdate;
        BITMAPINFO *bitmapInfo;
};

class CEGrabberMFCDoc : public CDocument {
    protected: // create from serialization only
        CEGrabberMFCDoc()
        : globalEGrabber(eGentl) {}
        DECLARE_DYNCREATE(CEGrabberMFCDoc)

        // Attributes
    public:
        EGenTL eGentl;
        MyEGrabber globalEGrabber;
    public:
        virtual BOOL OnNewDocument();
        virtual void Serialize(CArchive& ar);
    #ifdef SHARED_HANDLERS
        virtual void InitializeSearchContent();
        virtual void OnDrawThumbnail(CDC& dc, LPRECT lprcBounds);
    #endif // SHARED_HANDLERS

    // Implementation
    public:
        virtual ~CEGrabberMFCDoc();
    #ifdef _DEBUG
        virtual void AssertValid() const;
        virtual void Dump(CDumpContext& dc) const;
    #endif

    protected:

    // Generated message map functions
    protected:
        DECLARE_MESSAGE_MAP()

    #ifdef SHARED_HANDLERS
        // Helper function that sets search content for a Search Handler
        void SetSearchContent(const CString& value);
    #endif // SHARED_HANDLERS
    public:
        afx_msg void OnUpdateStartstream(CCmdUI *pCmdUI);
        afx_msg void OnUpdateStopstream(CCmdUI *pCmdUI);
};
