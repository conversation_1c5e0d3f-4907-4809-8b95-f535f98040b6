egrabber-cuda-sdl2 sample program

This sample demonstrates the interoperability between eGrabber and CUDA.
RGB8 images are obtained from the framegrabber with e<PERSON>rab<PERSON> and then processed in a CUDA kernel. 
Each CUDA thread processes one pixel, inversing the luminance of each of its three components.
The modified images are displayed in a SDL2 window.

Notes:
- This sample requires the CUDA Toolkit. 
  The CUDA Toolkit can be downloaded from the following link: https://developer.nvidia.com/cuda-downloads
- This sample requires SDL2. The SDL2 libraries and headers are included in this package (Windows only).
  In Ubuntu/Debian systems, SDL2 can be installed with `sudo apt-get install libsdl2-dev`.
  Otherwise, they can be downloaded from the following link: https://github.com/libsdl-org/SDL/releases
- This sample was tested with CUDA 12.0, with both Visual Studio 2019 (Windows) and GCC 11.4 (Linux) compilers.
