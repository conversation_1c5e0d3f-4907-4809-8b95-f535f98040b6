
This is a list of major changes in SDL's version history.

---------------------------------------------------------------------------
2.30.0:
---------------------------------------------------------------------------

General:
* Added support for 2 bits-per-pixel indexed surface formats
* Added the function SDL_GameControllerGetSteamHandle() to get the Steam API handle for a controller, if available
* Added the event SDL_CONTROLLERSTEAMHANDLEUPDATED which is sent when the Steam API handle for a controller changes. This could also change the name, VID, and PID of the controller.
* Added the environment variable SDL_LOGGING to control default log output

macOS:
* Added the hint SDL_HINT_JOYSTICK_IOKIT to control whether the IOKit controller driver should be used
* Added the hint SDL_HINT_JOYSTICK_MFI to control whether the GCController controller driver should be used
* Added the hint SDL_HINT_RENDER_METAL_PREFER_LOW_POWER_DEVICE to choose whether high or low power GPU should be used for rendering, in the case where there are multiple GPUs available

Xbox:
* Added the function SDL_GDKGetDefaultUser()

---------------------------------------------------------------------------
2.28.2:
---------------------------------------------------------------------------

General:
* Added the hint SDL_HINT_JOYSTICK_WGI to control whether to use Windows.Gaming.Input for controllers


---------------------------------------------------------------------------
2.28.0:
---------------------------------------------------------------------------

General:
* Added SDL_HasWindowSurface() and SDL_DestroyWindowSurface() to switch between the window surface and rendering APIs
* Added a display event SDL_DISPLAYEVENT_MOVED which is sent when the primary monitor changes or displays change position relative to each other
* Added the hint SDL_HINT_ENABLE_SCREEN_KEYBOARD to control whether the on-screen keyboard should be shown when text input is active


---------------------------------------------------------------------------
2.26.0:
---------------------------------------------------------------------------

General:
* Updated OpenGL headers to the latest API from The Khronos Group Inc.
* Added SDL_GetWindowSizeInPixels() to get the window size in pixels, which may differ from the window coordinate size for windows with high-DPI support
* Added simulated vsync synchronization for the software renderer
* Added the mouse position to SDL_MouseWheelEvent
* Added SDL_ResetHints() to reset all hints to their default values
* Added SDL_GetJoystickGUIDInfo() to get device information encoded in a joystick GUID
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_XBOX_360 to control whether the HIDAPI driver for XBox 360 controllers should be used
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_XBOX_360_PLAYER_LED to control whether the player LEDs should be lit to indicate which player is associated with an Xbox 360 controller
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_XBOX_360_WIRELESS to control whether the HIDAPI driver for XBox 360 wireless controllers should be used
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_XBOX_ONE to control whether the HIDAPI driver for XBox One controllers should be used
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_XBOX_ONE_HOME_LED to control the brightness of the XBox One guide button LED
* Added support for PS3 controllers to the HIDAPI driver, enabled by default on macOS, controlled by the SDL_HINT_JOYSTICK_HIDAPI_PS3 hint
* Added support for Nintendo Wii controllers to the HIDAPI driver, not enabled by default, controlled by the SDL_HINT_JOYSTICK_HIDAPI_WII hint
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_WII_PLAYER_LED to control whether the player LED should be lit on the Nintendo Wii controllers
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_VERTICAL_JOY_CONS to control whether Nintendo Switch Joy-Con controllers will be in vertical mode when using the HIDAPI driver
* Added access to the individual left and right gyro sensors of the combined Joy-Cons controller
* Added a microsecond timestamp to SDL_SensorEvent and SDL_ControllerSensorEvent, when the hardware provides that information
* Added SDL_SensorGetDataWithTimestamp() and SDL_GameControllerGetSensorDataWithTimestamp() to retrieve the last sensor data with the associated microsecond timestamp
* Added the hint SDL_HINT_HIDAPI_IGNORE_DEVICES to have the SDL HID API ignore specific devices
* SDL_GetRevision() now includes more information about the SDL build, including the git commit hash if available

Windows:
* Added the hint SDL_HINT_MOUSE_RELATIVE_SYSTEM_SCALE to control whether the system mouse acceleration curve is used for relative mouse motion

macOS:
* Implemented vsync synchronization on macOS 12

Linux:
* Added SDL_SetPrimarySelectionText(), SDL_GetPrimarySelectionText(), and SDL_HasPrimarySelectionText() to interact with the X11 primary selection clipboard
* Added the hint SDL_HINT_VIDEO_WAYLAND_EMULATE_MOUSE_WARP to control whether mouse pointer warp emulation is enabled under Wayland

Android:
* Enabled IME soft keyboard input
* Added version checking to make sure the SDL Java and C code are compatible


---------------------------------------------------------------------------
2.24.0:
---------------------------------------------------------------------------

General:
* New version numbering scheme, similar to GLib and Flatpak.
    * An even number in the minor version (second component) indicates a production-ready stable release such as 2.24.0, which would have been 2.0.24 under the old system.
      * The patchlevel (micro version, third component) indicates a bugfix-only update: for example, 2.24.1 would be a bugfix-only release to fix bugs in 2.24.0, without adding new features.
    * An odd number in the minor version indicates a prerelease such as 2.23.0. Stable distributions should not use these prereleases.
      * The patchlevel indicates successive prereleases, for example 2.23.1 and 2.23.2 would be prereleases during development of the SDL 2.24.0 stable release.
* Added SDL_GetPointDisplayIndex() and SDL_GetRectDisplayIndex() to get the display associated with a point and rectangle in screen space
* Added SDL_bsearch(), SDL_crc16(), and  SDL_utf8strnlen() to the stdlib routines
* Added SDL_CPUPauseInstruction() as a macro in SDL_atomic.h
* Added SDL_size_mul_overflow() and SDL_size_add_overflow() for better size overflow protection
* Added SDL_ResetHint() to reset a hint to the default value
* Added SDL_ResetKeyboard() to reset SDL's internal keyboard state, generating key up events for all currently pressed keys
* Added the hint SDL_HINT_MOUSE_RELATIVE_WARP_MOTION to control whether mouse warping generates motion events in relative mode. This hint defaults off.
* Added the hint SDL_HINT_TRACKPAD_IS_TOUCH_ONLY to control whether trackpads are treated as touch devices or mice. By default touchpads are treated as mouse input.
* The hint SDL_HINT_JOYSTICK_HIDAPI_JOY_CONS now defaults on
* Added support for mini-gamepad mode for Nintendo Joy-Con controllers using the HIDAPI driver
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_COMBINE_JOY_CONS to control whether Joy-Con controllers are automatically merged into a unified gamepad when using the HIDAPI driver. This hint defaults on.
* The hint SDL_HINT_JOYSTICK_HIDAPI_SWITCH_HOME_LED can be set to a floating point value to set the brightness of the Home LED on Nintendo Switch controllers
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_JOYCON_HOME_LED to set the Home LED brightness for the Nintendo Joy-Con controllers. By default the Home LED is not modified.
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_SWITCH_PLAYER_LED to control whether the player LED should be lit on the Nintendo Joy-Con controllers
* Added support for Nintendo Online classic controllers using the HIDAPI driver
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_NINTENDO_CLASSIC to control whether the HIDAPI driver for Nintendo Online classic controllers should be used
* Added support for the NVIDIA Shield Controller to the HIDAPI driver, supporting rumble and battery status
* Added support for NVIDIA SHIELD controller to the HIDAPI driver, and a hint SDL_HINT_JOYSTICK_HIDAPI_SHIELD to control whether this is used
* Added functions to get the platform dependent name for a joystick or game controller:
    * SDL_JoystickPathForIndex()
    * SDL_JoystickPath()
    * SDL_GameControllerPathForIndex()
    * SDL_GameControllerPath()
* Added SDL_GameControllerGetFirmwareVersion() and SDL_JoystickGetFirmwareVersion(), currently implemented for DualSense(tm) Wireless Controllers using HIDAPI
* Added SDL_JoystickAttachVirtualEx() for extended virtual controller support
* Added joystick event SDL_JOYBATTERYUPDATED for when battery status changes
* Added SDL_GUIDToString() and SDL_GUIDFromString() to convert between SDL GUID and string
* Added SDL_HasLSX() and SDL_HasLASX() to detect LoongArch SIMD support
* Added SDL_GetOriginalMemoryFunctions()
* Added SDL_GetDefaultAudioInfo() to get the name and format of the default audio device, currently implemented for PipeWire, PulseAudio, WASAPI, and DirectSound
* Added HIDAPI driver for the NVIDIA SHIELD controller (2017 model) to enable support for battery status and rumble
* Added support for opening audio devices with 3 or 5 channels (2.1, 4.1). All channel counts from Mono to 7.1 are now supported.
* Rewrote audio channel converters used by SDL_AudioCVT, based on the channel matrix coefficients used as the default for FAudio voices
* SDL log messages are no longer limited to 4K and can be any length
* Fixed a long-standing calling convention issue with dynapi affecting OpenWatcom or OS/2 builds

Windows:
* Added initial support for building for Windows and Xbox with Microsoft's Game Development Kit (GDK), see docs/README-gdk.md for details
* Added a D3D12 renderer implementation and SDL_RenderGetD3D12Device() to retrieve the D3D12 device associated with it
* Added the hint SDL_HINT_WINDOWS_DPI_AWARENESS to set whether the application is DPI-aware. This hint must be set before initializing the video subsystem
* Added the hint SDL_HINT_WINDOWS_DPI_SCALING to control whether the SDL coordinates are in DPI-scaled points or pixels
* Added the hint SDL_HINT_DIRECTINPUT_ENABLED to control whether the DirectInput driver should be used
* Added support for SDL_GetAudioDeviceSpec to the DirectSound backend

Linux:
* Support for XVidMode has been removed, mode changes are only supported using the XRandR extension
* Added the hint SDL_HINT_VIDEO_WAYLAND_MODE_EMULATION to control whether to expose a set of emulated modes in addition to the native resolution modes available on Wayland
* Added the hint SDL_HINT_KMSDRM_DEVICE_INDEX to specify which KMSDRM device to use if the default is not desired
* Added the hint SDL_HINT_LINUX_DIGITAL_HATS to control whether to treat hats as digital rather than checking to see if they may be analog
* Added the hint SDL_HINT_LINUX_HAT_DEADZONES to control whether to use deadzones on analog hats

macOS:
* Bumped minimum OS deployment version to macOS 10.9
* Added SDL_GL_FLOATBUFFERS to allow Cocoa GL contexts to use EDR
* Added the hint SDL_HINT_MAC_OPENGL_ASYNC_DISPATCH to control whether dispatching OpenGL context updates should block the dispatching thread until the main thread finishes processing. This hint defaults to blocking, which is the safer option on modern macOS.


---------------------------------------------------------------------------
2.0.22:
---------------------------------------------------------------------------

General:
* Added SDL_RenderGetWindow() to get the window associated with a renderer
* Added floating point rectangle functions:
    * SDL_PointInFRect()
    * SDL_FRectEmpty()
    * SDL_FRectEquals()
    * SDL_FRectEqualsEpsilon()
    * SDL_HasIntersectionF()
    * SDL_IntersectFRect()
    * SDL_UnionFRect()
    * SDL_EncloseFPoints()
    * SDL_IntersectFRectAndLine()
* Added SDL_IsTextInputShown() which returns whether the IME window is currently shown
* Added SDL_ClearComposition() to dismiss the composition window without disabling IME input
* Added SDL_TEXTEDITING_EXT event for handling long composition text, and a hint SDL_HINT_IME_SUPPORT_EXTENDED_TEXT to enable it
* Added the hint SDL_HINT_MOUSE_RELATIVE_MODE_CENTER to control whether the mouse should be constrained to the whole window or the center of the window when relative mode is enabled
* The mouse is now automatically captured when mouse buttons are pressed, and the hint SDL_HINT_MOUSE_AUTO_CAPTURE allows you to control this behavior
* Added the hint SDL_HINT_VIDEO_FOREIGN_WINDOW_OPENGL to let SDL know that a foreign window will be used with OpenGL
* Added the hint SDL_HINT_VIDEO_FOREIGN_WINDOW_VULKAN to let SDL know that a foreign window will be used with Vulkan
* Added the hint SDL_HINT_QUIT_ON_LAST_WINDOW_CLOSE to specify whether an SDL_QUIT event will be delivered when the last application window is closed
* Added the hint SDL_HINT_JOYSTICK_ROG_CHAKRAM to control whether ROG Chakram mice show up as joysticks

Windows:
* Added support for SDL_BLENDOPERATION_MINIMUM and SDL_BLENDOPERATION_MAXIMUM to the D3D9 renderer

Linux:
* Compiling with Wayland support requires libwayland-client version 1.18.0 or later
* Added the hint SDL_HINT_X11_WINDOW_TYPE to specify the _NET_WM_WINDOW_TYPE of SDL windows
* Added the hint SDL_HINT_VIDEO_WAYLAND_PREFER_LIBDECOR to allow using libdecor with compositors that support xdg-decoration

Android:
* Added SDL_AndroidSendMessage() to send a custom command to the SDL java activity


---------------------------------------------------------------------------
2.0.20:
---------------------------------------------------------------------------

General:
* SDL_RenderGeometryRaw() takes a pointer to SDL_Color, not int. You can cast color data in SDL_PIXELFORMAT_RGBA32 format (SDL_PIXELFORMAT_ABGR8888 on little endian systems) for this parameter.
* Improved accuracy of horizontal and vertical line drawing when using OpenGL or OpenGLES
* Added the hint SDL_HINT_RENDER_LINE_METHOD to control the method of line drawing used, to select speed, correctness, and compatibility.

Windows:
* Fixed size of custom cursors

Linux:
* Fixed hotplug controller detection, broken in 2.0.18


---------------------------------------------------------------------------
2.0.18:
---------------------------------------------------------------------------

General:
* The SDL wiki documentation and development headers are automatically kept in sync
* Each function has information about in which version of SDL it was introduced
* SDL-specific CMake options are now prefixed with 'SDL_'. Be sure to update your CMake build scripts accordingly!
* Added the hint SDL_HINT_APP_NAME to let SDL know the name of your application for various places it might show up in system information
* Added SDL_RenderGeometry() and SDL_RenderGeometryRaw() to allow rendering of arbitrary shapes using the SDL 2D render API
* Added SDL_SetTextureUserData() and SDL_GetTextureUserData() to associate application data with an SDL texture
* Added SDL_RenderWindowToLogical() and SDL_RenderLogicalToWindow() to convert between window coordinates and logical render coordinates
* Added SDL_RenderSetVSync() to change whether a renderer present is synchronized with vblank at runtime
* Added SDL_PremultiplyAlpha() to premultiply alpha on a block of SDL_PIXELFORMAT_ARGB8888 pixels
* Added a window event SDL_WINDOWEVENT_DISPLAY_CHANGED which is sent when a window changes what display it's centered on
* Added SDL_GetWindowICCProfile() to query a window's ICC profile, and a window event SDL_WINDOWEVENT_ICCPROF_CHANGED that is sent when it changes
* Added the hint SDL_HINT_VIDEO_EGL_ALLOW_TRANSPARENCY to allow EGL windows to be transparent instead of opaque
* SDL_WaitEvent() has been redesigned to use less CPU in most cases
* Added SDL_SetWindowMouseRect() and SDL_GetWindowMouseRect() to confine the mouse cursor to an area of a window
* You can now read precise mouse wheel motion using 'preciseX' and 'preciseY' event fields
* Added SDL_GameControllerHasRumble() and SDL_GameControllerHasRumbleTriggers() to query whether a game controller supports rumble
* Added SDL_JoystickHasRumble() and SDL_JoystickHasRumbleTriggers() to query whether a joystick supports rumble
* SDL's hidapi implementation is now available as a public API in SDL_hidapi.h

Windows:
* Improved relative mouse motion over Windows Remote Desktop
* Added the hint SDL_HINT_IME_SHOW_UI to show native UI components instead of hiding them (defaults off)

Windows/UWP:
* WGI is used instead of XInput for better controller support in UWP apps

Linux:
* Added the hint SDL_HINT_SCREENSAVER_INHIBIT_ACTIVITY_NAME to set the activity that's displayed by the system when the screensaver is disabled
* Added the hint SDL_HINT_LINUX_JOYSTICK_CLASSIC to control whether /dev/input/js* or /dev/input/event* are used as joystick devices
* Added the hint SDL_HINT_JOYSTICK_DEVICE to allow the user to specify devices that will be opened in addition to the normal joystick detection
* Added SDL_LinuxSetThreadPriorityAndPolicy() for more control over a thread priority on Linux

Android:
* Added support for audio output and capture using AAudio on Android 8.1 and newer
* Steam Controller support is disabled by default, and can be enabled by setting the hint SDL_HINT_JOYSTICK_HIDAPI_STEAM to "1" before calling SDL_Init()

Apple Arcade:
* Added SDL_GameControllerGetAppleSFSymbolsNameForButton() and SDL_GameControllerGetAppleSFSymbolsNameForAxis() to support Apple Arcade titles

iOS:
* Added documentation that the UIApplicationSupportsIndirectInputEvents key must be set to true in your application's Info.plist in order to get real Bluetooth mouse events.
* Steam Controller support is disabled by default, and can be enabled by setting the hint SDL_HINT_JOYSTICK_HIDAPI_STEAM to "1" before calling SDL_Init()


---------------------------------------------------------------------------
2.0.16:
---------------------------------------------------------------------------
General:
* Added SDL_FlashWindow() to get a user's attention
* Added SDL_GetAudioDeviceSpec() to get the preferred audio format of a device
* Added SDL_SetWindowAlwaysOnTop() to dynamically change the SDL_WINDOW_ALWAYS_ON_TOP flag for a window
* Added SDL_SetWindowKeyboardGrab() to support grabbing the keyboard independently of the mouse
* Added SDL_SoftStretchLinear() to do bilinear scaling between 32-bit software surfaces
* Added SDL_UpdateNVTexture() to update streaming NV12/21 textures
* Added SDL_GameControllerSendEffect() and SDL_JoystickSendEffect() to allow sending custom trigger effects to the DualSense controller
* Added SDL_GameControllerGetSensorDataRate() to get the sensor data rate for PlayStation and Nintendo Switch controllers
* Added support for the Amazon Luna game controller
* Added rumble support for the Google Stadia controller using the HIDAPI driver
* Added SDL_GameControllerType constants for the Amazon Luna and Google Stadia controllers
* Added analog rumble for Nintendo Switch Pro controllers using the HIDAPI driver
* Reduced CPU usage when using SDL_WaitEvent() and SDL_WaitEventTimeout()

Windows:
* Added SDL_SetWindowsMessageHook() to set a function that is called for all Windows messages
* Added SDL_RenderGetD3D11Device() to get the D3D11 device used by the SDL renderer

Linux:
* Greatly improved Wayland support
* Added support for audio output and capture using Pipewire
* Added the hint SDL_HINT_AUDIO_INCLUDE_MONITORS to control whether PulseAudio recording should include monitor devices
* Added the hint SDL_HINT_AUDIO_DEVICE_STREAM_ROLE to describe the role of your application for audio control panels

Android:
* Added SDL_AndroidShowToast() to show a lightweight notification

iOS:
* Added support for mouse relative mode on iOS 14.1 and newer
* Added support for the Xbox Series X controller

tvOS:
* Added support for the Xbox Series X controller


---------------------------------------------------------------------------
2.0.14:
---------------------------------------------------------------------------
General:
* Added support for PS5 DualSense and Xbox Series X controllers to the HIDAPI controller driver
* Added game controller button constants for paddles and new buttons
* Added game controller functions to get additional information:
	* SDL_GameControllerGetSerial()
	* SDL_GameControllerHasAxis()
	* SDL_GameControllerHasButton()
	* SDL_GameControllerGetNumTouchpads()
	* SDL_GameControllerGetNumTouchpadFingers()
	* SDL_GameControllerGetTouchpadFinger()
	* SDL_GameControllerHasSensor()
	* SDL_GameControllerSetSensorEnabled()
	* SDL_GameControllerIsSensorEnabled()
	* SDL_GameControllerGetSensorData()
	* SDL_GameControllerRumbleTriggers()
	* SDL_GameControllerHasLED()
	* SDL_GameControllerSetLED()
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_PS5 to control whether the HIDAPI driver for PS5 controllers should be used.
* Added joystick functions to get additional information:
	* SDL_JoystickGetSerial()
	* SDL_JoystickRumbleTriggers()
	* SDL_JoystickHasLED()
	* SDL_JoystickSetLED()
* Added an API to allow the application to create virtual joysticks:
	* SDL_JoystickAttachVirtual()
	* SDL_JoystickDetachVirtual()
	* SDL_JoystickIsVirtual()
	* SDL_JoystickSetVirtualAxis()
	* SDL_JoystickSetVirtualButton()
	* SDL_JoystickSetVirtualHat()
* Added SDL_LockSensors() and SDL_UnlockSensors() to guarantee exclusive access to the sensor list
* Added SDL_HAPTIC_STEERING_AXIS to play an effect on the steering wheel
* Added the hint SDL_HINT_MOUSE_RELATIVE_SCALING to control whether relative motion is scaled by the screen DPI or renderer logical size
* The default value for SDL_HINT_VIDEO_MINIMIZE_ON_FOCUS_LOSS is now false for better compatibility with modern window managers
* Added SDL_GetPreferredLocales() to get the application's current locale setting
* Added the hint SDL_HINT_PREFERRED_LOCALES to override your application's default locale setting
* Added SDL_OpenURL() to open a URL in the system's default browser
* Added SDL_HasSurfaceRLE() to tell whether a surface is currently using RLE encoding
* Added SDL_SIMDRealloc() to reallocate memory obtained from SDL_SIMDAlloc()
* Added SDL_GetErrorMsg() to get the last error in a thread-safe way
* Added SDL_crc32(), SDL_wcscasecmp(), SDL_wcsncasecmp(), SDL_trunc(), SDL_truncf()
* Added clearer names for RGB pixel formats, e.g. SDL_PIXELFORMAT_XRGB8888, SDL_PIXELFORMAT_XBGR8888, etc.

Windows:
* Added the RAWINPUT controller driver to support more than 4 Xbox controllers simultaneously
* Added the hint SDL_HINT_JOYSTICK_RAWINPUT to control whether the RAWINPUT driver should be used
* Added the hint SDL_HINT_JOYSTICK_HIDAPI_CORRELATE_XINPUT to control whether XInput and WGI should be used to for complete controller functionality with the RAWINPUT driver.

macOS:
* Added the SDL_WINDOW_METAL flag to specify that a window should be created with a Metal view
* Added SDL_Metal_GetLayer() to get the CAMetalLayer backing a Metal view
* Added SDL_Metal_GetDrawableSize() to get the size of a window's drawable, in pixels

Linux:
* Added the hint SDL_HINT_AUDIO_DEVICE_APP_NAME to specify the name that shows up in PulseAudio for your application
* Added the hint SDL_HINT_AUDIO_DEVICE_STREAM_NAME to specify the name that shows up in PulseAudio associated with your audio stream
* Added the hint SDL_HINT_LINUX_JOYSTICK_DEADZONES to control whether HID defined dead zones should be respected on Linux
* Added the hint SDL_HINT_THREAD_PRIORITY_POLICY to specify the thread scheduler policy
* Added the hint SDL_HINT_THREAD_FORCE_REALTIME_TIME_CRITICAL to allow time critical threads to use a realtime scheduling policy

Android:
* Added SDL_AndroidRequestPermission() to request a specific system permission
* Added the hint SDL_HINT_ANDROID_BLOCK_ON_PAUSE_PAUSEAUDIO to control whether audio will pause when the application goes intot he background

OS/2:
* Added support for OS/2, see docs/README-os2.md for details

Emscripten (running in a web browser):
* Added the hint SDL_HINT_EMSCRIPTEN_ASYNCIFY to control whether SDL should call emscripten_sleep internally


---------------------------------------------------------------------------
2.0.12:
---------------------------------------------------------------------------

General:
* Added SDL_GetTextureScaleMode() and SDL_SetTextureScaleMode() to get and set the scaling mode used for a texture
* Added SDL_LockTextureToSurface(), similar to SDL_LockTexture() but the locked area is exposed as a SDL surface.
* Added new blend mode, SDL_BLENDMODE_MUL, which does a modulate and blend operation
* Added the hint SDL_HINT_DISPLAY_USABLE_BOUNDS to override the results of SDL_GetDisplayUsableBounds() for display index 0.
* Added the window underneath the finger to the SDL_TouchFingerEvent
* Added SDL_GameControllerTypeForIndex(), SDL_GameControllerGetType() to return the type of a game controller (Xbox 360, Xbox One, PS3, PS4, or Nintendo Switch Pro)
* Added the hint SDL_HINT_GAMECONTROLLERTYPE to override the automatic game controller type detection
* Added SDL_JoystickFromPlayerIndex() and SDL_GameControllerFromPlayerIndex() to get the device associated with a player index
* Added SDL_JoystickSetPlayerIndex() and SDL_GameControllerSetPlayerIndex() to set the player index associated with a device
* Added the hint SDL_HINT_GAMECONTROLLER_USE_BUTTON_LABELS to specify whether Nintendo Switch Pro controllers should use the buttons as labeled or swapped to match positional layout. The default is to use the buttons as labeled.
* Added support for Nintendo GameCube controllers to the HIDAPI driver, and a hint SDL_HINT_JOYSTICK_HIDAPI_GAMECUBE to control whether this is used.
* Improved support for Xbox 360 and Xbox One controllers when using the HIDAPI driver
* Added support for many game controllers, including:
	* 8BitDo FC30 Pro
	* 8BitDo M30 GamePad
	* BDA PS4 Fightpad
	* HORI Fighting Commander
	* Hyperkin Duke
	* Hyperkin X91
	* MOGA XP5-A Plus
	* NACON GC-400ES
	* NVIDIA Controller v01.04
	* PDP Versus Fighting Pad
	* Razer Raion Fightpad for PS4
	* Razer Serval
	* Stadia Controller
	* SteelSeries Stratus Duo
	* Victrix Pro Fight Stick for PS4
	* Xbox One Elite Series 2
* Fixed blocking game controller rumble calls when using the HIDAPI driver
* Added SDL_zeroa() macro to zero an array of elements
* Added SDL_HasARMSIMD() which returns true if the CPU has ARM SIMD (ARMv6+) features

Windows:
* Fixed crash when using the release SDL DLL with applications built with gcc
* Fixed performance regression in event handling introduced in 2.0.10
* Added support for SDL_SetThreadPriority() for UWP applications

Linux:
* Added the hint SDL_HINT_VIDEO_X11_WINDOW_VISUALID to specify the visual chosen for new X11 windows
* Added the hint SDL_HINT_VIDEO_X11_FORCE_EGL to specify whether X11 should use GLX or EGL by default

iOS / tvOS / macOS:
* Added SDL_Metal_CreateView() and SDL_Metal_DestroyView() to create CAMetalLayer-backed NSView/UIView and attach it to the specified window.

iOS/ tvOS:
* Added support for Bluetooth Steam Controllers as game controllers

tvOS:
* Fixed support for surround sound on Apple TV

Android:
* Added SDL_GetAndroidSDKVersion() to return the API level of the current device
* Added support for audio capture using OpenSL-ES
* Added support for Bluetooth Steam Controllers as game controllers
* Fixed rare crashes when the app goes into the background or terminates


---------------------------------------------------------------------------
2.0.10:
---------------------------------------------------------------------------

General:
* The SDL_RW* macros have been turned into functions that are available only in 2.0.10 and onward
* Added SDL_SIMDGetAlignment(), SDL_SIMDAlloc(), and SDL_SIMDFree(), to allocate memory aligned for SIMD operations for the current CPU
* Added SDL_RenderDrawPointF(), SDL_RenderDrawPointsF(), SDL_RenderDrawLineF(), SDL_RenderDrawLinesF(), SDL_RenderDrawRectF(), SDL_RenderDrawRectsF(), SDL_RenderFillRectF(), SDL_RenderFillRectsF(), SDL_RenderCopyF(), SDL_RenderCopyExF(), to allow floating point precision in the SDL rendering API.
* Added SDL_GetTouchDeviceType() to get the type of a touch device, which can be a touch screen or a trackpad in relative or absolute coordinate mode.
* The SDL rendering API now uses batched rendering by default, for improved performance
* Added SDL_RenderFlush() to force batched render commands to execute, if you're going to mix SDL rendering with native rendering
* Added the hint SDL_HINT_RENDER_BATCHING to control whether batching should be used for the rendering API. This defaults to "1" if you don't specify what rendering driver to use when creating the renderer.
* Added the hint SDL_HINT_EVENT_LOGGING to enable logging of SDL events for debugging purposes
* Added the hint SDL_HINT_GAMECONTROLLERCONFIG_FILE to specify a file that will be loaded at joystick initialization with game controller bindings
* Added the hint SDL_HINT_MOUSE_TOUCH_EVENTS to control whether SDL will synthesize touch events from mouse events
* Improved handling of malformed WAVE and BMP files, fixing potential security exploits

Linux:
* Removed the Mir video driver in favor of Wayland

iOS / tvOS:
* Added support for Xbox and PS4 wireless controllers in iOS 13 and tvOS 13
* Added support for text input using Bluetooth keyboards

Android:
* Added low latency audio using OpenSL ES
* Removed SDL_HINT_ANDROID_SEPARATE_MOUSE_AND_TOUCH (replaced by SDL_HINT_MOUSE_TOUCH_EVENTS and SDL_HINT_TOUCH_MOUSE_EVENTS)
  SDL_HINT_ANDROID_SEPARATE_MOUSE_AND_TOUCH=1, should be replaced by setting both previous hints to 0.
  SDL_HINT_ANDROID_SEPARATE_MOUSE_AND_TOUCH=0, should be replaced by setting both previous hints to 1.
* Added the hint SDL_HINT_ANDROID_BLOCK_ON_PAUSE to set whether the event loop will block itself when the app is paused.


---------------------------------------------------------------------------
2.0.9:
---------------------------------------------------------------------------

General:
* Added a new sensor API, initialized by passing SDL_INIT_SENSOR to SDL_Init(), and defined in SDL_sensor.h
* Added an event SDL_SENSORUPDATE which is sent when a sensor is updated
* Added SDL_GetDisplayOrientation() to return the current display orientation
* Added an event SDL_DISPLAYEVENT which is sent when the display orientation changes
* Added HIDAPI joystick drivers for more consistent support for Xbox, PS4 and Nintendo Switch Pro controller support across platforms. (Thanks to Valve for contributing the PS4 and Nintendo Switch Pro controller support)
* Added support for many other popular game controllers
* Added SDL_JoystickGetDevicePlayerIndex(), SDL_JoystickGetPlayerIndex(), and SDL_GameControllerGetPlayerIndex() to get the player index for a controller. For XInput controllers this returns the XInput index for the controller.
* Added SDL_GameControllerRumble() and SDL_JoystickRumble() which allow simple rumble without using the haptics API
* Added SDL_GameControllerMappingForDeviceIndex() to get the mapping for a controller before it's opened
* Added the hint SDL_HINT_MOUSE_DOUBLE_CLICK_TIME to control the mouse double-click time
* Added the hint SDL_HINT_MOUSE_DOUBLE_CLICK_RADIUS to control the mouse double-click radius, in pixels
* Added SDL_HasColorKey() to return whether a surface has a colorkey active
* Added SDL_HasAVX512F() to return whether the CPU has AVX-512F features
* Added SDL_IsTablet() to return whether the application is running on a tablet
* Added SDL_THREAD_PRIORITY_TIME_CRITICAL for threads that must run at the highest priority

Mac OS X:
* Fixed black screen at start on Mac OS X Mojave

Linux:
* Added SDL_LinuxSetThreadPriority() to allow adjusting the thread priority of native threads using RealtimeKit if available.

iOS:
* Fixed Asian IME input

Android:
* Updated required Android SDK to API 26, to match Google's new App Store requirements
* Added support for wired USB Xbox, PS4, and Nintendo Switch Pro controllers
* Added support for relative mouse mode on Android 7.0 and newer (except where it's broken, on Chromebooks and when in DeX mode with Samsung Experience 9.0)
* Added support for custom mouse cursors on Android 7.0 and newer
* Added the hint SDL_HINT_ANDROID_TRAP_BACK_BUTTON to control whether the back button will back out of the app (the default) or be passed to the application as SDL_SCANCODE_AC_BACK
* Added SDL_AndroidBackButton() to trigger the Android system back button behavior when handling the back button in the application
* Added SDL_IsChromebook() to return whether the app is running in the Chromebook Android runtime
* Added SDL_IsDeXMode() to return whether the app is running while docked in the Samsung DeX


---------------------------------------------------------------------------
2.0.8:
---------------------------------------------------------------------------

General:
* Added SDL_fmod() and SDL_log10()
* Each of the SDL math functions now has the corresponding float version
* Added SDL_SetYUVConversionMode() and SDL_GetYUVConversionMode() to control the formula used when converting to and from YUV colorspace. The options are JPEG, BT.601, and BT.709

Windows:
* Implemented WASAPI support on Windows UWP and removed the deprecated XAudio2 implementation
* Added resampling support on WASAPI on Windows 7 and above

Windows UWP:
* Added SDL_WinRTGetDeviceFamily() to find out what type of device your application is running on

Mac OS X:
* Added support for the Vulkan SDK for Mac:
  https://www.lunarg.com/lunarg-releases-vulkan-sdk-1-0-69-0-for-mac/
* Added support for OpenGL ES using ANGLE when it's available

Mac OS X / iOS / tvOS:
* Added a Metal 2D render implementation
* Added SDL_RenderGetMetalLayer() and SDL_RenderGetMetalCommandEncoder() to insert your own drawing into SDL rendering when using the Metal implementation

iOS:
* Added the hint SDL_HINT_IOS_HIDE_HOME_INDICATOR to control whether the home indicator bar on iPhone X should be hidden. This defaults to dimming the indicator for fullscreen applications and showing the indicator for windowed applications.

iOS / Android:
* Added the hint SDL_HINT_RETURN_KEY_HIDES_IME to control whether the return key on the software keyboard should hide the keyboard or send a key event (the default)

Android:
* SDL now supports building with Android Studio and Gradle by default, and the old Ant project is available in android-project-ant
* SDL now requires the API 19 SDK to build, but can still target devices down to API 14 (Android 4.0.1)
* Added SDL_IsAndroidTV() to tell whether the application is running on Android TV

Android / tvOS:
* Added the hint SDL_HINT_TV_REMOTE_AS_JOYSTICK to control whether TV remotes should be listed as joystick devices (the default) or send keyboard events.

Linux:
* Added the hint SDL_HINT_VIDEO_X11_NET_WM_BYPASS_COMPOSITOR to control whether the X server should skip the compositor for the SDL application. This defaults to "1"
* Added the hint SDL_HINT_VIDEO_DOUBLE_BUFFER to control whether the Raspberry Pi and KMSDRM video drivers should use double or triple buffering (the default)


---------------------------------------------------------------------------
2.0.7:
---------------------------------------------------------------------------

General:
* Added audio stream conversion functions:
	SDL_NewAudioStream
	SDL_AudioStreamPut
	SDL_AudioStreamGet
	SDL_AudioStreamAvailable
	SDL_AudioStreamFlush
	SDL_AudioStreamClear
	SDL_FreeAudioStream
* Added functions to query and set the SDL memory allocation functions:
	SDL_GetMemoryFunctions()
	SDL_SetMemoryFunctions()
	SDL_GetNumAllocations()
* Added locking functions for multi-threaded access to the joystick and game controller APIs:
	SDL_LockJoysticks()
	SDL_UnlockJoysticks()
* The following functions are now thread-safe:
	SDL_SetEventFilter()
	SDL_GetEventFilter()
	SDL_AddEventWatch()
	SDL_DelEventWatch()


General:
---------------------------------------------------------------------------
2.0.6:
---------------------------------------------------------------------------

General:
* Added cross-platform Vulkan graphics support in SDL_vulkan.h
	SDL_Vulkan_LoadLibrary()
	SDL_Vulkan_GetVkGetInstanceProcAddr()
	SDL_Vulkan_GetInstanceExtensions()
	SDL_Vulkan_CreateSurface()
	SDL_Vulkan_GetDrawableSize()
	SDL_Vulkan_UnloadLibrary()
  This is all the platform-specific code you need to bring up Vulkan on all SDL platforms. You can look at an example in test/testvulkan.c
* Added SDL_ComposeCustomBlendMode() to create custom blend modes for 2D rendering
* Added SDL_HasNEON() which returns whether the CPU has NEON instruction support
* Added support for many game controllers, including the Nintendo Switch Pro Controller
* Added support for inverted axes and separate axis directions in game controller mappings
* Added functions to return information about a joystick before it's opened:
	SDL_JoystickGetDeviceVendor()
	SDL_JoystickGetDeviceProduct()
	SDL_JoystickGetDeviceProductVersion()
	SDL_JoystickGetDeviceType()
	SDL_JoystickGetDeviceInstanceID()
* Added functions to return information about an open joystick:
	SDL_JoystickGetVendor()
	SDL_JoystickGetProduct()
	SDL_JoystickGetProductVersion()
	SDL_JoystickGetType()
	SDL_JoystickGetAxisInitialState()
* Added functions to return information about an open game controller:
	SDL_GameControllerGetVendor()
	SDL_GameControllerGetProduct()
	SDL_GameControllerGetProductVersion()
* Added SDL_GameControllerNumMappings() and SDL_GameControllerMappingForIndex() to be able to enumerate the built-in game controller mappings
* Added SDL_LoadFile() and SDL_LoadFile_RW() to load a file into memory
* Added SDL_DuplicateSurface() to make a copy of a surface
* Added an experimental JACK audio driver
* Implemented non-power-of-two audio resampling, optionally using libsamplerate to perform the resampling
* Added the hint SDL_HINT_AUDIO_RESAMPLING_MODE to control the quality of resampling
* Added the hint SDL_HINT_RENDER_LOGICAL_SIZE_MODE to control the scaling policy for SDL_RenderSetLogicalSize():
	"0" or "letterbox" - Uses letterbox/sidebars to fit the entire rendering on screen (the default)
	"1" or "overscan"  - Will zoom the rendering so it fills the entire screen, allowing edges to be drawn offscreen
* Added the hints SDL_HINT_MOUSE_NORMAL_SPEED_SCALE and SDL_HINT_MOUSE_RELATIVE_SPEED_SCALE to scale the mouse speed when being read from raw mouse input
* Added the hint SDL_HINT_TOUCH_MOUSE_EVENTS to control whether SDL will synthesize mouse events from touch events

Windows:
* The new default audio driver on Windows is WASAPI and supports hot-plugging devices and changing the default audio device
* The old XAudio2 audio driver is deprecated and will be removed in the next release
* Added hints SDL_HINT_WINDOWS_INTRESOURCE_ICON and SDL_HINT_WINDOWS_INTRESOURCE_ICON_SMALL to specify a custom icon resource ID for SDL windows
* The hint SDL_HINT_WINDOWS_DISABLE_THREAD_NAMING is now on by default for compatibility with .NET languages and various Windows debuggers
* Updated the GUID format for game controller mappings, older mappings will be automatically converted on load
* Implemented the SDL_WINDOW_ALWAYS_ON_TOP flag on Windows

Linux:
* Added an experimental KMS/DRM video driver for embedded development

iOS:
* Added a hint SDL_HINT_AUDIO_CATEGORY to control the audio category, determining whether the phone mute switch affects the audio

---------------------------------------------------------------------------
2.0.5:
---------------------------------------------------------------------------

General:
* Implemented audio capture support for some platforms
* Added SDL_DequeueAudio() to retrieve audio when buffer queuing is turned on for audio capture
* Added events for dragging and dropping text
* Added events for dragging and dropping multiple items
* By default the click raising a window will not be delivered to the SDL application. You can set the hint SDL_HINT_MOUSE_FOCUS_CLICKTHROUGH to "1" to allow that click through to the window.
* Saving a surface with an alpha channel as a BMP will use a newer BMP format that supports alpha information. You can set the hint SDL_HINT_BMP_SAVE_LEGACY_FORMAT to "1" to use the old format.
* Added SDL_GetHintBoolean() to get the boolean value of a hint
* Added SDL_RenderSetIntegerScale() to set whether to smoothly scale or use integral multiples of the viewport size when scaling the rendering output
* Added SDL_CreateRGBSurfaceWithFormat() and SDL_CreateRGBSurfaceWithFormatFrom() to create an SDL surface with a specific pixel format
* Added SDL_GetDisplayUsableBounds() which returns the area usable for windows. For example, on Mac OS X, this subtracts the area occupied by the menu bar and dock.
* Added SDL_GetWindowBordersSize() which returns the size of the window's borders around the client area
* Added a window event SDL_WINDOWEVENT_HIT_TEST when a window had a hit test that wasn't SDL_HITTEST_NORMAL (e.g. in the title bar or window frame)
* Added SDL_SetWindowResizable() to change whether a window is resizable
* Added SDL_SetWindowOpacity() and SDL_GetWindowOpacity() to affect the window transparency
* Added SDL_SetWindowModalFor() to set a window as modal for another window
* Added support for AUDIO_U16LSB and AUDIO_U16MSB to SDL_MixAudioFormat()
* Fixed flipped images when reading back from target textures when using the OpenGL renderer
* Fixed texture color modulation with SDL_BLENDMODE_NONE when using the OpenGL renderer
* Fixed bug where the alpha value of colorkeys was ignored when blitting in some cases

Windows:
* Added a hint SDL_HINT_WINDOWS_DISABLE_THREAD_NAMING to prevent SDL from raising a debugger exception to name threads. This exception can cause problems with .NET applications when running under a debugger.
* The hint SDL_HINT_THREAD_STACK_SIZE is now supported on Windows
* Fixed XBox controller triggers automatically being pulled at startup
* The first icon from the executable is used as the default window icon at runtime
* Fixed SDL log messages being printed twice if SDL was built with C library support
* Reset dead keys when the SDL window loses focus, so dead keys pressed in SDL applications don't affect text input into other applications.

Mac OS X:
* Fixed selecting the dummy video driver
* The caps lock key now generates a pressed event when pressed and a released event when released, instead of a press/release event pair when pressed.
* Fixed mouse wheel events on Mac OS X 10.12
* The audio driver has been updated to use AVFoundation for better compatibility with newer versions of Mac OS X

Linux:
* Added support for the Fcitx IME
* Added a window event SDL_WINDOWEVENT_TAKE_FOCUS when a window manager asks the SDL window whether it wants to take focus.
* Refresh rates are now rounded instead of truncated, e.g. 59.94 Hz is rounded up to 60 Hz instead of 59.
* Added initial support for touchscreens on Raspberry Pi

OpenBSD:
* SDL_GetBasePath() is now implemented on OpenBSD

iOS:
* Added support for dynamically loaded objects on iOS 8 and newer

tvOS:
* Added support for Apple TV
* Added a hint SDL_HINT_APPLE_TV_REMOTE_ALLOW_ROTATION to control whether he Apple TV remote's joystick axes will automatically match the rotation of the remote.

Android:
* Fixed SDL not resizing window when Android screen resolution changes
* Corrected the joystick Z axis reporting for the accelerometer

Emscripten (running in a web browser):
* Many bug fixes and improvements


---------------------------------------------------------------------------
2.0.4:
---------------------------------------------------------------------------

General:
* Added support for web applications using Emscripten, see docs/README-emscripten.md for more information
* Added support for web applications using Native Client (NaCl), see docs/README-nacl.md for more information
* Added an API to queue audio instead of using the audio callback:
    SDL_QueueAudio(), SDL_GetQueuedAudioSize(), SDL_ClearQueuedAudio()
* Added events for audio device hot plug support:
    SDL_AUDIODEVICEADDED, SDL_AUDIODEVICEREMOVED
* Added SDL_PointInRect()
* Added SDL_HasAVX2() to detect CPUs with AVX2 support
* Added SDL_SetWindowHitTest() to let apps treat parts of their SDL window like traditional window decorations (drag areas, resize areas)
* Added SDL_GetGrabbedWindow() to get the window that currently has input grab, if any
* Added SDL_RenderIsClipEnabled() to tell whether clipping is currently enabled in a renderer
* Added SDL_CaptureMouse() to capture the mouse to get events while the mouse is not in your window
* Added SDL_WarpMouseGlobal() to warp the mouse cursor in global screen space
* Added SDL_GetGlobalMouseState() to get the current mouse state outside of an SDL window
* Added a direction field to mouse wheel events to tell whether they are flipped (natural) or not
* Added GL_CONTEXT_RELEASE_BEHAVIOR GL attribute (maps to [WGL|GLX]_ARB_context_flush_control extension)
* Added EGL_KHR_create_context support to allow OpenGL ES version selection on some platforms
* Added NV12 and NV21 YUV texture support for OpenGL and OpenGL ES 2.0 renderers
* Added a Vivante video driver that is used on various SoC platforms
* Added an event SDL_RENDER_DEVICE_RESET that is sent from the D3D renderers when the D3D device is lost, and from Android's event loop when the GLES context had to be recreated
* Added a hint SDL_HINT_NO_SIGNAL_HANDLERS to disable SDL's built in signal handling
* Added a hint SDL_HINT_THREAD_STACK_SIZE to set the stack size of SDL's threads
* Added SDL_sqrtf(), SDL_tan(), and SDL_tanf() to the stdlib routines
* Improved support for WAV and BMP files with unusual chunks in them
* Renamed SDL_assert_data to SDL_AssertData and SDL_assert_state to SDL_AssertState
* Added a hint SDL_HINT_WINDOW_FRAME_USABLE_WHILE_CURSOR_HIDDEN to prevent window interaction while cursor is hidden
* Added SDL_GetDisplayDPI() to get the DPI information for a display
* Added SDL_JoystickCurrentPowerLevel() to get the battery level of a joystick
* Added SDL_JoystickFromInstanceID(), as a helper function, to get the SDL_Joystick* that an event is referring to.
* Added SDL_GameControllerFromInstanceID(), as a helper function, to get the SDL_GameController* that an event is referring to.

Windows:
* Added support for Windows Phone 8.1 and Windows 10/UWP (Universal Windows Platform)
* Timer resolution is now 1 ms by default, adjustable with the SDL_HINT_TIMER_RESOLUTION hint
* SDLmain no longer depends on the C runtime, so you can use the same .lib in both Debug and Release builds
* Added SDL_SetWindowsMessageHook() to set a function to be called for every windows message before TranslateMessage()
* Added a hint SDL_HINT_WINDOWS_ENABLE_MESSAGELOOP to control whether SDL_PumpEvents() processes the Windows message loop
* You can distinguish between real mouse and touch events by looking for SDL_TOUCH_MOUSEID in the mouse event "which" field
* SDL_SysWMinfo now contains the window HDC
* Added support for Unicode command line options
* Prevent beeping when Alt-key combos are pressed
* SDL_SetTextInputRect() re-positions the OS-rendered IME
* Added a hint SDL_HINT_WINDOWS_NO_CLOSE_ON_ALT_F4 to prevent generating SDL_WINDOWEVENT_CLOSE events when Alt-F4 is pressed
* Added a hint SDL_HINT_XINPUT_USE_OLD_JOYSTICK_MAPPING to use the old axis and button mapping for XInput devices (deprecated)

Mac OS X:
* Implemented drag-and-drop support
* Improved joystick hot-plug detection
* The SDL_WINDOWEVENT_EXPOSED window event is triggered in the appropriate situations
* Fixed relative mouse mode when the application loses/regains focus
* Fixed bugs related to transitioning to and from Spaces-aware fullscreen-desktop mode
* Fixed the refresh rate of display modes
* SDL_SysWMInfo is now ARC-compatible
* Added a hint SDL_HINT_MAC_BACKGROUND_APP to prevent forcing the application to become a foreground process

Linux:
* Enabled building with Mir and Wayland support by default.
* Added IBus IME support
* Added a hint SDL_HINT_IME_INTERNAL_EDITING to control whether IBus should handle text editing internally instead of sending SDL_TEXTEDITING events
* Added a hint SDL_HINT_VIDEO_X11_NET_WM_PING to allow disabling _NET_WM_PING protocol handling in SDL_CreateWindow()
* Added support for multiple audio devices when using Pulseaudio
* Fixed duplicate mouse events when using relative mouse motion

iOS:
* Added support for iOS 8
* The SDL_WINDOW_ALLOW_HIGHDPI window flag now enables high-dpi support, and SDL_GL_GetDrawableSize() or SDL_GetRendererOutputSize() gets the window resolution in pixels
* SDL_GetWindowSize() and display mode sizes are in the "DPI-independent points" / "screen coordinates" coordinate space rather than pixels (matches OS X behavior)
* Added native resolution support for the iPhone 6 Plus
* Added support for MFi game controllers
* Added support for the hint SDL_HINT_ACCELEROMETER_AS_JOYSTICK
* Added sRGB OpenGL ES context support on iOS 7+
* Added support for SDL_DisableScreenSaver(), SDL_EnableScreenSaver() and the hint SDL_HINT_VIDEO_ALLOW_SCREENSAVER
* SDL_SysWMinfo now contains the OpenGL ES framebuffer and color renderbuffer objects used by the window's active GLES view
* Fixed various rotation and orientation issues
* Fixed memory leaks

Android:
* Added a hint SDL_HINT_ANDROID_SEPARATE_MOUSE_AND_TOUCH to prevent mouse events from being registered as touch events
* Added hints SDL_HINT_ANDROID_APK_EXPANSION_MAIN_FILE_VERSION and SDL_HINT_ANDROID_APK_EXPANSION_PATCH_FILE_VERSION
* Added support for SDL_DisableScreenSaver(), SDL_EnableScreenSaver() and the hint SDL_HINT_VIDEO_ALLOW_SCREENSAVER
* Added support for SDL_ShowMessageBox() and SDL_ShowSimpleMessageBox()

Raspberry Pi:
* Added support for the Raspberry Pi 2


---------------------------------------------------------------------------
2.0.3:
---------------------------------------------------------------------------

Mac OS X:
* Fixed creating an OpenGL context by default on Mac OS X 10.6


---------------------------------------------------------------------------
2.0.2:
---------------------------------------------------------------------------
General:
* Added SDL_GL_ResetAttributes() to reset OpenGL attributes to default values
* Added an API to load a database of game controller mappings from a file:
    SDL_GameControllerAddMappingsFromFile(), SDL_GameControllerAddMappingsFromRW()
* Added game controller mappings for the PS4 and OUYA controllers
* Added SDL_GetDefaultAssertionHandler() and SDL_GetAssertionHandler()
* Added SDL_DetachThread()
* Added SDL_HasAVX() to determine if the CPU has AVX features
* Added SDL_vsscanf(), SDL_acos(), and SDL_asin() to the stdlib routines
* EGL can now create/manage OpenGL and OpenGL ES 1.x/2.x contexts, and share
  them using SDL_GL_SHARE_WITH_CURRENT_CONTEXT
* Added a field "clicks" to the mouse button event which records whether the event is a single click, double click, etc.
* The screensaver is now disabled by default, and there is a hint SDL_HINT_VIDEO_ALLOW_SCREENSAVER that can change that behavior.
* Added a hint SDL_HINT_MOUSE_RELATIVE_MODE_WARP to specify whether mouse relative mode should be emulated using mouse warping.
* testgl2 does not need to link with libGL anymore
* Added testgles2 test program to demonstrate working with OpenGL ES 2.0
* Added controllermap test program to visually map a game controller

Windows:
* Support for OpenGL ES 2.x contexts using either WGL or EGL (natively via
  the driver or emulated through ANGLE)
* Added a hint SDL_HINT_VIDEO_WIN_D3DCOMPILER to specify which D3D shader compiler to use for OpenGL ES 2 support through ANGLE
* Added a hint SDL_HINT_VIDEO_WINDOW_SHARE_PIXEL_FORMAT that is useful when creating multiple windows that should share the same OpenGL context.
* Added an event SDL_RENDER_TARGETS_RESET that is sent when D3D9 render targets are reset after the device has been restored.

Mac OS X:
* Added a hint SDL_HINT_MAC_CTRL_CLICK_EMULATE_RIGHT_CLICK to control whether Ctrl+click should be treated as a right click on Mac OS X. This is off by default.

Linux:
* Fixed fullscreen and focused behavior when receiving NotifyGrab events
* Added experimental Wayland and Mir support, disabled by default

Android:
* Joystick support (minimum SDK version required to build SDL is now 12,
  the required runtime version remains at 10, but on such devices joystick
  support won't be available).
* Hotplugging support for joysticks
* Added a hint SDL_HINT_ACCELEROMETER_AS_JOYSTICK to control whether the accelerometer should be listed as a 3 axis joystick, which it will by default.


---------------------------------------------------------------------------
2.0.1:
---------------------------------------------------------------------------

General:
* Added an API to get common filesystem paths in SDL_filesystem.h:
    SDL_GetBasePath(), SDL_GetPrefPath()
* Added an API to do optimized YV12 and IYUV texture updates:
    SDL_UpdateYUVTexture()
* Added an API to get the amount of RAM on the system:
    SDL_GetSystemRAM()
* Added a macro to perform timestamp comparisons with SDL_GetTicks():
    SDL_TICKS_PASSED()
* Dramatically improved OpenGL ES 2.0 rendering performance
* Added OpenGL attribute SDL_GL_FRAMEBUFFER_SRGB_CAPABLE

Windows:
* Created a static library configuration for the Visual Studio 2010 project
* Added a hint to create the Direct3D device with support for multi-threading:
    SDL_HINT_RENDER_DIRECT3D_THREADSAFE
* Added a function to get the D3D9 adapter index for a display:
    SDL_Direct3D9GetAdapterIndex()
* Added a function to get the D3D9 device for a D3D9 renderer:
    SDL_RenderGetD3D9Device()
* Fixed building SDL with the mingw32 toolchain (mingw-w64 is preferred)
* Fixed crash when using two XInput controllers at the same time
* Fixed detecting a mixture of XInput and DirectInput controllers
* Fixed clearing a D3D render target larger than the window
* Improved support for format specifiers in SDL_snprintf()

Mac OS X:
* Added support for retina displays:
  Create your window with the SDL_WINDOW_ALLOW_HIGHDPI flag, and then use SDL_GL_GetDrawableSize() to find the actual drawable size. You are responsible for scaling mouse and drawing coordinates appropriately.
* Fixed mouse warping in fullscreen mode
* Right mouse click is emulated by holding the Ctrl key while left clicking

Linux:
* Fixed float audio support with the PulseAudio driver
* Fixed missing line endpoints in the OpenGL renderer on some drivers
* X11 symbols are no longer defined to avoid collisions when linking statically

iOS:
* Fixed status bar visibility on iOS 7
* Flipped the accelerometer Y axis to match expected values

Android:
IMPORTANT: You MUST get the updated SDLActivity.java to match C code
* Moved EGL initialization to native code
* Fixed the accelerometer axis rotation relative to the device rotation
* Fixed race conditions when handling the EGL context on pause/resume
* Touch devices are available for enumeration immediately after init

Raspberry Pi:
* Added support for the Raspberry Pi, see README-raspberrypi.txt for details
