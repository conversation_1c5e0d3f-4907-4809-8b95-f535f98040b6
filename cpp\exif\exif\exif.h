#ifndef EXIF_HEADER_FILE
#define EXIF_HEADER_FILE

#include <libexif/exif-data.h>
#include <stdexcept>
#include <cstdlib>
#include <cstring>

namespace Exif {

namespace {

static const unsigned char exif_header[] = { 0xff, 0xd8, 0xff, 0xe1 };

static inline void *exifAlloc(ExifLong s) {
    return std::calloc(static_cast<size_t>(s), 1);
}

static inline void *exifRealloc(void *p, ExifLong s) {
    return std::realloc(p, static_cast<size_t>(s));
}

static inline void exifFree(void *p) {
    std::free(p);
}

class ExifMemory {
    public:
        ExifMemory()
        {
            exifMem = exif_mem_new(exifAlloc, exifRealloc, exifFree);
            if (!exifMem) {
                throw std::runtime_error("Could not allocate ExifMem");
            }
        }
        ~ExifMemory() {
            exif_mem_unref(exifMem);
        }
        ExifMem *mem() const { return exifMem; }
    private:
        ExifMem *exifMem;
};

class ExifResources {
    public:
        ExifResources(ExifMemory &exifMemory, uint8_t *jpeg, size_t size)
        : exifMemory(exifMemory)
        , exif(exif_data_new_mem(exifMemory.mem()))
        , jpeg(jpeg)
        , size(size)
        {
            if (!exif) {
                throw std::runtime_error("Could not allocate ExifData");
            }
        }
        ~ExifResources() {
            exif_data_unref(exif);
        }
        ExifEntry *initTag(ExifIfd ifd, ExifTag tag) {
            if (exif_content_get_entry(exif->ifd[ifd], tag)) {
                throw std::runtime_error("Exif entry already exists");
            }
            ExifEntry *entry = exif_entry_new_mem(exifMemory.mem());
            if (!entry) {
                throw std::runtime_error("Could not allocate ExifEntry");
            }
            entry->tag = tag;
            exif_content_add_entry(exif->ifd[ifd], entry);
            exif_entry_initialize(entry, tag);
            exif_entry_unref(entry);
            return entry;
        }
        ExifEntry *createTag(ExifIfd ifd, ExifTag tag, size_t len, const ExifFormat &format = EXIF_FORMAT_UNDEFINED) {
            if (exif_content_get_entry(exif->ifd[ifd], tag)) {
                throw std::runtime_error("ExifEntry already exists");
            }
            ExifEntry *entry = exif_entry_new_mem(exifMemory.mem());
            if (!entry) {
                throw std::runtime_error("Could not allocate ExifEntry");
            }
            void *buf = exif_mem_alloc(exifMemory.mem(), static_cast<ExifLong>(len));
            if (!buf) {
                exif_entry_unref(entry);
                throw std::runtime_error("Could not allocate memory for ExifEntry data");
            }
            entry->data = (unsigned char *)buf;
            entry->size = static_cast<unsigned int>(len);
            entry->tag = tag;
            entry->components = static_cast<unsigned long>(len);
            entry->format = format;
            exif_content_add_entry(exif->ifd[ifd], entry);
            exif_entry_unref(entry);
            return entry;
        }
        struct CloseWriteFileResources {
            ExifMemory &exifMemory;
            unsigned char *exif_data;
            unsigned int exif_data_len;
            FILE *f;
            CloseWriteFileResources(ExifMemory &exifMemory)
            : exifMemory(exifMemory)
            , exif_data(NULL)
            , exif_data_len(0)
            , f(NULL)
            {
            }
            ~CloseWriteFileResources() {
                if (exif_data) {
                    exif_mem_free(exifMemory.mem(), exif_data);
                }
                if (NULL != f) {
                    fclose(f);
                }
            }
        };
        void writeFile(const std::string &fileName) {
            CloseWriteFileResources res(exifMemory);
            exif_data_save_data(exif, &res.exif_data, &res.exif_data_len);
            if (res.exif_data == NULL) {
                throw std::runtime_error("Unexpected NULL exif_data");
            }
#ifdef _WIN32
            fopen_s(&res.f, fileName.c_str(), "wb");
#else
            res.f = fopen(fileName.c_str(), "wb");
#endif
            if (!res.f) {
                throw std::runtime_error("Could not create file");
            }
            if (fwrite(exif_header, sizeof(exif_header), 1, res.f) != 1) {
                throw std::runtime_error("Error writing EXIF header to file");
            }
            if (fputc((res.exif_data_len + 2) >> 8, res.f) < 0) {
                throw std::runtime_error("Error writing EXIF block length in big-endian order (1/2) to file");
            }
            if (fputc((res.exif_data_len + 2) & 0xff, res.f) < 0) {
                throw std::runtime_error("Error writing EXIF block length in big-endian order (2/2) to file");
            }
            if (fwrite(res.exif_data, res.exif_data_len, 1, res.f) != 1) {
                throw std::runtime_error("Error writing EXIF data block to file");
            }
            if (fwrite(jpeg, size, 1, res.f) != 1) {
                throw std::runtime_error("Error writing JPEG image data");
            }
        }
        template <typename T> void addTag(ExifIfd ifd, ExifTag tag, T value);
        void addUserComment(const std::string &str);
        void addThumbnail(unsigned char *data, size_t size);
    private:
        ExifMemory &exifMemory;
        ExifData *exif;
        uint8_t *jpeg;
        size_t size;
};

template <> inline void ExifResources::addTag<ExifShort>(ExifIfd ifd, ExifTag tag, ExifShort value) {
    ExifEntry *entry = initTag(ifd, tag);
    exif_set_short(entry->data, exif_data_get_byte_order(exif), value);
}

template <> inline void ExifResources::addTag<ExifSShort>(ExifIfd ifd, ExifTag tag, ExifSShort value) {
    ExifEntry *entry = initTag(ifd, tag);
    exif_set_sshort(entry->data, exif_data_get_byte_order(exif), value);
}

template <> inline void ExifResources::addTag<ExifLong>(ExifIfd ifd, ExifTag tag, ExifLong value) {
    ExifEntry *entry = initTag(ifd, tag);
    exif_set_long(entry->data, exif_data_get_byte_order(exif), value);
}

template <> inline void ExifResources::addTag<ExifSLong>(ExifIfd ifd, ExifTag tag, ExifSLong value) {
    ExifEntry *entry = initTag(ifd, tag);
    exif_set_slong(entry->data, exif_data_get_byte_order(exif), value);
}

template <> inline void ExifResources::addTag<std::string>(ExifIfd ifd, ExifTag tag, std::string str) {
    ExifEntry *entry = createTag(ifd, tag, str.length() + 1, EXIF_FORMAT_ASCII);
    memcpy(entry->data, str.c_str(), entry->size);
}

inline void ExifResources::addUserComment(const std::string &str) {
    char characterCode[] = { 'A', 'S', 'C', 'I', 'I', 0, 0, 0 };
    ExifEntry *entry = createTag(EXIF_IFD_EXIF, EXIF_TAG_USER_COMMENT, sizeof(characterCode) + str.length());
    memcpy(entry->data, characterCode, sizeof(characterCode));
    memcpy(entry->data + sizeof(characterCode), str.c_str(), str.length());
}

inline void ExifResources::addThumbnail(unsigned char *data, size_t size) {
    if (size > 64*1024) {
        // lowering the jpeg quality setting might help reduce the thumbnail size
        throw std::runtime_error("Compressed thumbnails shall be recorded in no more than 64 KBytes");
    }
    if (exif->data) {
        throw std::runtime_error("Thumbnail already exists");
    }
    exif->data = data;
    exif->size = static_cast<unsigned int>(size);
}

}

}

#endif
