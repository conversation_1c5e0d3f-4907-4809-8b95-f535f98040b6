﻿<Window x:Class="EGrabberWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Loaded="OnLoaded"
        Title="EGrabber WPF" Height="130" Width="600" FontWeight="Bold">
    <Border Padding="10">
        <StackPanel Loaded="OnLoaded">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Text="EGrabber" FontWeight="Bold" Margin="10,10,0,10" Grid.Column="0"/>
                <TextBox Grid.Column="1" Margin="10,10,10,10" Name="grabberInfo" IsReadOnly="True">
                </TextBox>
            </Grid>

            <!-- Buttons-->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Button Content="StartStream" Click="Button_Start" Grid.Column="0" Margin="10,10,10,0"></Button>
                <Button Content="StopStream" Click="Button_Stop" Grid.Column="1" Margin="10,10,10,0"></Button>
            </Grid>
        </StackPanel>
    </Border>
</Window>

    
    