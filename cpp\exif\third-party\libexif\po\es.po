# libexif Spanish Translation.
# Copyright:
#   Free Software Foundation, Inc., 2002
#   <PERSON> <<EMAIL>>, 2002
# This file is distributed under the same license as the libexif package.
#
msgid ""
msgstr ""
"Project-Id-Version: libexif\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2012-07-12 20:41+0200\n"
"PO-Revision-Date: 2005-03-12 05:43+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: libexif/canon/mnote-canon-entry.c:40 libexif/fuji/mnote-fuji-entry.c:35
#: libexif/olympus/mnote-olympus-entry.c:37
#: libexif/pentax/mnote-pentax-entry.c:39
#, c-format
msgid "Invalid format '%s', expected '%s'."
msgstr "Formato no válido '%s', se esperaba '%s'."

#: libexif/canon/mnote-canon-entry.c:52 libexif/fuji/mnote-fuji-entry.c:47
#: libexif/olympus/mnote-olympus-entry.c:62
#: libexif/pentax/mnote-pentax-entry.c:51
#, c-format
msgid "Invalid number of components (%i, expected %i)."
msgstr "Cantidad de componentes no válida (%i, se esperaba %i)."

#: libexif/canon/mnote-canon-entry.c:61
#: libexif/olympus/mnote-olympus-entry.c:72
#: libexif/pentax/mnote-pentax-entry.c:61
#, c-format
msgid "Invalid number of components (%i, expected %i or %i)."
msgstr "Cantidad de componentes no válida (%i, se esperaba %i o %i)."

#: libexif/canon/mnote-canon-entry.c:76 libexif/canon/mnote-canon-entry.c:130
#: libexif/canon/mnote-canon-entry.c:182 libexif/exif-entry.c:816
#: libexif/olympus/mnote-olympus-entry.c:199
#: libexif/olympus/mnote-olympus-tag.c:108
#: libexif/pentax/mnote-pentax-entry.c:174
#: libexif/pentax/mnote-pentax-entry.c:209
#: libexif/pentax/mnote-pentax-entry.c:297
msgid "Macro"
msgstr "Macro"

#: libexif/canon/mnote-canon-entry.c:77 libexif/canon/mnote-canon-entry.c:79
#: libexif/canon/mnote-canon-entry.c:157 libexif/canon/mnote-canon-entry.c:160
#: libexif/canon/mnote-canon-entry.c:163 libexif/exif-entry.c:694
#: libexif/exif-entry.c:697 libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/exif-entry.c:765 libexif/fuji/mnote-fuji-entry.c:64
#: libexif/olympus/mnote-olympus-entry.c:121
#: libexif/olympus/mnote-olympus-entry.c:198
#: libexif/olympus/mnote-olympus-entry.c:206
#: libexif/olympus/mnote-olympus-entry.c:216
#: libexif/olympus/mnote-olympus-entry.c:592
#: libexif/pentax/mnote-pentax-entry.c:105
#: libexif/pentax/mnote-pentax-entry.c:110
#: libexif/pentax/mnote-pentax-entry.c:115
#: libexif/pentax/mnote-pentax-entry.c:208
msgid "Normal"
msgstr "Normal"

#: libexif/canon/mnote-canon-entry.c:78
msgid "Economy"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:80
#, fuzzy
msgid "Fine"
msgstr "pulg"

#: libexif/canon/mnote-canon-entry.c:81 libexif/fuji/mnote-fuji-entry.c:178
#: libexif/pentax/mnote-pentax-entry.c:141
msgid "RAW"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:82
msgid "Superfine"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:83 libexif/canon/mnote-canon-entry.c:304
#: libexif/canon/mnote-canon-entry.c:307 libexif/canon/mnote-canon-entry.c:315
#: libexif/canon/mnote-canon-entry.c:348 libexif/canon/mnote-canon-entry.c:360
#: libexif/canon/mnote-canon-entry.c:373 libexif/canon/mnote-canon-entry.c:375
#: libexif/canon/mnote-canon-entry.c:577 libexif/canon/mnote-canon-entry.c:674
#: libexif/fuji/mnote-fuji-entry.c:70 libexif/fuji/mnote-fuji-entry.c:103
#: libexif/fuji/mnote-fuji-entry.c:107 libexif/fuji/mnote-fuji-entry.c:115
#: libexif/fuji/mnote-fuji-entry.c:142
#: libexif/olympus/mnote-olympus-entry.c:181
#: libexif/olympus/mnote-olympus-entry.c:189
#: libexif/olympus/mnote-olympus-entry.c:254
#: libexif/olympus/mnote-olympus-entry.c:536
#: libexif/olympus/mnote-olympus-entry.c:553
#: libexif/pentax/mnote-pentax-entry.c:195
#: libexif/pentax/mnote-pentax-entry.c:260
msgid "Off"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:84 libexif/canon/mnote-canon-entry.c:167
#: libexif/canon/mnote-canon-entry.c:180 libexif/canon/mnote-canon-entry.c:331
#: libexif/canon/mnote-canon-entry.c:403 libexif/fuji/mnote-fuji-entry.c:73
#: libexif/fuji/mnote-fuji-entry.c:101 libexif/fuji/mnote-fuji-entry.c:111
#: libexif/fuji/mnote-fuji-entry.c:119
#: libexif/olympus/mnote-olympus-entry.c:134
#: libexif/olympus/mnote-olympus-entry.c:186
#: libexif/olympus/mnote-olympus-entry.c:202
#: libexif/olympus/mnote-olympus-entry.c:247
#: libexif/pentax/mnote-pentax-entry.c:77
#: libexif/pentax/mnote-pentax-entry.c:88
#: libexif/pentax/mnote-pentax-entry.c:91
#: libexif/pentax/mnote-pentax-entry.c:97
#: libexif/pentax/mnote-pentax-entry.c:131
#: libexif/pentax/mnote-pentax-entry.c:229
#: libexif/pentax/mnote-pentax-entry.c:245
#: libexif/pentax/mnote-pentax-entry.c:290
msgid "Auto"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:85 libexif/canon/mnote-canon-entry.c:305
#: libexif/canon/mnote-canon-entry.c:350 libexif/canon/mnote-canon-entry.c:364
#: libexif/canon/mnote-canon-entry.c:374 libexif/fuji/mnote-fuji-entry.c:102
#: libexif/fuji/mnote-fuji-entry.c:108 libexif/fuji/mnote-fuji-entry.c:116
#: libexif/fuji/mnote-fuji-entry.c:143
#: libexif/olympus/mnote-olympus-entry.c:182
#: libexif/olympus/mnote-olympus-entry.c:539
#: libexif/olympus/mnote-olympus-entry.c:556
#: libexif/pentax/mnote-pentax-entry.c:198
#: libexif/pentax/mnote-pentax-entry.c:261
msgid "On"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:86 libexif/fuji/mnote-fuji-entry.c:104
#: libexif/olympus/mnote-olympus-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:94
#, fuzzy
msgid "Red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/canon/mnote-canon-entry.c:87
msgid "Slow synchro"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:88
#, fuzzy
msgid "Auto, red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/canon/mnote-canon-entry.c:89
#: libexif/pentax/mnote-pentax-entry.c:200
#, fuzzy
msgid "On, red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/canon/mnote-canon-entry.c:90
#, fuzzy
msgid "External flash"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:91 libexif/canon/mnote-canon-entry.c:101
#: libexif/canon/mnote-canon-entry.c:297
msgid "Single"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:92 libexif/canon/mnote-canon-entry.c:102
#: libexif/canon/mnote-canon-entry.c:298
msgid "Continuous"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:93
msgid "Movie"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:94
msgid "Continuous, speed priority"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:95
msgid "Continuous, low"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:96
msgid "Continuous, high"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:97
msgid "One-shot AF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:98
msgid "AI servo AF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:99
msgid "AI focus AF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:100 libexif/canon/mnote-canon-entry.c:103
#, fuzzy
msgid "Manual focus"
msgstr "Exposición manual"

#: libexif/canon/mnote-canon-entry.c:104 libexif/canon/mnote-canon-entry.c:132
#: libexif/canon/mnote-canon-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:212
#, fuzzy
msgid "Pan focus"
msgstr "Exposición manual"

#: libexif/canon/mnote-canon-entry.c:105
msgid "JPEG"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:106
msgid "CRW+THM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:107
msgid "AVI+THM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:108
msgid "TIF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:109
msgid "TIF+JPEG"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:110
msgid "CR2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:111
msgid "CR2+JPEG"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:112
#, fuzzy
msgid "Large"
msgstr "promedio"

#: libexif/canon/mnote-canon-entry.c:113
msgid "Medium"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:114
msgid "Small"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:115
msgid "Medium 1"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:116
msgid "Medium 2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:117
msgid "Medium 3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:118
msgid "Postcard"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:119
msgid "Widescreen"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:120
#, fuzzy
msgid "Full auto"
msgstr "acción"

#: libexif/canon/mnote-canon-entry.c:121 libexif/canon/mnote-canon-entry.c:179
#: libexif/canon/mnote-canon-entry.c:201 libexif/canon/mnote-canon-entry.c:288
#: libexif/canon/mnote-canon-entry.c:395 libexif/exif-entry.c:764
#: libexif/fuji/mnote-fuji-entry.c:112
#: libexif/olympus/mnote-olympus-entry.c:93
#: libexif/olympus/mnote-olympus-entry.c:203
#: libexif/pentax/mnote-pentax-entry.c:79
#: libexif/pentax/mnote-pentax-entry.c:102
#: libexif/pentax/mnote-pentax-entry.c:133
#: libexif/pentax/mnote-pentax-entry.c:165
#: libexif/pentax/mnote-pentax-entry.c:211
#: libexif/pentax/mnote-pentax-entry.c:250
msgid "Manual"
msgstr "Manual"

#: libexif/canon/mnote-canon-entry.c:122 libexif/canon/mnote-canon-entry.c:433
#: libexif/exif-entry.c:691 libexif/exif-entry.c:775
#: libexif/fuji/mnote-fuji-entry.c:121 libexif/pentax/mnote-pentax-entry.c:167
#: libexif/pentax/mnote-pentax-entry.c:301
msgid "Landscape"
msgstr "Paisaje"

#: libexif/canon/mnote-canon-entry.c:123
#, fuzzy
msgid "Fast shutter"
msgstr "obturador"

#: libexif/canon/mnote-canon-entry.c:124
#, fuzzy
msgid "Slow shutter"
msgstr "obturador"

#: libexif/canon/mnote-canon-entry.c:125 libexif/fuji/mnote-fuji-entry.c:123
#: libexif/olympus/mnote-olympus-entry.c:257
#, fuzzy
msgid "Night"
msgstr "Escena nocturna"

#: libexif/canon/mnote-canon-entry.c:126
msgid "Grayscale"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:127 libexif/canon/mnote-canon-entry.c:311
#: libexif/pentax/mnote-pentax-entry.c:128
msgid "Sepia"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:128 libexif/canon/mnote-canon-entry.c:432
#: libexif/exif-entry.c:691 libexif/exif-entry.c:773
#: libexif/fuji/mnote-fuji-entry.c:120 libexif/pentax/mnote-pentax-entry.c:166
#: libexif/pentax/mnote-pentax-entry.c:291
#: libexif/pentax/mnote-pentax-entry.c:294
#: libexif/pentax/mnote-pentax-entry.c:300
msgid "Portrait"
msgstr "Retrato"

#: libexif/canon/mnote-canon-entry.c:129 libexif/fuji/mnote-fuji-entry.c:122
#, fuzzy
msgid "Sports"
msgstr "Lugar"

#: libexif/canon/mnote-canon-entry.c:131 libexif/canon/mnote-canon-entry.c:312
#: libexif/canon/mnote-canon-entry.c:338 libexif/canon/mnote-canon-entry.c:410
#: libexif/fuji/mnote-fuji-entry.c:89 libexif/pentax/mnote-pentax-entry.c:127
msgid "Black & white"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:133 libexif/canon/mnote-canon-entry.c:308
msgid "Vivid"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:134 libexif/canon/mnote-canon-entry.c:309
#: libexif/canon/mnote-canon-entry.c:434
#, fuzzy
msgid "Neutral"
msgstr "Normal"

#: libexif/canon/mnote-canon-entry.c:135
#: libexif/pentax/mnote-pentax-entry.c:93
#, fuzzy
msgid "Flash off"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:136
#, fuzzy
msgid "Long shutter"
msgstr "obturador"

#: libexif/canon/mnote-canon-entry.c:137 libexif/canon/mnote-canon-entry.c:188
#: libexif/olympus/mnote-olympus-entry.c:174
msgid "Super macro"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:138
msgid "Foliage"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:139
msgid "Indoor"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:140 libexif/fuji/mnote-fuji-entry.c:135
#: libexif/pentax/mnote-pentax-entry.c:175
msgid "Fireworks"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:141 libexif/fuji/mnote-fuji-entry.c:133
msgid "Beach"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:142 libexif/canon/mnote-canon-entry.c:347
#: libexif/canon/mnote-canon-entry.c:419 libexif/fuji/mnote-fuji-entry.c:136
#: libexif/pentax/mnote-pentax-entry.c:187
#: libexif/pentax/mnote-pentax-entry.c:292
#: libexif/pentax/mnote-pentax-entry.c:298
#, fuzzy
msgid "Underwater"
msgstr "Tiempo bueno"

#: libexif/canon/mnote-canon-entry.c:143 libexif/fuji/mnote-fuji-entry.c:134
msgid "Snow"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:144
msgid "Kids & pets"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:145
#, fuzzy
msgid "Night snapshot"
msgstr "Escena nocturna"

#: libexif/canon/mnote-canon-entry.c:146
#, fuzzy
msgid "Digital macro"
msgstr "Relación de zoom digital"

#: libexif/canon/mnote-canon-entry.c:147
msgid "My colors"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:148
msgid "Still image"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:149
#, fuzzy
msgid "Color accent"
msgstr "Espacio de color"

#: libexif/canon/mnote-canon-entry.c:150
#, fuzzy
msgid "Color swap"
msgstr "Espacio de color"

#: libexif/canon/mnote-canon-entry.c:151
msgid "Aquarium"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:152
msgid "ISO 3200"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:153 libexif/canon/mnote-canon-entry.c:351
#: libexif/canon/mnote-canon-entry.c:368 libexif/canon/mnote-canon-entry.c:420
#: libexif/olympus/mnote-olympus-entry.c:192
#: libexif/olympus/mnote-olympus-entry.c:229
#: libexif/olympus/mnote-olympus-entry.c:457
#: libexif/pentax/mnote-pentax-entry.c:242
msgid "None"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:154
msgid "2x"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:155
msgid "4x"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:156 libexif/exif-entry.c:722
#: libexif/exif-entry.c:752
msgid "Other"
msgstr "Otro"

#: libexif/canon/mnote-canon-entry.c:158 libexif/canon/mnote-canon-entry.c:161
#: libexif/canon/mnote-canon-entry.c:164 libexif/canon/mnote-canon-entry.c:401
#: libexif/fuji/mnote-fuji-entry.c:86 libexif/pentax/mnote-pentax-entry.c:112
#: libexif/pentax/mnote-pentax-entry.c:117
msgid "High"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:159 libexif/canon/mnote-canon-entry.c:162
#: libexif/canon/mnote-canon-entry.c:165 libexif/canon/mnote-canon-entry.c:399
#: libexif/pentax/mnote-pentax-entry.c:111
#: libexif/pentax/mnote-pentax-entry.c:116
msgid "Low"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:166
msgid "Auto high"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:168
msgid "50"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:169
#: libexif/pentax/mnote-pentax-entry.c:120
#: libexif/pentax/mnote-pentax-entry.c:122
msgid "100"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:170
#: libexif/pentax/mnote-pentax-entry.c:121
#: libexif/pentax/mnote-pentax-entry.c:123
msgid "200"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:171
msgid "400"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:172
msgid "800"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:173
msgid "Default"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:174 libexif/exif-entry.c:718
msgid "Spot"
msgstr "Lugar"

#: libexif/canon/mnote-canon-entry.c:175 libexif/exif-entry.c:716
msgid "Average"
msgstr "Promedio"

#: libexif/canon/mnote-canon-entry.c:176
msgid "Evaluative"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:177 libexif/exif-entry.c:721
msgid "Partial"
msgstr "Parcial"

#: libexif/canon/mnote-canon-entry.c:178 libexif/exif-entry.c:717
#, fuzzy
msgid "Center-weighted average"
msgstr "Promedio Ponderado en el Centro"

#: libexif/canon/mnote-canon-entry.c:181
#, fuzzy
msgid "Not known"
msgstr "Desconocido"

#: libexif/canon/mnote-canon-entry.c:183
#, fuzzy
msgid "Very close"
msgstr "co-situado"

#: libexif/canon/mnote-canon-entry.c:184 libexif/exif-entry.c:817
#, fuzzy
msgid "Close"
msgstr "co-situado"

#: libexif/canon/mnote-canon-entry.c:185
msgid "Middle range"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:186
#, fuzzy
msgid "Far range"
msgstr "Rango de transferencia"

#: libexif/canon/mnote-canon-entry.c:189
#: libexif/pentax/mnote-pentax-entry.c:210
msgid "Infinity"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:190
#, fuzzy
msgid "Manual AF point selection"
msgstr "Balance de blanco manual"

#: libexif/canon/mnote-canon-entry.c:191 libexif/canon/mnote-canon-entry.c:352
msgid "None (MF)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:192
msgid "Auto-selected"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:193 libexif/canon/mnote-canon-entry.c:353
#: libexif/pentax/mnote-pentax-entry.c:224
#: libexif/pentax/mnote-pentax-entry.c:238
#, fuzzy
msgid "Right"
msgstr "Luz de día"

#: libexif/canon/mnote-canon-entry.c:194 libexif/canon/mnote-canon-entry.c:354
#: libexif/pentax/mnote-pentax-entry.c:222
#: libexif/pentax/mnote-pentax-entry.c:237
#, fuzzy
msgid "Center"
msgstr "Centrado"

#: libexif/canon/mnote-canon-entry.c:195 libexif/canon/mnote-canon-entry.c:356
#: libexif/pentax/mnote-pentax-entry.c:220
#: libexif/pentax/mnote-pentax-entry.c:236
msgid "Left"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:196
msgid "Auto AF point selection"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:197
msgid "Easy shooting"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:198
#: libexif/pentax/mnote-pentax-entry.c:163
msgid "Program"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:199
msgid "Tv-priority"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:200
#, fuzzy
msgid "Av-priority"
msgstr "Prioridad de apertura"

#: libexif/canon/mnote-canon-entry.c:202
msgid "A-DEP"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:203
msgid "M-DEP"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:204
msgid "Canon EF 50mm f/1.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:205
msgid "Canon EF 28mm f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:206
msgid "Sigma UC Zoom 35-135mm f/4-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:207
msgid "Tokina AF193-2 19-35mm f/3.5-4.5"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:208
msgid "Canon EF 100-300mm F5.6L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:209
msgid "Sigma 50mm f/2.8 EX or 28mm f/1.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:210
msgid "Canon EF 35mm f/2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:211
msgid "Canon EF 15mm f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:212
msgid "Canon EF 80-200mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:213
msgid "Tokina AT-X280AF PRO 28-80mm F2.8 Aspherical"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:214
msgid "Cosina 100mm f/3.5 Macro AF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:215
msgid "Tamron AF Aspherical 28-200mm f/3.8-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:216
msgid "Canon EF 50mm f/1.8 MkII"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:217
msgid "Tamron SP AF 300mm f/2.8 LD IF"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:218
msgid "Canon EF 24mm f/2.8 or Sigma 15mm f/2.8 EX Fisheye"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:219
msgid "Canon EF 35-80mm f/4-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:220
msgid "Canon EF 75-300mm f/4-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:221
msgid "Canon EF 28-80mm f/3.5-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:222
msgid "Canon EF 28-105mm f/4-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:223
msgid "Canon EF-S 18-55mm f/3.5-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:224
msgid "Canon EF-S 18-55mm f/3.5-5.6 IS II"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:225
msgid "Canon MP-E 65mm f/2.8 1-5x Macro Photo"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:226
msgid "Canon TS-E 24mm f/3.5L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:227
msgid "Canon TS-E 45mm f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:228
msgid "Canon TS-E 90mm f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:229
msgid "Canon EF 50mm f/1.0L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:230
msgid "Sigma 17-35mm f2.8-4 EX Aspherical HSM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:231
msgid "Canon EF 600mm f/4L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:232
msgid "Canon EF 200mm f/1.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:233
msgid "Canon EF 300mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:234
msgid "Canon EF 85mm f/1.2L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:235
msgid "Canon EF 400mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:236
msgid "Canon EF 500mm f/4.5L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:237
msgid "Canon EF 300mm f/2.8L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:238
msgid "Canon EF 500mm f/4L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:239
msgid "Canon EF 100mm f/2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:240
msgid "Sigma 20mm EX f/1.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:241
msgid "Canon EF 200mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:242
msgid "Sigma 10-20mm F4-5.6 or 12-24mm f/4.5-5.6 or 14mm f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:243
msgid "Canon EF 35-350mm f/3.5-5.6L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:244
msgid "Canon EF 85mm f/1.8 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:245
msgid "Canon EF 28-105mm f/3.5-4.5 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:246
msgid "Canon EF 20-35mm f/3.5-4.5 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:247
msgid "Canon EF 28-70mm f/2.8L or Sigma 24-70mm EX f/2.8"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:248
msgid "Canon EF 70-200mm f/2.8 L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:249
msgid "Canon EF 70-200mm f/2.8 L + x1.4"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:250
msgid "Canon EF 70-200mm f/2.8 L + x2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:251
msgid "Canon EF 28mm f/1.8 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:252
msgid "Sigma 15-30mm f/3.5-4.5 EX DG Aspherical"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:253
msgid "Canon EF 200mm f/2.8L II"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:254
msgid "Canon EF 180mm Macro f/3.5L or Sigma 180mm EX HSM Macro f/3.5"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:255
msgid "Canon EF 135mm f/2L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:256
msgid "Canon EF 24-85mm f/3.5-4.5 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:257
msgid "Canon EF 300mm f/4L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:258
msgid "Canon EF 28-135mm f/3.5-5.6 IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:259
msgid "Canon EF 35mm f/1.4L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:260
msgid "Canon EF 100-400mm f/4.5-5.6L IS + x1.4"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:261
msgid "Canon EF 100-400mm f/4.5-5.6L IS + x2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:262
msgid "Canon EF 100-400mm f/4.5-5.6L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:263
msgid "Canon EF 400mm f/2.8L + x2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:264
msgid "Canon EF 70-200mm f/4L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:265
msgid "Canon EF 100mm f/2.8 Macro"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:266
msgid "Canon EF 400mm f/4 DO IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:267
msgid "Canon EF 75-300mm f/4-5.6 IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:268
msgid "Canon EF 50mm f/1.4"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:269
msgid "Canon EF 28-80 f/3.5-5.6 USM IV"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:270
msgid "Canon EF 28-200mm f/3.5-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:271
msgid "Canon EF 90-300mm f/4.5-5.6"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:272
msgid "Canon EF-S 18-55mm f/3.5-4.5 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:273
msgid "Canon EF 70-200mm f/2.8L IS USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:274
msgid "Canon EF 70-200mm f/2.8L IS USM + x1.4"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:275
msgid "Canon EF 70-200mm f/2.8L IS USM + x2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:276
msgid "Canon EF 16-35mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:277
msgid "Canon EF 24-70mm f/2.8L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:278
msgid "Canon EF 17-40mm f/4L"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:279
msgid "Canon EF 70-300mm f/4.5-5.6 DO IS USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:280
msgid "Canon EF-S 17-85mm f4-5.6 IS USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:281
msgid "Canon EF-S10-22mm F3.5-4.5 USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:282
msgid "Canon EF-S60mm F2.8 Macro USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:283
msgid "Canon EF 24-105mm f/4L IS"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:284
msgid "Canon EF 70-300mm F4-5.6 IS USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:285
msgid "Canon EF 50mm F1.2L USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:286
msgid "Canon EF 70-200mm f/4L IS USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:287
msgid "Canon EF 70-200mm f/2.8L IS II USM"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:289
msgid "TTL"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:290
msgid "A-TTL"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:291
msgid "E-TTL"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:292
msgid "FP sync enabled"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:293
msgid "2nd-curtain sync used"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:294
msgid "FP sync used"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:295
#: libexif/olympus/mnote-olympus-entry.c:193
msgid "Internal"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:296
#: libexif/olympus/mnote-olympus-entry.c:194
msgid "External"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:299
#, fuzzy
msgid "Normal AE"
msgstr "Normal"

#: libexif/canon/mnote-canon-entry.c:300
#, fuzzy
msgid "Exposure compensation"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-entry.c:301
msgid "AE lock"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:302
#, fuzzy
msgid "AE lock + exposure compensation"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-entry.c:303
msgid "No AE"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:306
msgid "On, shot only"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:310
msgid "Smooth"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:313 libexif/canon/mnote-canon-entry.c:337
#: libexif/canon/mnote-canon-entry.c:396 libexif/canon/mnote-canon-entry.c:409
#: libexif/fuji/mnote-fuji-entry.c:81 libexif/pentax/mnote-pentax-entry.c:87
#, fuzzy
msgid "Custom"
msgstr "Proceso personalizado"

#: libexif/canon/mnote-canon-entry.c:314
msgid "My color data"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:316 libexif/canon/mnote-canon-entry.c:378
#: libexif/pentax/mnote-pentax-entry.c:126
#: libexif/pentax/mnote-pentax-entry.c:145
msgid "Full"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:317 libexif/canon/mnote-canon-entry.c:377
msgid "2/3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:318 libexif/canon/mnote-canon-entry.c:376
msgid "1/3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:324
msgid "Fixed"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:325 libexif/pentax/mnote-pentax-tag.c:44
msgid "Zoom"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:332
#, fuzzy
msgid "Sunny"
msgstr "soleado"

#: libexif/canon/mnote-canon-entry.c:333 libexif/canon/mnote-canon-entry.c:405
#: libexif/exif-entry.c:739 libexif/fuji/mnote-fuji-entry.c:75
#: libexif/olympus/mnote-olympus-entry.c:139
#: libexif/pentax/mnote-pentax-entry.c:255
#, fuzzy
msgid "Cloudy"
msgstr "nublado"

#: libexif/canon/mnote-canon-entry.c:334 libexif/canon/mnote-canon-entry.c:406
#: libexif/exif-entry.c:736 libexif/pentax/mnote-pentax-entry.c:100
#: libexif/pentax/mnote-pentax-entry.c:249
#, fuzzy
msgid "Tungsten"
msgstr "tungsteno"

#: libexif/canon/mnote-canon-entry.c:335 libexif/canon/mnote-canon-entry.c:407
#: libexif/exif-entry.c:735 libexif/pentax/mnote-pentax-entry.c:101
#: libexif/pentax/mnote-pentax-entry.c:248
msgid "Fluorescent"
msgstr "Fluorescente"

#: libexif/canon/mnote-canon-entry.c:336 libexif/canon/mnote-canon-entry.c:408
#: libexif/exif-entry.c:737 libexif/exif-entry.c:779 libexif/exif-tag.c:577
#: libexif/fuji/mnote-fuji-entry.c:80 libexif/pentax/mnote-pentax-entry.c:254
msgid "Flash"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:339 libexif/canon/mnote-canon-entry.c:411
#: libexif/exif-entry.c:740 libexif/pentax/mnote-pentax-entry.c:99
#: libexif/pentax/mnote-pentax-entry.c:247
msgid "Shade"
msgstr "Sombra"

#: libexif/canon/mnote-canon-entry.c:340 libexif/canon/mnote-canon-entry.c:412
msgid "Manual temperature (Kelvin)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:341 libexif/canon/mnote-canon-entry.c:413
msgid "PC set 1"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:342 libexif/canon/mnote-canon-entry.c:414
msgid "PC set 2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:343 libexif/canon/mnote-canon-entry.c:415
msgid "PC set 3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:344 libexif/canon/mnote-canon-entry.c:416
#: libexif/exif-entry.c:741 libexif/fuji/mnote-fuji-entry.c:76
#: libexif/pentax/mnote-pentax-entry.c:251
msgid "Daylight fluorescent"
msgstr "Luz de día fluorescente"

#: libexif/canon/mnote-canon-entry.c:345 libexif/canon/mnote-canon-entry.c:417
#, fuzzy
msgid "Custom 1"
msgstr "Proceso personalizado"

#: libexif/canon/mnote-canon-entry.c:346 libexif/canon/mnote-canon-entry.c:418
#, fuzzy
msgid "Custom 2"
msgstr "Proceso personalizado"

#: libexif/canon/mnote-canon-entry.c:349 libexif/exif-entry.c:692
#: libexif/pentax/mnote-pentax-entry.c:78
#: libexif/pentax/mnote-pentax-entry.c:132
#: libexif/pentax/mnote-pentax-entry.c:169
#: libexif/pentax/mnote-pentax-entry.c:295
msgid "Night scene"
msgstr "Escena nocturna"

#: libexif/canon/mnote-canon-entry.c:355
#, fuzzy
msgid "Center-right"
msgstr "peso centrado"

#: libexif/canon/mnote-canon-entry.c:357
#, fuzzy
msgid "Left-right"
msgstr "abajo - derecha"

#: libexif/canon/mnote-canon-entry.c:358
#, fuzzy
msgid "Left-center"
msgstr "abajo centrado"

#: libexif/canon/mnote-canon-entry.c:359
msgid "All"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:361
msgid "On (shot 1)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:362
msgid "On (shot 2)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:363
msgid "On (shot 3)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:365
msgid "EOS high-end"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:366
msgid "Compact"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:367
msgid "EOS mid-range"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:369
msgid "Rotate 90 CW"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:370
msgid "Rotate 180"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:371
msgid "Rotate 270 CW"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:372
msgid "Rotated by software"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:384
#: libexif/olympus/mnote-olympus-entry.c:612
#, fuzzy
msgid "Left to right"
msgstr "abajo - derecha"

#: libexif/canon/mnote-canon-entry.c:385
#: libexif/olympus/mnote-olympus-entry.c:615
#, fuzzy
msgid "Right to left"
msgstr "derecha - arriba"

#: libexif/canon/mnote-canon-entry.c:386
#: libexif/olympus/mnote-olympus-entry.c:618
#, fuzzy
msgid "Bottom to top"
msgstr "abajo - izquierda"

#: libexif/canon/mnote-canon-entry.c:387
#: libexif/olympus/mnote-olympus-entry.c:621
#, fuzzy
msgid "Top to bottom"
msgstr "izquierda - abajo"

#: libexif/canon/mnote-canon-entry.c:388
msgid "2x2 matrix (clockwise)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:394 libexif/canon/mnote-canon-entry.c:400
#: libexif/canon/mnote-canon-entry.c:421 libexif/canon/mnote-canon-entry.c:431
#: libexif/exif-entry.c:691 libexif/fuji/mnote-fuji-entry.c:84
#: libexif/fuji/mnote-fuji-entry.c:93 libexif/fuji/mnote-fuji-entry.c:163
#: libexif/olympus/mnote-olympus-entry.c:230
msgid "Standard"
msgstr "Estándar"

#: libexif/canon/mnote-canon-entry.c:397
msgid "N/A"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:398
#, fuzzy
msgid "Lowest"
msgstr "arriba - izquierda"

#: libexif/canon/mnote-canon-entry.c:402
#, fuzzy
msgid "Highest"
msgstr "Escena nocturna"

#: libexif/canon/mnote-canon-entry.c:404 libexif/exif-entry.c:734
#: libexif/fuji/mnote-fuji-entry.c:74
#: libexif/olympus/mnote-olympus-entry.c:136
#: libexif/pentax/mnote-pentax-entry.c:98
#: libexif/pentax/mnote-pentax-entry.c:246
msgid "Daylight"
msgstr "Luz de día"

#: libexif/canon/mnote-canon-entry.c:422
msgid "Set 1"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:423
msgid "Set 2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:424
msgid "Set 3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:425
msgid "User def. 1"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:426
msgid "User def. 2"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:427
msgid "User def. 3"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:428
#, fuzzy
msgid "External 1"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:429
#, fuzzy
msgid "External 2"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:430
#, fuzzy
msgid "External 3"
msgstr "Flash"

#: libexif/canon/mnote-canon-entry.c:435
msgid "Faithful"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:436
#: libexif/olympus/mnote-olympus-entry.c:118
msgid "Monochrome"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:494
msgid ", "
msgstr ""

#: libexif/canon/mnote-canon-entry.c:580 libexif/canon/mnote-canon-entry.c:677
#, c-format
msgid "%i (ms)"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:624
#, c-format
msgid "%.2f mm"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:648
#, c-format
msgid "%.2f EV"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:658 libexif/exif-entry.c:1089
#, c-format
msgid "1/%i"
msgstr ""

#: libexif/canon/mnote-canon-entry.c:670
#, c-format
msgid "%u mm"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:35
msgid "Settings (First Part)"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:36 libexif/canon/mnote-canon-tag.c:92
#: libexif/exif-tag.c:581 libexif/pentax/mnote-pentax-tag.c:88
msgid "Focal Length"
msgstr "Distancia focal"

#: libexif/canon/mnote-canon-tag.c:37
msgid "Settings (Second Part)"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:38
#: libexif/olympus/mnote-olympus-entry.c:601
#: libexif/pentax/mnote-pentax-entry.c:177
msgid "Panorama"
msgstr "Panorama"

#: libexif/canon/mnote-canon-tag.c:39
#, fuzzy
msgid "Image Type"
msgstr "Ancho de la imagen"

#: libexif/canon/mnote-canon-tag.c:40 libexif/olympus/mnote-olympus-tag.c:37
#: libexif/olympus/mnote-olympus-tag.c:113
msgid "Firmware Version"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:41
#, fuzzy
msgid "Image Number"
msgstr "ID único de imagen"

#: libexif/canon/mnote-canon-tag.c:42
#, fuzzy
msgid "Owner Name"
msgstr "El número F."

#: libexif/canon/mnote-canon-tag.c:43
#, fuzzy
msgid "Color Information"
msgstr "Espacio de color"

#: libexif/canon/mnote-canon-tag.c:44 libexif/fuji/mnote-fuji-tag.c:37
#: libexif/olympus/mnote-olympus-tag.c:81
#: libexif/olympus/mnote-olympus-tag.c:146
#, fuzzy
msgid "Serial Number"
msgstr "El número F."

#: libexif/canon/mnote-canon-tag.c:45
#, fuzzy
msgid "Custom Functions"
msgstr "Proceso personalizado"

#: libexif/canon/mnote-canon-tag.c:56 libexif/fuji/mnote-fuji-tag.c:45
#, fuzzy
msgid "Macro Mode"
msgstr "Macro"

#: libexif/canon/mnote-canon-tag.c:57 libexif/canon/mnote-canon-tag.c:117
#: libexif/olympus/mnote-olympus-tag.c:175
#: libexif/pentax/mnote-pentax-tag.c:128
msgid "Self-timer"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:58 libexif/fuji/mnote-fuji-tag.c:38
#: libexif/olympus/mnote-olympus-tag.c:40
#: libexif/olympus/mnote-olympus-tag.c:94
#: libexif/olympus/mnote-olympus-tag.c:107
msgid "Quality"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:59 libexif/fuji/mnote-fuji-tag.c:43
#: libexif/olympus/mnote-olympus-tag.c:45
#: libexif/olympus/mnote-olympus-tag.c:127
#: libexif/pentax/mnote-pentax-tag.c:38 libexif/pentax/mnote-pentax-tag.c:73
#, fuzzy
msgid "Flash Mode"
msgstr "Flash"

#: libexif/canon/mnote-canon-tag.c:60 libexif/pentax/mnote-pentax-tag.c:101
#, fuzzy
msgid "Drive Mode"
msgstr "Modo de métrica"

#: libexif/canon/mnote-canon-tag.c:61 libexif/canon/mnote-canon-tag.c:82
#: libexif/olympus/mnote-olympus-tag.c:43
#: libexif/olympus/mnote-olympus-tag.c:134
#: libexif/olympus/mnote-olympus-tag.c:173
#: libexif/pentax/mnote-pentax-tag.c:37 libexif/pentax/mnote-pentax-tag.c:74
#: libexif/pentax/mnote-pentax-tag.c:130
#, fuzzy
msgid "Focus Mode"
msgstr "Modo de exposición"

#: libexif/canon/mnote-canon-tag.c:62 libexif/pentax/mnote-pentax-tag.c:127
#, fuzzy
msgid "Record Mode"
msgstr "Macro"

#: libexif/canon/mnote-canon-tag.c:63 libexif/pentax/mnote-pentax-tag.c:71
#, fuzzy
msgid "Image Size"
msgstr "Ancho de la imagen"

#: libexif/canon/mnote-canon-tag.c:64
msgid "Easy Shooting Mode"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:65 libexif/olympus/mnote-olympus-tag.c:64
#: libexif/olympus/mnote-olympus-tag.c:101
#: libexif/olympus/mnote-olympus-tag.c:110
#: libexif/olympus/mnote-olympus-tag.c:180
#: libexif/pentax/mnote-pentax-tag.c:89
#, fuzzy
msgid "Digital Zoom"
msgstr "Relación de zoom digital"

#: libexif/canon/mnote-canon-tag.c:66 libexif/exif-tag.c:828
#: libexif/fuji/mnote-fuji-tag.c:42 libexif/pentax/mnote-pentax-tag.c:46
#: libexif/pentax/mnote-pentax-tag.c:91
msgid "Contrast"
msgstr "Contraste"

#: libexif/canon/mnote-canon-tag.c:67 libexif/exif-tag.c:832
#: libexif/olympus/mnote-olympus-tag.c:75
#: libexif/olympus/mnote-olympus-tag.c:87 libexif/pentax/mnote-pentax-tag.c:47
#: libexif/pentax/mnote-pentax-tag.c:90
msgid "Saturation"
msgstr "Saturación"

#: libexif/canon/mnote-canon-tag.c:68 libexif/exif-tag.c:836
#: libexif/fuji/mnote-fuji-tag.c:39 libexif/pentax/mnote-pentax-tag.c:45
#: libexif/pentax/mnote-pentax-tag.c:92
msgid "Sharpness"
msgstr "Nitidez"

#: libexif/canon/mnote-canon-tag.c:69
msgid "ISO"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:70 libexif/exif-tag.c:571
#: libexif/pentax/mnote-pentax-tag.c:82
msgid "Metering Mode"
msgstr "Modo de métrica"

#: libexif/canon/mnote-canon-tag.c:71 libexif/olympus/mnote-olympus-tag.c:133
#, fuzzy
msgid "Focus Range"
msgstr "Modo de exposición"

#: libexif/canon/mnote-canon-tag.c:72 libexif/canon/mnote-canon-tag.c:105
#, fuzzy
msgid "AF Point"
msgstr "Modo de exposición"

#: libexif/canon/mnote-canon-tag.c:73 libexif/exif-tag.c:795
msgid "Exposure Mode"
msgstr "Modo de exposición"

#: libexif/canon/mnote-canon-tag.c:74 libexif/olympus/mnote-olympus-tag.c:61
#: libexif/pentax/mnote-pentax-tag.c:106
#, fuzzy
msgid "Lens Type"
msgstr "Tipo de escena"

#: libexif/canon/mnote-canon-tag.c:75
#, fuzzy
msgid "Long Focal Length of Lens"
msgstr "Distancia focal"

#: libexif/canon/mnote-canon-tag.c:76
#, fuzzy
msgid "Short Focal Length of Lens"
msgstr "Distancia focal"

#: libexif/canon/mnote-canon-tag.c:77
#, fuzzy
msgid "Focal Units per mm"
msgstr "Distancia focal"

#: libexif/canon/mnote-canon-tag.c:78
#, fuzzy
msgid "Maximal Aperture"
msgstr "apertura"

#: libexif/canon/mnote-canon-tag.c:79
#, fuzzy
msgid "Minimal Aperture"
msgstr "apertura"

#: libexif/canon/mnote-canon-tag.c:80
#, fuzzy
msgid "Flash Activity"
msgstr "El flash disparó."

#: libexif/canon/mnote-canon-tag.c:81
#, fuzzy
msgid "Flash Details"
msgstr "Flash"

#: libexif/canon/mnote-canon-tag.c:83
#, fuzzy
msgid "AE Setting"
msgstr "Velocidad ISO"

#: libexif/canon/mnote-canon-tag.c:84
#, fuzzy
msgid "Image Stabilization"
msgstr "Descripción de la imagen"

#: libexif/canon/mnote-canon-tag.c:85
#, fuzzy
msgid "Display Aperture"
msgstr "apertura"

#: libexif/canon/mnote-canon-tag.c:86
#, fuzzy
msgid "Zoom Source Width"
msgstr "Ancho de la imagen"

#: libexif/canon/mnote-canon-tag.c:87
#, fuzzy
msgid "Zoom Target Width"
msgstr "Ancho de la imagen"

#: libexif/canon/mnote-canon-tag.c:88
msgid "Photo Effect"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:89 libexif/canon/mnote-canon-tag.c:118
msgid "Manual Flash Output"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:90
#, fuzzy
msgid "Color Tone"
msgstr "Espacio de color"

#: libexif/canon/mnote-canon-tag.c:91
#, fuzzy
msgid "Focal Type"
msgstr "Distancia focal"

#: libexif/canon/mnote-canon-tag.c:93
#, fuzzy
msgid "Focal Plane X Size"
msgstr "Resolución X del plano focal"

#: libexif/canon/mnote-canon-tag.c:94
#, fuzzy
msgid "Focal Plane Y Size"
msgstr "Resolución X del plano focal"

#: libexif/canon/mnote-canon-tag.c:95
msgid "Auto ISO"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:96
#, fuzzy
msgid "Shot ISO"
msgstr "Lugar"

#: libexif/canon/mnote-canon-tag.c:97
msgid "Measured EV"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:98
#, fuzzy
msgid "Target Aperture"
msgstr "apertura"

#: libexif/canon/mnote-canon-tag.c:99
#, fuzzy
msgid "Target Exposure Time"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-tag.c:100 libexif/olympus/mnote-olympus-tag.c:129
#: libexif/pentax/mnote-pentax-tag.c:81
#, fuzzy
msgid "Exposure Compensation"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-tag.c:101 libexif/canon/mnote-canon-tag.c:123
#: libexif/exif-tag.c:800 libexif/fuji/mnote-fuji-tag.c:40
#: libexif/olympus/mnote-olympus-tag.c:41
#: libexif/olympus/mnote-olympus-tag.c:98 libexif/pentax/mnote-pentax-tag.c:41
#: libexif/pentax/mnote-pentax-tag.c:84 libexif/pentax/mnote-pentax-tag.c:124
msgid "White Balance"
msgstr "Balance de blanco"

#: libexif/canon/mnote-canon-tag.c:102
#, fuzzy
msgid "Slow Shutter"
msgstr "obturador"

#: libexif/canon/mnote-canon-tag.c:103
#, fuzzy
msgid "Sequence Number"
msgstr "El número F."

#: libexif/canon/mnote-canon-tag.c:104
#, fuzzy
msgid "Flash Guide Number"
msgstr "El número F."

#: libexif/canon/mnote-canon-tag.c:106 libexif/olympus/mnote-olympus-tag.c:52
#: libexif/pentax/mnote-pentax-tag.c:109
#, fuzzy
msgid "Flash Exposure Compensation"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-tag.c:107
#, fuzzy
msgid "AE Bracketing"
msgstr "acción"

#: libexif/canon/mnote-canon-tag.c:108
#, fuzzy
msgid "AE Bracket Value"
msgstr "Auto bracket"

#: libexif/canon/mnote-canon-tag.c:109
#, fuzzy
msgid "Focus Distance Upper"
msgstr "Balance de blanco manual"

#: libexif/canon/mnote-canon-tag.c:110
#, fuzzy
msgid "Focus Distance Lower"
msgstr "Balance de blanco manual"

#: libexif/canon/mnote-canon-tag.c:111
msgid "FNumber"
msgstr "El número F."

#: libexif/canon/mnote-canon-tag.c:112 libexif/exif-tag.c:466
#: libexif/pentax/mnote-pentax-tag.c:78
msgid "Exposure Time"
msgstr "Tiempo de exposición"

#: libexif/canon/mnote-canon-tag.c:113
#, fuzzy
msgid "Bulb Duration"
msgstr "Saturación"

#: libexif/canon/mnote-canon-tag.c:114
msgid "Camera Type"
msgstr ""

#: libexif/canon/mnote-canon-tag.c:115
#, fuzzy
msgid "Auto Rotate"
msgstr "Auto bracket"

#: libexif/canon/mnote-canon-tag.c:116
#, fuzzy
msgid "ND Filter"
msgstr "Relación de zoom digital"

#: libexif/canon/mnote-canon-tag.c:119
#, fuzzy
msgid "Panorama Frame"
msgstr "Panorama"

#: libexif/canon/mnote-canon-tag.c:120
#, fuzzy
msgid "Panorama Direction"
msgstr "Panorama"

#: libexif/canon/mnote-canon-tag.c:121
#, fuzzy
msgid "Tone Curve"
msgstr "Contraste"

#: libexif/canon/mnote-canon-tag.c:122
#, fuzzy
msgid "Sharpness Frequency"
msgstr "Nitidez"

#: libexif/canon/mnote-canon-tag.c:124
#, fuzzy
msgid "Picture Style"
msgstr "Modo de exposición"

#: libexif/exif-byte-order.c:33
msgid "Motorola"
msgstr ""

#: libexif/exif-byte-order.c:35
msgid "Intel"
msgstr ""

#: libexif/exif-data.c:780
msgid "Size of data too small to allow for EXIF data."
msgstr ""

#: libexif/exif-data.c:841
msgid "EXIF marker not found."
msgstr ""

#: libexif/exif-data.c:868
msgid "EXIF header not found."
msgstr ""

#: libexif/exif-data.c:893
#, fuzzy
msgid "Unknown encoding."
msgstr "Desconocido"

#: libexif/exif-data.c:1178
msgid "Ignore unknown tags"
msgstr ""

#: libexif/exif-data.c:1179
msgid "Ignore unknown tags when loading EXIF data."
msgstr ""

#: libexif/exif-data.c:1180
msgid "Follow specification"
msgstr ""

#: libexif/exif-data.c:1181
msgid ""
"Add, correct and remove entries to get EXIF data that follows the "
"specification."
msgstr ""

#: libexif/exif-data.c:1183
msgid "Do not change maker note"
msgstr ""

#: libexif/exif-data.c:1184
msgid ""
"When loading and resaving Exif data, save the maker note unmodified. Be "
"aware that the maker note can get corrupted."
msgstr ""

#: libexif/exif-entry.c:234 libexif/exif-entry.c:303 libexif/exif-entry.c:336
#, c-format
msgid ""
"Tag '%s' was of format '%s' (which is against specification) and has been "
"changed to format '%s'."
msgstr ""

#: libexif/exif-entry.c:271
#, c-format
msgid ""
"Tag '%s' is of format '%s' (which is against specification) but cannot be "
"changed to format '%s'."
msgstr ""

#: libexif/exif-entry.c:354
#, c-format
msgid ""
"Tag 'UserComment' had invalid format '%s'. Format has been set to "
"'undefined'."
msgstr ""

#: libexif/exif-entry.c:381
msgid ""
"Tag 'UserComment' has been expanded to at least 8 bytes in order to follow "
"the specification."
msgstr ""

#: libexif/exif-entry.c:396
msgid ""
"Tag 'UserComment' is not empty but does not start with a format identifier. "
"This has been fixed."
msgstr ""

#: libexif/exif-entry.c:424
msgid ""
"Tag 'UserComment' did not start with a format identifier. This has been "
"fixed."
msgstr ""

#: libexif/exif-entry.c:462
#, fuzzy, c-format
msgid "%i bytes undefined data"
msgstr "%i bytes de datos desconocidos"

#: libexif/exif-entry.c:585
#, fuzzy, c-format
msgid "%i bytes unsupported data type"
msgstr "%i bytes de datos desconocidos"

#: libexif/exif-entry.c:642
#, fuzzy, c-format
msgid "The tag '%s' contains data of an invalid format ('%s', expected '%s')."
msgstr "Formato no válido '%s', se esperaba '%s'."

#: libexif/exif-entry.c:655
#, fuzzy, c-format
msgid ""
"The tag '%s' contains an invalid number of components (%i, expected %i)."
msgstr "Cantidad de componentes no válida (%i, se esperaba %i)."

#: libexif/exif-entry.c:669
#, fuzzy
msgid "Chunky format"
msgstr "formato por trozos"

#: libexif/exif-entry.c:669
#, fuzzy
msgid "Planar format"
msgstr "formato planar"

#: libexif/exif-entry.c:671 libexif/exif-entry.c:763
#: test/nls/test-codeset.c:54
msgid "Not defined"
msgstr "No definido"

#: libexif/exif-entry.c:671
msgid "One-chip color area sensor"
msgstr "Sensor de área de color de un chip"

#: libexif/exif-entry.c:672
msgid "Two-chip color area sensor"
msgstr "Sensor de área de color de dos chips"

#: libexif/exif-entry.c:672
msgid "Three-chip color area sensor"
msgstr "Sensor de área de color de tres chips"

#: libexif/exif-entry.c:673
msgid "Color sequential area sensor"
msgstr "Sensor de área de color secuencial"

#: libexif/exif-entry.c:673
msgid "Trilinear sensor"
msgstr "Sensor tri-lineal"

#: libexif/exif-entry.c:674
msgid "Color sequential linear sensor"
msgstr "Sensor lineal secuencial de color"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:233
#, fuzzy
msgid "Top-left"
msgstr "arriba - izquierda"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:235
#, fuzzy
msgid "Top-right"
msgstr "arriba - derecha"

#: libexif/exif-entry.c:676 libexif/pentax/mnote-pentax-entry.c:241
#, fuzzy
msgid "Bottom-right"
msgstr "abajo - derecha"

#: libexif/exif-entry.c:677 libexif/pentax/mnote-pentax-entry.c:239
#, fuzzy
msgid "Bottom-left"
msgstr "abajo - izquierda"

#: libexif/exif-entry.c:677
msgid "Left-top"
msgstr ""

#: libexif/exif-entry.c:677
#, fuzzy
msgid "Right-top"
msgstr "derecha - arriba"

#: libexif/exif-entry.c:678
#, fuzzy
msgid "Right-bottom"
msgstr "derecha - abajo"

#: libexif/exif-entry.c:678
#, fuzzy
msgid "Left-bottom"
msgstr "izquierda - abajo"

#: libexif/exif-entry.c:680
#, fuzzy
msgid "Centered"
msgstr "centrado"

#: libexif/exif-entry.c:680
#, fuzzy
msgid "Co-sited"
msgstr "co-situado"

#: libexif/exif-entry.c:682
msgid "Reversed mono"
msgstr ""

#: libexif/exif-entry.c:682
#, fuzzy
msgid "Normal mono"
msgstr "Normal"

#: libexif/exif-entry.c:682
msgid "RGB"
msgstr "RVA"

#: libexif/exif-entry.c:682
#, fuzzy
msgid "Palette"
msgstr "Patrón"

#: libexif/exif-entry.c:683
msgid "CMYK"
msgstr ""

#: libexif/exif-entry.c:683
msgid "YCbCr"
msgstr "YCbCr"

#: libexif/exif-entry.c:683
msgid "CieLAB"
msgstr ""

#: libexif/exif-entry.c:685
msgid "Normal process"
msgstr "Proceso normal"

#: libexif/exif-entry.c:685
msgid "Custom process"
msgstr "Proceso personalizado"

#: libexif/exif-entry.c:687
msgid "Auto exposure"
msgstr "Exposición automática"

#: libexif/exif-entry.c:687 libexif/fuji/mnote-fuji-entry.c:139
msgid "Manual exposure"
msgstr "Exposición manual"

#: libexif/exif-entry.c:687
msgid "Auto bracket"
msgstr "Auto bracket"

#: libexif/exif-entry.c:689
msgid "Auto white balance"
msgstr "Balance de blanco automático"

#: libexif/exif-entry.c:689
msgid "Manual white balance"
msgstr "Balance de blanco manual"

#: libexif/exif-entry.c:694
msgid "Low gain up"
msgstr "Ganancia baja alta"

#: libexif/exif-entry.c:694
msgid "High gain up"
msgstr "Ganancia alta alta"

#: libexif/exif-entry.c:695
msgid "Low gain down"
msgstr "Ganancia baja baja"

#: libexif/exif-entry.c:695
msgid "High gain down"
msgstr "Ganancia alta baja"

#: libexif/exif-entry.c:697
msgid "Low saturation"
msgstr "Baja saturación"

#: libexif/exif-entry.c:697 test/nls/test-codeset.c:48
#: test/nls/test-codeset.c:61
msgid "High saturation"
msgstr "Alta saturación"

#: libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/fuji/mnote-fuji-entry.c:63
#: libexif/olympus/mnote-olympus-entry.c:208
#: libexif/olympus/mnote-olympus-entry.c:217
#: libexif/pentax/mnote-pentax-entry.c:106
#: libexif/pentax/mnote-pentax-entry.c:170
msgid "Soft"
msgstr "Suave"

#: libexif/exif-entry.c:698 libexif/exif-entry.c:699
#: libexif/fuji/mnote-fuji-entry.c:65 libexif/fuji/mnote-fuji-entry.c:95
#: libexif/olympus/mnote-olympus-entry.c:207
#: libexif/olympus/mnote-olympus-entry.c:215
#: libexif/pentax/mnote-pentax-entry.c:107
msgid "Hard"
msgstr "Duro"

#: libexif/exif-entry.c:715 libexif/exif-entry.c:733 libexif/exif-entry.c:815
#: libexif/olympus/mnote-olympus-entry.c:595
#: libexif/olympus/mnote-olympus-entry.c:689
#: libexif/olympus/mnote-olympus-entry.c:744
#: libexif/pentax/mnote-pentax-entry.c:256
msgid "Unknown"
msgstr "Desconocido"

#: libexif/exif-entry.c:716
#, fuzzy
msgid "Avg"
msgstr "promedio"

#: libexif/exif-entry.c:717
#, fuzzy
msgid "Center-weight"
msgstr "peso centrado"

#: libexif/exif-entry.c:719
#, fuzzy
msgid "Multi spot"
msgstr "Multi Lugar"

#: libexif/exif-entry.c:720
msgid "Pattern"
msgstr "Patrón"

#: libexif/exif-entry.c:725
msgid "Uncompressed"
msgstr "Descomprimido"

#: libexif/exif-entry.c:726
msgid "LZW compression"
msgstr "compresión LZW"

#: libexif/exif-entry.c:727 libexif/exif-entry.c:728
msgid "JPEG compression"
msgstr "compresión JPEG"

#: libexif/exif-entry.c:729
msgid "Deflate/ZIP compression"
msgstr "compresión Deflate/ZIP"

#: libexif/exif-entry.c:730
msgid "PackBits compression"
msgstr "compresión PackBits"

#: libexif/exif-entry.c:736
#, fuzzy
msgid "Tungsten incandescent light"
msgstr "Tungsteno (luz incandescente)"

#: libexif/exif-entry.c:738
msgid "Fine weather"
msgstr "Tiempo bueno"

#: libexif/exif-entry.c:739
msgid "Cloudy weather"
msgstr "Tiempo nublado"

#: libexif/exif-entry.c:742 libexif/fuji/mnote-fuji-entry.c:77
#: libexif/pentax/mnote-pentax-entry.c:252
msgid "Day white fluorescent"
msgstr "Día blanco fluorescente"

#: libexif/exif-entry.c:743
msgid "Cool white fluorescent"
msgstr "Blanco frío fluorescente"

#: libexif/exif-entry.c:744 libexif/fuji/mnote-fuji-entry.c:78
#: libexif/pentax/mnote-pentax-entry.c:253
msgid "White fluorescent"
msgstr "Blanco fluorescente"

#: libexif/exif-entry.c:745
msgid "Standard light A"
msgstr "Luz estándar A"

#: libexif/exif-entry.c:746
msgid "Standard light B"
msgstr "Luz estándar B"

#: libexif/exif-entry.c:747
msgid "Standard light C"
msgstr "Luz estándar C"

#: libexif/exif-entry.c:748
msgid "D55"
msgstr "D55"

#: libexif/exif-entry.c:749
msgid "D65"
msgstr "D65"

#: libexif/exif-entry.c:750
msgid "D75"
msgstr "D75"

#: libexif/exif-entry.c:751
msgid "ISO studio tungsten"
msgstr "tungsteno de estudio ISO"

#: libexif/exif-entry.c:755 libexif/exif-entry.c:759
msgid "Inch"
msgstr "Pulgada"

#: libexif/exif-entry.c:755 libexif/exif-entry.c:759
msgid "in"
msgstr "pulg"

#: libexif/exif-entry.c:756 libexif/exif-entry.c:760
msgid "Centimeter"
msgstr "Centímetro"

#: libexif/exif-entry.c:756 libexif/exif-entry.c:760
msgid "cm"
msgstr "cm"

#: libexif/exif-entry.c:765
msgid "Normal program"
msgstr "Programa normal"

#: libexif/exif-entry.c:766
msgid "Aperture priority"
msgstr "Prioridad de apertura"

#: libexif/exif-entry.c:766 libexif/exif-tag.c:550
msgid "Aperture"
msgstr "Apertura"

#: libexif/exif-entry.c:767
msgid "Shutter priority"
msgstr "Prioridad del obturador"

#: libexif/exif-entry.c:767
#, fuzzy
msgid "Shutter"
msgstr "obturador"

#: libexif/exif-entry.c:768
msgid "Creative program (biased toward depth of field)"
msgstr "Programa creativo (orientado a la profundidad del campo)"

#: libexif/exif-entry.c:769
#, fuzzy
msgid "Creative"
msgstr "creativo"

#: libexif/exif-entry.c:770
#, fuzzy
msgid "Creative program (biased toward fast shutter speed)"
msgstr "Programa de acción (orientado a velocidad rápida del obturador)"

#: libexif/exif-entry.c:771
#, fuzzy
msgid "Action"
msgstr "acción"

#: libexif/exif-entry.c:772
#, fuzzy
msgid "Portrait mode (for closeup photos with the background out of focus)"
msgstr "Modo retrato (para fotos de cerca con el fondo fuera de foco"

#: libexif/exif-entry.c:774
#, fuzzy
msgid "Landscape mode (for landscape photos with the background in focus)"
msgstr "Modo paisaje (para fotos de paisaje con el fondo en foco"

#: libexif/exif-entry.c:778 libexif/exif-entry.c:783
#: libexif/olympus/mnote-olympus-entry.c:100
#, fuzzy
msgid "Flash did not fire"
msgstr "El flash no disparó."

#: libexif/exif-entry.c:778
#, fuzzy
msgid "No flash"
msgstr "Flash"

#: libexif/exif-entry.c:779
#, fuzzy
msgid "Flash fired"
msgstr "El flash disparó."

#: libexif/exif-entry.c:779 libexif/olympus/mnote-olympus-entry.c:173
#: libexif/olympus/mnote-olympus-entry.c:178
#: libexif/olympus/mnote-olympus-entry.c:212
#: libexif/olympus/mnote-olympus-entry.c:221
#: libexif/olympus/mnote-olympus-entry.c:244
msgid "Yes"
msgstr "sí"

#: libexif/exif-entry.c:780
#, fuzzy
msgid "Strobe return light not detected"
msgstr "Luz de retorno estrosboscópica no detectada."

#: libexif/exif-entry.c:780
#, fuzzy
msgid "Without strobe"
msgstr "sin estrosboscópica"

#: libexif/exif-entry.c:782
#, fuzzy
msgid "Strobe return light detected"
msgstr "Luz de retorno estrosboscópica detectada."

#: libexif/exif-entry.c:782
#, fuzzy
msgid "With strobe"
msgstr "con estrosboscópica"

#: libexif/exif-entry.c:784
#, fuzzy
msgid "Flash fired, compulsory flash mode"
msgstr "El flash no disparó, modo compulsivo del flash."

#: libexif/exif-entry.c:785
#, fuzzy
msgid "Flash fired, compulsory flash mode, return light not detected"
msgstr ""
"El flash disparó, modo compulsivo del flash, luz de retorno no detectada."

#: libexif/exif-entry.c:787
#, fuzzy
msgid "Flash fired, compulsory flash mode, return light detected"
msgstr "El flash disparó, modo compulsivo del flash, luz de retorno detectada."

#: libexif/exif-entry.c:789
#, fuzzy
msgid "Flash did not fire, compulsory flash mode"
msgstr "El flash no disparó, modo compulsivo del flash."

#: libexif/exif-entry.c:790
#, fuzzy
msgid "Flash did not fire, auto mode"
msgstr "El flash no disparó, modo automático."

#: libexif/exif-entry.c:791
#, fuzzy
msgid "Flash fired, auto mode"
msgstr "El flash disparó, modo automático."

#: libexif/exif-entry.c:792
#, fuzzy
msgid "Flash fired, auto mode, return light not detected"
msgstr "El flash disparó, modo automático, luz de retorno no detectada."

#: libexif/exif-entry.c:794
#, fuzzy
msgid "Flash fired, auto mode, return light detected"
msgstr "El flash disparó, modo automático, luz de retorno detectada."

#: libexif/exif-entry.c:795
#, fuzzy
msgid "No flash function"
msgstr "Sin función de flash."

#: libexif/exif-entry.c:796
#, fuzzy
msgid "Flash fired, red-eye reduction mode"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/exif-entry.c:797
#, fuzzy
msgid "Flash fired, red-eye reduction mode, return light not detected"
msgstr ""
"El flash disparó, modo de reducción de ojos rojos, luz de retorno no "
"detectada."

#: libexif/exif-entry.c:799
#, fuzzy
msgid "Flash fired, red-eye reduction mode, return light detected"
msgstr ""
"El flash disparó, modo de reducción de ojos rojos, luz de retorno detectada."

#: libexif/exif-entry.c:801
#, fuzzy
msgid "Flash fired, compulsory flash mode, red-eye reduction mode"
msgstr ""
"El flash disparó, modo compulsivo del flash, modo de reducción de ojos rojos."

#: libexif/exif-entry.c:803
#, fuzzy
msgid ""
"Flash fired, compulsory flash mode, red-eye reduction mode, return light not "
"detected"
msgstr ""
"El flash disparó, modo compulsivo del flash, modo de reducción de ojos "
"rojos, luz de retorno no detectada."

#: libexif/exif-entry.c:805
#, fuzzy
msgid ""
"Flash fired, compulsory flash mode, red-eye reduction mode, return light "
"detected"
msgstr ""
"El flash disparó, modo compulsivo del flash, modo de reducción de ojos "
"rojos, luz de retorno no detectada."

#: libexif/exif-entry.c:807
#, fuzzy
msgid "Flash did not fire, auto mode, red-eye reduction mode"
msgstr "El flash disparó, modo automático, modo de reducción de ojos rojos."

#: libexif/exif-entry.c:808
#, fuzzy
msgid "Flash fired, auto mode, red-eye reduction mode"
msgstr "El flash disparó, modo automático, modo de reducción de ojos rojos."

#: libexif/exif-entry.c:809
#, fuzzy
msgid ""
"Flash fired, auto mode, return light not detected, red-eye reduction mode"
msgstr ""
"El flash disparó, modo automático, modo de reducción de ojos rojos, luz de "
"retorno no detectada."

#: libexif/exif-entry.c:811
#, fuzzy
msgid "Flash fired, auto mode, return light detected, red-eye reduction mode"
msgstr ""
"El flash disparó, modo automático, modo de reducción de ojos rojos, luz de "
"retorno detectada."

#: libexif/exif-entry.c:815
msgid "?"
msgstr ""

#: libexif/exif-entry.c:817
msgid "Close view"
msgstr "Vista cercana"

#: libexif/exif-entry.c:818
msgid "Distant view"
msgstr "Vista distante"

#: libexif/exif-entry.c:818
#, fuzzy
msgid "Distant"
msgstr "Vista distante"

#: libexif/exif-entry.c:821
msgid "sRGB"
msgstr "sRVA"

#: libexif/exif-entry.c:822
msgid "Adobe RGB"
msgstr ""

#: libexif/exif-entry.c:823
msgid "Uncalibrated"
msgstr "Descalibrado"

#: libexif/exif-entry.c:878
#, c-format
msgid "Invalid size of entry (%i, expected %li x %i)."
msgstr "Cantidad de componentes no válida (%i, se esperaba %li x %i)."

#: libexif/exif-entry.c:911
msgid "Unsupported UNICODE string"
msgstr ""

#: libexif/exif-entry.c:919
msgid "Unsupported JIS string"
msgstr ""

#: libexif/exif-entry.c:935
msgid "Tag UserComment contains data but is against specification."
msgstr ""

#: libexif/exif-entry.c:939
#, c-format
msgid "Byte at position %i: 0x%02x"
msgstr ""

#: libexif/exif-entry.c:947
#, fuzzy
msgid "Unknown Exif Version"
msgstr "Versión Exif"

#: libexif/exif-entry.c:951
#, c-format
msgid "Exif Version %d.%d"
msgstr "Versión Exif %d.%d"

#: libexif/exif-entry.c:962
msgid "FlashPix Version 1.0"
msgstr ""

#: libexif/exif-entry.c:964
msgid "FlashPix Version 1.01"
msgstr ""

#: libexif/exif-entry.c:966
msgid "Unknown FlashPix Version"
msgstr ""

#: libexif/exif-entry.c:979 libexif/exif-entry.c:998 libexif/exif-entry.c:1666
#: libexif/exif-entry.c:1671 libexif/exif-entry.c:1675
#: libexif/exif-entry.c:1680 libexif/exif-entry.c:1681
msgid "[None]"
msgstr ""

#: libexif/exif-entry.c:981
msgid "(Photographer)"
msgstr ""

#: libexif/exif-entry.c:1000
msgid "(Editor)"
msgstr ""

#: libexif/exif-entry.c:1024 libexif/exif-entry.c:1104
#: libexif/exif-entry.c:1121 libexif/exif-entry.c:1165
#, c-format
msgid "%.02f EV"
msgstr ""

#: libexif/exif-entry.c:1025
#, c-format
msgid " (f/%.01f)"
msgstr ""

#: libexif/exif-entry.c:1059
#, c-format
msgid " (35 equivalent: %d mm)"
msgstr " (equivalente 35: %d mm)"

#: libexif/exif-entry.c:1092 libexif/exif-entry.c:1093
msgid " sec."
msgstr " seg."

#: libexif/exif-entry.c:1107
#, fuzzy, c-format
msgid " (1/%d sec.)"
msgstr " 1/%d seg.)"

#: libexif/exif-entry.c:1109
#, fuzzy, c-format
msgid " (%d sec.)"
msgstr " %d seg.)"

#: libexif/exif-entry.c:1122
#, c-format
msgid " (%.02f cd/m^2)"
msgstr ""

#: libexif/exif-entry.c:1132
msgid "DSC"
msgstr "DSC"

#: libexif/exif-entry.c:1134 libexif/exif-entry.c:1174
#: libexif/exif-entry.c:1261 libexif/exif-entry.c:1312
#: libexif/exif-entry.c:1321 libexif/exif-entry.c:1357
#: libexif/fuji/mnote-fuji-entry.c:236 libexif/fuji/mnote-fuji-entry.c:245
#: libexif/pentax/mnote-pentax-entry.c:350
#: libexif/pentax/mnote-pentax-entry.c:359
#, c-format
msgid "Internal error (unknown value %i)"
msgstr ""

#: libexif/exif-entry.c:1142
msgid "-"
msgstr "-"

#: libexif/exif-entry.c:1143
msgid "Y"
msgstr "Y"

#: libexif/exif-entry.c:1144
msgid "Cb"
msgstr "Cb"

#: libexif/exif-entry.c:1145
msgid "Cr"
msgstr "Cr"

#: libexif/exif-entry.c:1146
msgid "R"
msgstr "R"

#: libexif/exif-entry.c:1147
msgid "G"
msgstr "V"

#: libexif/exif-entry.c:1148
msgid "B"
msgstr "A"

#: libexif/exif-entry.c:1149
#, fuzzy
msgid "Reserved"
msgstr "reservado"

#: libexif/exif-entry.c:1172
msgid "Directly photographed"
msgstr ""

#: libexif/exif-entry.c:1185
msgid "YCbCr4:2:2"
msgstr "YCbCr4:2:2"

#: libexif/exif-entry.c:1187
msgid "YCbCr4:2:0"
msgstr "YCbCr4:2:0"

#: libexif/exif-entry.c:1204
#, c-format
msgid "Within distance %i of (x,y) = (%i,%i)"
msgstr "Dentro de la distancia %i de (x,y) = (%i,%i)"

#: libexif/exif-entry.c:1213
#, c-format
msgid "Within rectangle (width %i, height %i) around (x,y) = (%i,%i)"
msgstr "Dentro del rectángulo (ancho %i, alto %i) alrededor de (x,y) = (%i,%i)"

#: libexif/exif-entry.c:1219
#, c-format
msgid "Unexpected number of components (%li, expected 2, 3, or 4)."
msgstr "Cantidad de componentes inesperada (%li, esperados 2, 3, o 4)."

#: libexif/exif-entry.c:1257
#, fuzzy
msgid "Sea level"
msgstr "Nivel de la batería"

#: libexif/exif-entry.c:1259
msgid "Sea level reference"
msgstr ""

#: libexif/exif-entry.c:1367
#, fuzzy, c-format
msgid "Unknown value %i"
msgstr "Desconocido"

#: libexif/exif-format.c:37
#, fuzzy
msgid "Short"
msgstr "Lugar"

#: libexif/exif-format.c:38
msgid "Rational"
msgstr ""

#: libexif/exif-format.c:39
msgid "SRational"
msgstr ""

#: libexif/exif-format.c:40
msgid "Undefined"
msgstr "Indefinido"

#: libexif/exif-format.c:41
msgid "ASCII"
msgstr ""

#: libexif/exif-format.c:42
msgid "Long"
msgstr ""

#: libexif/exif-format.c:43
#, fuzzy
msgid "Byte"
msgstr "Centímetro"

#: libexif/exif-format.c:44
msgid "SByte"
msgstr ""

#: libexif/exif-format.c:45
msgid "SShort"
msgstr ""

#: libexif/exif-format.c:46
msgid "SLong"
msgstr ""

#: libexif/exif-format.c:47
msgid "Float"
msgstr ""

#: libexif/exif-format.c:48
msgid "Double"
msgstr ""

#: libexif/exif-loader.c:119
#, c-format
msgid "The file '%s' could not be opened."
msgstr ""

#: libexif/exif-loader.c:300
msgid "The data supplied does not seem to contain EXIF data."
msgstr ""

#: libexif/exif-log.c:43
msgid "Debugging information"
msgstr ""

#: libexif/exif-log.c:44
msgid "Debugging information is available."
msgstr ""

#: libexif/exif-log.c:45
msgid "Not enough memory"
msgstr ""

#: libexif/exif-log.c:46
msgid "The system cannot provide enough memory."
msgstr ""

#: libexif/exif-log.c:47
msgid "Corrupt data"
msgstr ""

#: libexif/exif-log.c:48
msgid "The data provided does not follow the specification."
msgstr ""

#: libexif/exif-tag.c:62
msgid "GPS Tag Version"
msgstr ""

#: libexif/exif-tag.c:63
#, fuzzy
msgid ""
"Indicates the version of <GPSInfoIFD>. The version is given as 2.0.0.0. This "
"tag is mandatory when <GPSInfo> tag is present. (Note: The <GPSVersionID> "
"tag is given in bytes, unlike the <ExifVersion> tag. When the version is "
"2.0.0.0, the tag value is 02000000.H)."
msgstr ""
"Indica la versión de <GPSInfoIFD>. La versión está dada como 2.0.0.0. Esta "
"etiqueta es obligatoria cuando está presente la etiqueta <GPSInfo>. (Nota: "
"La etiqueta <GPSVersion ID> está dada en bytes, a diferencia de la etiqueta "
"<ExifVersion>. Cuando la versión es 2.0.0.0, el valor de la etiqueta es "
"02000000.H)."

#: libexif/exif-tag.c:69
msgid "Interoperability Index"
msgstr ""

#: libexif/exif-tag.c:70
msgid ""
"Indicates the identification of the Interoperability rule. Use \"R98\" for "
"stating ExifR98 Rules. Four bytes used including the termination code "
"(NULL). see the separate volume of Recommended Exif Interoperability Rules "
"(ExifR98) for other tags used for ExifR98."
msgstr ""
"Indica la identificación de la regla de inter-operatibilidad. Utilice "
"\"R98\" para indicar reglas ExifR98. Se utilizan cuatro bytes incluyendo el "
"código de terminación (NULL). Vea el volumen separado de Recommended Exif "
"Interoperatibility Rules (ExifR98) para otras etiquetas usadas en ExifR98."

#: libexif/exif-tag.c:76
msgid "North or South Latitude"
msgstr ""

#: libexif/exif-tag.c:77
msgid ""
"Indicates whether the latitude is north or south latitude. The ASCII value "
"'N' indicates north latitude, and 'S' is south latitude."
msgstr ""
"Indica si la latitud es Norte o Sur. El valor ASCII 'N' indica latitud "
"Norte, y 'S' indica latitud Sur."

#: libexif/exif-tag.c:81
msgid "Interoperability Version"
msgstr ""

#: libexif/exif-tag.c:83
msgid "Latitude"
msgstr ""

#: libexif/exif-tag.c:84
#, fuzzy
msgid ""
"Indicates the latitude. The latitude is expressed as three RATIONAL values "
"giving the degrees, minutes, and seconds, respectively. When degrees, "
"minutes and seconds are expressed, the format is dd/1,mm/1,ss/1. When "
"degrees and minutes are used and, for example, fractions of minutes are "
"given up to two decimal places, the format is dd/1,mmmm/100,0/1."
msgstr ""
"Indica la latitud. La latitud se expresa como tres valores RACIONALES dando "
"los grados, minutos y segundos, respectivamente. Cuando se expresan grados, "
"minutos y segundos, el formato es gg/1,mm/1,ss/1. Cuando se utilizan grados "
"y minutos y, por ejemplo, se dan fracciones de minutos hasta con dos "
"posiciones decimales, el formato es gg/1,mmmm/100,0/1."

#: libexif/exif-tag.c:91
msgid "East or West Longitude"
msgstr ""

#: libexif/exif-tag.c:92
msgid ""
"Indicates whether the longitude is east or west longitude. ASCII 'E' "
"indicates east longitude, and 'W' is west longitude."
msgstr ""
"Indica si la longitud es Este u Oeste. El valor ASCII 'E' indica longitud "
"Este, y 'W' indica longitud Oeste."

#: libexif/exif-tag.c:95
msgid "Longitude"
msgstr ""

#: libexif/exif-tag.c:96
msgid ""
"Indicates the longitude. The longitude is expressed as three RATIONAL values "
"giving the degrees, minutes, and seconds, respectively. When degrees, "
"minutes and seconds are expressed, the format is ddd/1,mm/1,ss/1. When "
"degrees and minutes are used and, for example, fractions of minutes are "
"given up to two decimal places, the format is ddd/1,mmmm/100,0/1."
msgstr ""
"Indica la longitud. La longitud se expresa como tres valores RACIONALES "
"dando los grados, minutos y segundos, respectivamente. Cuando se expresan "
"grados, minutos y segundos, el formato es gg/1,mm/1,ss/1. Cuando se utilizan "
"grados y minutos y, por ejemplo, se dan fracciones de minutos hasta con dos "
"posiciones decimales, el formato es gg/1,mmmm/100,0/1."

#: libexif/exif-tag.c:103
msgid "Altitude Reference"
msgstr ""

#: libexif/exif-tag.c:104
msgid ""
"Indicates the altitude used as the reference altitude. If the reference is "
"sea level and the altitude is above sea level, 0 is given. If the altitude "
"is below sea level, a value of 1 is given and the altitude is indicated as "
"an absolute value in the GSPAltitude tag. The reference unit is meters. Note "
"that this tag is BYTE type, unlike other reference tags."
msgstr ""

#: libexif/exif-tag.c:110
msgid "Altitude"
msgstr ""

#: libexif/exif-tag.c:111
msgid ""
"Indicates the altitude based on the reference in GPSAltitudeRef. Altitude is "
"expressed as one RATIONAL value. The reference unit is meters."
msgstr ""

#: libexif/exif-tag.c:114
msgid "GPS Time (Atomic Clock)"
msgstr ""

#: libexif/exif-tag.c:115
msgid ""
"Indicates the time as UTC (Coordinated Universal Time). TimeStamp is "
"expressed as three RATIONAL values giving the hour, minute, and second."
msgstr ""

#: libexif/exif-tag.c:118
msgid "GPS Satellites"
msgstr ""

#: libexif/exif-tag.c:119
msgid ""
"Indicates the GPS satellites used for measurements. This tag can be used to "
"describe the number of satellites, their ID number, angle of elevation, "
"azimuth, SNR and other information in ASCII notation. The format is not "
"specified. If the GPS receiver is incapable of taking measurements, value of "
"the tag shall be set to NULL."
msgstr ""

#: libexif/exif-tag.c:125
msgid "GPS Receiver Status"
msgstr ""

#: libexif/exif-tag.c:126
msgid ""
"Indicates the status of the GPS receiver when the image is recorded. 'A' "
"means measurement is in progress, and 'V' means the measurement is "
"Interoperability."
msgstr ""

#: libexif/exif-tag.c:129
#, fuzzy
msgid "GPS Measurement Mode"
msgstr "Modo de métrica"

#: libexif/exif-tag.c:130
msgid ""
"Indicates the GPS measurement mode. '2' means two-dimensional measurement "
"and '3' means three-dimensional measurement is in progress."
msgstr ""

#: libexif/exif-tag.c:133
msgid "Measurement Precision"
msgstr ""

#: libexif/exif-tag.c:134
msgid ""
"Indicates the GPS DOP (data degree of precision). An HDOP value is written "
"during two-dimensional measurement, and PDOP during three-dimensional "
"measurement."
msgstr ""

#: libexif/exif-tag.c:137
msgid "Speed Unit"
msgstr ""

#: libexif/exif-tag.c:138
msgid ""
"Indicates the unit used to express the GPS receiver speed of movement. 'K', "
"'M' and 'N' represent kilometers per hour, miles per hour, and knots."
msgstr ""

#: libexif/exif-tag.c:141
msgid "Speed of GPS Receiver"
msgstr ""

#: libexif/exif-tag.c:142
msgid "Indicates the speed of GPS receiver movement."
msgstr ""

#: libexif/exif-tag.c:143
msgid "Reference for direction of movement"
msgstr ""

#: libexif/exif-tag.c:144
msgid ""
"Indicates the reference for giving the direction of GPS receiver movement. "
"'T' denotes true direction and 'M' is magnetic direction."
msgstr ""

#: libexif/exif-tag.c:147
msgid "Direction of Movement"
msgstr ""

#: libexif/exif-tag.c:148
msgid ""
"Indicates the direction of GPS receiver movement. The range of values is "
"from 0.00 to 359.99."
msgstr ""

#: libexif/exif-tag.c:150
msgid "GPS Image Direction Reference"
msgstr ""

#: libexif/exif-tag.c:151
msgid ""
"Indicates the reference for giving the direction of the image when it is "
"captured. 'T' denotes true direction and 'M' is magnetic direction."
msgstr ""

#: libexif/exif-tag.c:153
#, fuzzy
msgid "GPS Image Direction"
msgstr "Descripción de la imagen"

#: libexif/exif-tag.c:154
msgid ""
"Indicates the direction of the image when it was captured. The range of "
"values is from 0.00 to 359.99."
msgstr ""

#: libexif/exif-tag.c:156
msgid "Geodetic Survey Data Used"
msgstr ""

#: libexif/exif-tag.c:157
msgid ""
"Indicates the geodetic survey data used by the GPS receiver. If the survey "
"data is restricted to Japan, the value of this tag is 'TOKYO' or 'WGS-84'. "
"If a GPS Info tag is recorded, it is strongly recommended that this tag be "
"recorded."
msgstr ""

#: libexif/exif-tag.c:161
msgid "Reference For Latitude of Destination"
msgstr ""

#: libexif/exif-tag.c:162
#, fuzzy
msgid ""
"Indicates whether the latitude of the destination point is north or south "
"latitude. The ASCII value 'N' indicates north latitude, and 'S' is south "
"latitude."
msgstr ""
"Indica si la latitud es Norte o Sur. El valor ASCII 'N' indica latitud "
"Norte, y 'S' indica latitud Sur."

#: libexif/exif-tag.c:165
msgid "Latitude of Destination"
msgstr ""

#: libexif/exif-tag.c:166
#, fuzzy
msgid ""
"Indicates the latitude of the destination point. The latitude is expressed "
"as three RATIONAL values giving the degrees, minutes, and seconds, "
"respectively. If latitude is expressed as degrees, minutes and seconds, a "
"typical format would be dd/1,mm/1,ss/1. When degrees and minutes are used "
"and, for example, fractions of minutes are given up to two decimal places, "
"the format would be dd/1,mmmm/100,0/1."
msgstr ""
"Indica la latitud. La latitud se expresa como tres valores RACIONALES dando "
"los grados, minutos y segundos, respectivamente. Cuando se expresan grados, "
"minutos y segundos, el formato es gg/1,mm/1,ss/1. Cuando se utilizan grados "
"y minutos y, por ejemplo, se dan fracciones de minutos hasta con dos "
"posiciones decimales, el formato es gg/1,mmmm/100,0/1."

#: libexif/exif-tag.c:173
msgid "Reference for Longitude of Destination"
msgstr ""

#: libexif/exif-tag.c:174
#, fuzzy
msgid ""
"Indicates whether the longitude of the destination point is east or west "
"longitude. ASCII 'E' indicates east longitude, and 'W' is west longitude."
msgstr ""
"Indica si la longitud es Este u Oeste. El valor ASCII 'E' indica longitud "
"Este, y 'W' indica longitud Oeste."

#: libexif/exif-tag.c:177
msgid "Longitude of Destination"
msgstr ""

#: libexif/exif-tag.c:178
#, fuzzy
msgid ""
"Indicates the longitude of the destination point. The longitude is expressed "
"as three RATIONAL values giving the degrees, minutes, and seconds, "
"respectively. If longitude is expressed as degrees, minutes and seconds, a "
"typical format would be ddd/1,mm/1,ss/1. When degrees and minutes are used "
"and, for example, fractions of minutes are given up to two decimal places, "
"the format would be ddd/1,mmmm/100,0/1."
msgstr ""
"Indica la longitud. La longitud se expresa como tres valores RACIONALES "
"dando los grados, minutos y segundos, respectivamente. Cuando se expresan "
"grados, minutos y segundos, el formato es gg/1,mm/1,ss/1. Cuando se utilizan "
"grados y minutos y, por ejemplo, se dan fracciones de minutos hasta con dos "
"posiciones decimales, el formato es gg/1,mmmm/100,0/1."

#: libexif/exif-tag.c:186
msgid "Reference for Bearing of Destination"
msgstr ""

#: libexif/exif-tag.c:187
msgid ""
"Indicates the reference used for giving the bearing to the destination "
"point. 'T' denotes true direction and 'M' is magnetic direction."
msgstr ""

#: libexif/exif-tag.c:190
msgid "Bearing of Destination"
msgstr ""

#: libexif/exif-tag.c:191
msgid ""
"Indicates the bearing to the destination point. The range of values is from "
"0.00 to 359.99."
msgstr ""

#: libexif/exif-tag.c:193
msgid "Reference for Distance to Destination"
msgstr ""

#: libexif/exif-tag.c:194
msgid ""
"Indicates the unit used to express the distance to the destination point. "
"'K', 'M' and 'N' represent kilometers, miles and nautical miles."
msgstr ""

#: libexif/exif-tag.c:197
#, fuzzy
msgid "Distance to Destination"
msgstr "Descripción de ajuste del dispositivo"

#: libexif/exif-tag.c:198
#, fuzzy
msgid "Indicates the distance to the destination point."
msgstr "Esta etiqueta indica la distancia al sujeto."

#: libexif/exif-tag.c:199
#, fuzzy
msgid "Name of GPS Processing Method"
msgstr "Método de sensado"

#: libexif/exif-tag.c:200
msgid ""
"A character string recording the name of the method used for location "
"finding. The first byte indicates the character code used, and this is "
"followed by the name of the method. Since the Type is not ASCII, NULL "
"termination is not necessary."
msgstr ""

#: libexif/exif-tag.c:205
msgid "Name of GPS Area"
msgstr ""

#: libexif/exif-tag.c:206
msgid ""
"A character string recording the name of the GPS area. The first byte "
"indicates the character code used, and this is followed by the name of the "
"GPS area. Since the Type is not ASCII, NULL termination is not necessary."
msgstr ""

#: libexif/exif-tag.c:210
msgid "GPS Date"
msgstr ""

#: libexif/exif-tag.c:211
msgid ""
"A character string recording date and time information relative to UTC "
"(Coordinated Universal Time). The format is \"YYYY:MM:DD\". The length of "
"the string is 11 bytes including NULL."
msgstr ""

#: libexif/exif-tag.c:215
msgid "GPS Differential Correction"
msgstr ""

#: libexif/exif-tag.c:216
msgid ""
"Indicates whether differential correction is applied to the GPS receiver."
msgstr ""

#: libexif/exif-tag.c:220
msgid "New Subfile Type"
msgstr ""

#: libexif/exif-tag.c:220
msgid "A general indication of the kind of data contained in this subfile."
msgstr ""

#: libexif/exif-tag.c:222
msgid "Image Width"
msgstr "Ancho de la imagen"

#: libexif/exif-tag.c:223
#, fuzzy
msgid ""
"The number of columns of image data, equal to the number of pixels per row. "
"In JPEG compressed data a JPEG marker is used instead of this tag."
msgstr ""
"La cantidad de columnas de los datos de la imagen, igual a la cantidad de "
"pixels por fila. En datos comprimidos JPEG se utiliza un marcador JPEG en "
"vez de esta etiqueta."

#: libexif/exif-tag.c:227
msgid "Image Length"
msgstr "Longitud de la imagen"

#: libexif/exif-tag.c:228
msgid ""
"The number of rows of image data. In JPEG compressed data a JPEG marker is "
"used instead of this tag."
msgstr ""
"La cantidad de filas de datos de la imagen. En datos comprimidos JPEG se "
"utiliza un marcador JPEG en vez de esta etiqueta."

#: libexif/exif-tag.c:231
msgid "Bits per Sample"
msgstr "Bits por muestra"

#: libexif/exif-tag.c:232
#, fuzzy
msgid ""
"The number of bits per image component. In this standard each component of "
"the image is 8 bits, so the value for this tag is 8. See also "
"<SamplesPerPixel>. In JPEG compressed data a JPEG marker is used instead of "
"this tag."
msgstr ""
"La cantidad de bits por componente de imagen. En este estándar cada "
"componente de imagen es 8 bits, por lo que el valor de esta etiqueta es 9. "
"Vea también <SamplesPerPixel>. En datos comprimidos JPEG se utliza un "
"marcador JPEG en vez de esta etiqueta."

#: libexif/exif-tag.c:237
msgid "Compression"
msgstr "Compresión"

#: libexif/exif-tag.c:238
msgid ""
"The compression scheme used for the image data. When a primary image is JPEG "
"compressed, this designation is not necessary and is omitted. When "
"thumbnails use JPEG compression, this tag value is set to 6."
msgstr ""
"El esquema de compresión utilizado para los datos de la imagen. Cuando una "
"imagen primaria está comprimida con JPEG, esto no es necesario y se omite. "
"Cuando las diapositivas utilizan compresión JPEG, el valor de esta etiqueta "
"es 6."

#: libexif/exif-tag.c:244
msgid "Photometric Interpretation"
msgstr "Interpretación fotométrica"

#: libexif/exif-tag.c:245
msgid ""
"The pixel composition. In JPEG compressed data a JPEG marker is used instead "
"of this tag."
msgstr ""
"La composición del pixel. En datos comprimidos JPEG se utliza un marcador "
"JPEG en vez de esta etiqueta."

#: libexif/exif-tag.c:249
msgid "Fill Order"
msgstr "Orden de llenado"

#: libexif/exif-tag.c:251
msgid "Document Name"
msgstr "Nombre del documento"

#: libexif/exif-tag.c:253
msgid "Image Description"
msgstr "Descripción de la imagen"

#: libexif/exif-tag.c:254
msgid ""
"A character string giving the title of the image. It may be a comment such "
"as \"1988 company picnic\" or the like. Two-bytes character codes cannot be "
"used. When a 2-bytes code is necessary, the Exif Private tag <UserComment> "
"is to be used."
msgstr ""
"Una cadena de caracteres que da título a la imagen. Puede ser un comentario "
"como \"picnic de 1988\" o algo por el estilo. No se pueden utilizar "
"caracteres codificados con 2 bytes. Cuando se necesita un código de 2 bytes, "
"se debe usar la etiqueta privada Exif <UserComment>."

#: libexif/exif-tag.c:260
msgid "Manufacturer"
msgstr "Fabricante"

#: libexif/exif-tag.c:261
msgid ""
"The manufacturer of the recording equipment. This is the manufacturer of the "
"DSC, scanner, video digitizer or other equipment that generated the image. "
"When the field is left blank, it is treated as unknown."
msgstr ""
"El fabricante del equipo. Este es el fabricante del DSC, escáner, "
"digitalizador de vídeo u otro equipo que generó la imagen. Cuando el campo "
"se deja en blanco, se trata como desconocido."

#: libexif/exif-tag.c:267
msgid "Model"
msgstr "Modelo"

#: libexif/exif-tag.c:268
msgid ""
"The model name or model number of the equipment. This is the model name or "
"number of the DSC, scanner, video digitizer or other equipment that "
"generated the image. When the field is left blank, it is treated as unknown."
msgstr ""
"El nombre o número de modelo del equipo. Este es el nombre del modelo o "
"número del DSC, escáner, digitalizador de vídeo u otro equipo que generó la "
"imagen. Cuando el campo se deja en blanco, se trata como desconocido."

#: libexif/exif-tag.c:273
msgid "Strip Offsets"
msgstr "Desplazamiento de tira"

#: libexif/exif-tag.c:274
msgid ""
"For each strip, the byte offset of that strip. It is recommended that this "
"be selected so the number of strip bytes does not exceed 64 Kbytes. With "
"JPEG compressed data this designation is not needed and is omitted. See also "
"<RowsPerStrip> and <StripByteCounts>."
msgstr ""
"El desplazamiento en bytes de cada tira. Se recomienda que se seleccione de "
"manera tal que la cantidad de bytes de la tira no exceda 64 Kbytes. Con "
"datos comprimidos JPEG esto no es necesario y se omite. Vea también "
"<RowsPerStrip> y <StripByteCounts>."

#: libexif/exif-tag.c:280
msgid "Orientation"
msgstr "Orientación"

#: libexif/exif-tag.c:281
#, fuzzy
msgid "The image orientation viewed in terms of rows and columns."
msgstr "La orientación de la imagen vista en términos de filas y columnas."

#: libexif/exif-tag.c:284
msgid "Samples per Pixel"
msgstr "Muestras por pixel"

#: libexif/exif-tag.c:285
msgid ""
"The number of components per pixel. Since this standard applies to RGB and "
"YCbCr images, the value set for this tag is 3. In JPEG compressed data a "
"JPEG marker is used instead of this tag."
msgstr ""
"La cantidad de componentes por pixel. Debido a que este estándar se aplica a "
"imágenes RGB e YCbCr, el valor para esta etiqueta es 3. En datos comprimidos "
"JPEG se utliza un marcador JPEG en vez de esta etiqueta."

#: libexif/exif-tag.c:290
msgid "Rows per Strip"
msgstr "Filas por tira"

#: libexif/exif-tag.c:291
#, fuzzy
msgid ""
"The number of rows per strip. This is the number of rows in the image of one "
"strip when an image is divided into strips. With JPEG compressed data this "
"designation is not needed and is omitted. See also <StripOffsets> and "
"<StripByteCounts>."
msgstr ""
"La cantidad de filas por tira. Esta es la cantidad de filas en la imagen de "
"una tira cuando se divide a la imagen en tiras. En datos comprimidos JPEG "
"esto no es necesario y se omite. Vea también <RowsPerStrip> y "
"<StripByteCounts>."

#: libexif/exif-tag.c:297
msgid "Strip Byte Count"
msgstr "Cantidad de bytes por tira"

#: libexif/exif-tag.c:298
msgid ""
"The total number of bytes in each strip. With JPEG compressed data this "
"designation is not needed and is omitted."
msgstr ""
"La cantidad total de bytes en cada tira. En datos comprimidos JPEG esto no "
"es necesario y se omite."

#: libexif/exif-tag.c:301
#, fuzzy
msgid "X-Resolution"
msgstr "Resolución X"

#: libexif/exif-tag.c:302
msgid ""
"The number of pixels per <ResolutionUnit> in the <ImageWidth> direction. "
"When the image resolution is unknown, 72 [dpi] is designated."
msgstr ""
"La cantidad de pixels por <ResolutionUnit> en la dirección <ImageWidth>. "
"Cuando se desconoce la resolución de una imagen, se utilizan 72 [dpi]."

#: libexif/exif-tag.c:306
#, fuzzy
msgid "Y-Resolution"
msgstr "Resolución X"

#: libexif/exif-tag.c:307
msgid ""
"The number of pixels per <ResolutionUnit> in the <ImageLength> direction. "
"The same value as <XResolution> is designated."
msgstr ""
"La cantidad de pixels por <ResolutionUnit> en la dirección <ImageLength>. Se "
"utiliza el mismo valor que <XResolution>."

#: libexif/exif-tag.c:311
msgid "Planar Configuration"
msgstr "Configuración planar"

#: libexif/exif-tag.c:312
msgid ""
"Indicates whether pixel components are recorded in a chunky or planar "
"format. In JPEG compressed files a JPEG marker is used instead of this tag. "
"If this field does not exist, the TIFF default of 1 (chunky) is assumed."
msgstr ""
"Indica si los componentes de pixel se graban en formato planar o por trozos. "
"En datos comprimidos JPEG se utliza un marcador JPEG en vez de esta "
"etiqueta. Si este campo no existe, se asume el predeterminado de TIFF, 1 "
"(por trozos)."

#: libexif/exif-tag.c:317
msgid "Resolution Unit"
msgstr "Unidad de resolución"

#: libexif/exif-tag.c:318
msgid ""
"The unit for measuring <XResolution> and <YResolution>. The same unit is "
"used for both <XResolution> and <YResolution>. If the image resolution is "
"unknown, 2 (inches) is designated."
msgstr ""
"La unidad para medir <XResolution> e <YResolution>. Se utiliza la misma "
"unidad para ambas. Si la resolución de la imagen se desconoce, se designa 2 "
"(pulgadas)."

#: libexif/exif-tag.c:323
msgid "Transfer Function"
msgstr "Función de transferencia"

#: libexif/exif-tag.c:324
msgid ""
"A transfer function for the image, described in tabular style. Normally this "
"tag is not necessary, since color space is specified in the color space "
"information tag (<ColorSpace>)."
msgstr ""
"Una función de transferencia para la imagen, descripta en forma tabular. "
"Normalmente esta etiqueta no es necesaria, dado que el espacio de color se "
"especifica en la etiqueta de información de espacio de color (<ColorSpace>)."

#: libexif/exif-tag.c:328
msgid "Software"
msgstr "Software"

#: libexif/exif-tag.c:329
msgid ""
"This tag records the name and version of the software or firmware of the "
"camera or image input device used to generate the image. The detailed format "
"is not specified, but it is recommended that the example shown below be "
"followed. When the field is left blank, it is treated as unknown."
msgstr ""
"Esta etiqueta registra el nombre y la versión del software o firmware de la "
"cámara o dispositivo de entrada de imagen utilizado para generar la imágen. "
"No se especifica el formato detallado, pero se recomienda que se siga el "
"ejemplo mostrado debajo. Cuando el campo se deja en blanco, se trata como "
"desconocido."

#: libexif/exif-tag.c:336
msgid "Date and Time"
msgstr "Fecha y hora"

#: libexif/exif-tag.c:337
msgid ""
"The date and time of image creation. In this standard (EXIF-2.1) it is the "
"date and time the file was changed."
msgstr ""
"La fecha y hora de la creación de la imagen. En este estándar (EXIF-2.1) es "
"la fecha y hora en la que cambió el archivo."

#: libexif/exif-tag.c:340
msgid "Artist"
msgstr "Artista"

#: libexif/exif-tag.c:341
msgid ""
"This tag records the name of the camera owner, photographer or image "
"creator. The detailed format is not specified, but it is recommended that "
"the information be written as in the example below for ease of "
"Interoperability. When the field is left blank, it is treated as unknown."
msgstr ""
"Esta etiqueta registra el nombre del dueño de la cámara, fotógrafo o creador "
"de la imagen. No se especifica el formato detallado, pero se recomienda que "
"se escriba la información en como en el ejemplo de abajo para facilitar la "
"inter-operabilidad. Cuando el campo se deja en blanco, se trata como "
"desconocido."

#: libexif/exif-tag.c:347 libexif/pentax/mnote-pentax-tag.c:113
msgid "White Point"
msgstr "Punto blanco"

#: libexif/exif-tag.c:348
#, fuzzy
msgid ""
"The chromaticity of the white point of the image. Normally this tag is not "
"necessary, since color space is specified in the color space information tag "
"(<ColorSpace>)."
msgstr ""
"La cromaticidad del punto blanco de la imagen. Normalmente esta etiqueta no "
"es necesaria ya que el espacio de color se especifica en la etiqueta de "
"información del espacio de color (<ColorSpace>)."

#: libexif/exif-tag.c:353
msgid "Primary Chromaticities"
msgstr "Cromaticidades primarias"

#: libexif/exif-tag.c:354
#, fuzzy
msgid ""
"The chromaticity of the three primary colors of the image. Normally this tag "
"is not necessary, since color space is specified in the color space "
"information tag (<ColorSpace>)."
msgstr ""
"La cromaticidad de los tres colores primarios de la imagen. Normalmente esta "
"etiqueta no es necesaria ya que el espacio de color se especifica en la "
"etiqueta de información del espacio de color (<ColorSpace>)."

#: libexif/exif-tag.c:359
msgid "Defined by Adobe Corporation to enable TIFF Trees within a TIFF file."
msgstr ""

#: libexif/exif-tag.c:362
msgid "Transfer Range"
msgstr "Rango de transferencia"

#: libexif/exif-tag.c:366
msgid "JPEG Interchange Format"
msgstr "Formato de intercambio de JPEG"

#: libexif/exif-tag.c:367
msgid ""
"The offset to the start byte (SOI) of JPEG compressed thumbnail data. This "
"is not used for primary image JPEG data."
msgstr ""
"El desplazamiento del byte de comienzo (SOI) de los datos comprimidos de la "
"diapositiva JPEG. Esto no se utiliza para los datos primarios de imagen JPEG."

#: libexif/exif-tag.c:372
msgid "JPEG Interchange Format Length"
msgstr "Longitud del formato de intercambio de JPEG"

#: libexif/exif-tag.c:373
msgid ""
"The number of bytes of JPEG compressed thumbnail data. This is not used for "
"primary image JPEG data. JPEG thumbnails are not divided but are recorded as "
"a continuous JPEG bitstream from SOI to EOI. Appn and COM markers should not "
"be recorded. Compressed thumbnails must be recorded in no more than 64 "
"Kbytes, including all other data to be recorded in APP1."
msgstr ""
"La cantidad de bytes de datos comprimidos de diapositiva JPEG. Esto no se "
"utiliza para los datos primarios JPEG. Las diapositivas JPEG no se dividen "
"sino que se graban como un flujo de bits contínuo desde SOI hasta EOI. No se "
"deberían registrar marcadores Appn y COM. Las diapositivas comprimidas "
"deberían grabarse en no más de 64 Kbytes, incluyendo todos los otros datos a "
"grabar en APP1."

#: libexif/exif-tag.c:382
msgid "YCbCr Coefficients"
msgstr "Coeficientes YCbCr"

#: libexif/exif-tag.c:383
#, fuzzy
msgid ""
"The matrix coefficients for transformation from RGB to YCbCr image data. No "
"default is given in TIFF; but here the value given in \"Color Space "
"Guidelines\", is used as the default. The color space is declared in a color "
"space information tag, with the default being the value that gives the "
"optimal image characteristics Interoperability this condition."
msgstr ""
"Los coeficientes de la matriz para la transformación de RGB a datos de "
"imagen YCbCr. No hay predeterminados en TIFF; pero aquí se utiliza el valor "
"dado en el Apéndice E, \"Color Space Guidelines\" como predeterminado. El "
"espacio de color se declara en una etiqueta de información del espacio de "
"color, siendo el predeterminado el que da las características óptimas de "
"inter-operabilidad para la imagen."

#: libexif/exif-tag.c:392
msgid "YCbCr Sub-Sampling"
msgstr "Sub-muestreo YCbCr"

#: libexif/exif-tag.c:393
msgid ""
"The sampling ratio of chrominance components in relation to the luminance "
"component. In JPEG compressed data a JPEG marker is used instead of this tag."
msgstr ""
"La relación de muestreo de los componentes de crominancia en relación con el "
"componente de luminancia. En datos comprimidos JPEG se utliza un marcador "
"JPEG en vez de esta etiqueta."

#: libexif/exif-tag.c:398
msgid "YCbCr Positioning"
msgstr "Posicionamiento YCbCr"

#: libexif/exif-tag.c:399
msgid ""
"The position of chrominance components in relation to the luminance "
"component. This field is designated only for JPEG compressed data or "
"uncompressed YCbCr data. The TIFF default is 1 (centered); but when Y:Cb:Cr "
"= 4:2:2 it is recommended in this standard that 2 (co-sited) be used to "
"record data, in order to improve the image quality when viewed on TV "
"systems. When this field does not exist, the reader shall assume the TIFF "
"default. In the case of Y:Cb:Cr = 4:2:0, the TIFF default (centered) is "
"recommended. If the reader does not have the capability of supporting both "
"kinds of <YCbCrPositioning>, it shall follow the TIFF default regardless of "
"the value in this field. It is preferable that readers be able to support "
"both centered and co-sited positioning."
msgstr ""
"La posición de los componentes de crominancia en relación con el de "
"luminancia. Este campo se designa sólo para datos comprimidos JPEG o datos "
"no comprimidos YCbCr. El valor predeterminado TIFF es 1 (centrado); pero "
"cuando Y:Cb:Cr = 4:2:2 se recomienda en este estándar que se utilice 2 (co-"
"sitiado) para registrar los datos, para mejorar la calidad de la imagen "
"cuando se ve en sistemas de TV. Cuando este campo no existe, el lector "
"deberá asumir el predeterminado TIFF. En caso que Y:Cb:Cr = 4:2:0, se "
"recomienda el predeterminado TIFF (centrado). Si el lector no tiene la "
"capacidad de soportar ambos tipos de <YCbCrPositioning>, debería seguir el "
"predeterminado TIFF sin importar el valor de este campo. Es preferible que "
"los lectores puedan soportar el posicionamiento centrado y co-sitiado."

#: libexif/exif-tag.c:414
msgid "Reference Black/White"
msgstr "Referencia Blanco/Negro"

#: libexif/exif-tag.c:415
msgid ""
"The reference black point value and reference white point value. No defaults "
"are given in TIFF, but the values below are given as defaults here. The "
"color space is declared in a color space information tag, with the default "
"being the value that gives the optimal image characteristics "
"Interoperability these conditions."
msgstr ""
"El valor de referencia de los puntos blanco y negro. En TIFF no se dan "
"predeterminados, pero los valores de abajo se dan como predeterminados aquí. "
"El espacio de color se declara en una etiqueta de información de espacio de "
"color, siendo el valor predeterminado aquel que da las características de "
"inter-operabilidad óptimas para la imagen."

#: libexif/exif-tag.c:423
msgid "XML Packet"
msgstr ""

#: libexif/exif-tag.c:423
msgid "XMP Metadata"
msgstr ""

#: libexif/exif-tag.c:438 libexif/exif-tag.c:784
msgid "CFA Pattern"
msgstr "Patrón CFA"

#: libexif/exif-tag.c:439 libexif/exif-tag.c:785
msgid ""
"Indicates the color filter array (CFA) geometric pattern of the image sensor "
"when a one-chip color area sensor is used. It does not apply to all sensing "
"methods."
msgstr ""
"Indica el patrón geométrico de la matriz de filtro de color (CFA) del sensor "
"de imagen cuando se utiliza un sensor de área color de un chip. No aplica a "
"todos los métodos de sensado."

#: libexif/exif-tag.c:443
msgid "Battery Level"
msgstr "Nivel de la batería"

#: libexif/exif-tag.c:444
msgid "Copyright"
msgstr "Copyright"

#: libexif/exif-tag.c:445
msgid ""
"Copyright information. In this standard the tag is used to indicate both the "
"photographer and editor copyrights. It is the copyright notice of the person "
"or organization claiming rights to the image. The Interoperability copyright "
"statement including date and rights should be written in this field; e.g., "
"\"Copyright, John Smith, 19xx. All rights reserved.\". In this standard the "
"field records both the photographer and editor copyrights, with each "
"recorded in a separate part of the statement. When there is a clear "
"distinction between the photographer and editor copyrights, these are to be "
"written in the order of photographer followed by editor copyright, separated "
"by NULL (in this case, since the statement also ends with a NULL, there are "
"two NULL codes) (see example 1). When only the photographer is given, it is "
"terminated by one NULL code (see example 2). When only the editor copyright "
"is given, the photographer copyright part consists of one space followed by "
"a terminating NULL code, then the editor copyright is given (see example 3). "
"When the field is left blank, it is treated as unknown."
msgstr ""
"Información del Copyright. En este estándar la etiqueta se utiliza para "
"indicar tanto el copyright del fotógrafo como el del editor. Es la nota de "
"copyright de la persona u Organización que reclama derechos sobre la imagen. "
"En este campo debería escribirse la declaración de copyrtight de inter-"
"operabilidad, incluyendo la fecha y los derechos; ej: \"Copyright, Juan "
"Perez, 20xx. Todos los derechos reservados.\" En este estándar el campo "
"registra tanto al copyright del fotógrafo como el del editor, con cada uno "
"registrado en una parte separada de la declaración. Cuando hay una "
"distinción clara entre ambos copyrights, primero debería escribirse el del "
"fotógrafo seguido por el del editor, separados por NULL (en este caso, "
"debido a que la declaración también termina con un NULL, hay dos códigos "
"NULL) (vea el ejemplo 1). Cuando se da sólo el del fotógrafo, está terminado "
"por un código NULL (vea el ejemplo 2). Cuando se da sólo el del editor, la "
"parte del copyright del fotógrafo consiste de un espacio seguido de un "
"código de terminación NULL, luego se da el copyright del editor (vea el "
"ejemplo 3). Cuando el campo se deja en blanco, se trata como desconocido."

#: libexif/exif-tag.c:467
msgid "Exposure time, given in seconds (sec)."
msgstr "Tiempo de exposición, dado en segundos (seg)."

#: libexif/exif-tag.c:469 libexif/pentax/mnote-pentax-tag.c:79
#, fuzzy
msgid "F-Number"
msgstr "El número F."

#: libexif/exif-tag.c:470
msgid "The F number."
msgstr "El número F."

#: libexif/exif-tag.c:475
msgid "Image Resources Block"
msgstr ""

#: libexif/exif-tag.c:477
msgid ""
"A pointer to the Exif IFD. Interoperability, Exif IFD has the same structure "
"as that of the IFD specified in TIFF. ordinarily, however, it does not "
"contain image data as in the case of TIFF."
msgstr ""
"Un puntero al IFD Exif. Inter-operabilidad, el IFD Exif tiene la misma "
"estructura que la del IFD especificado en TIFF. Sin embargo, por lo común, "
"no contiene datos de imagen como en el caso de TIFF."

#: libexif/exif-tag.c:485
#, fuzzy
msgid "Exposure Program"
msgstr "Modo de exposición"

#: libexif/exif-tag.c:486
msgid ""
"The class of the program used by the camera to set exposure when the picture "
"is taken."
msgstr ""
"La clase de programa utilizado por la cámara para ajustar la exposición "
"cuando se toma la foto."

#: libexif/exif-tag.c:490
msgid "Spectral Sensitivity"
msgstr "Sensibilidad espectral"

#: libexif/exif-tag.c:491
#, fuzzy
msgid ""
"Indicates the spectral sensitivity of each channel of the camera used. The "
"tag value is an ASCII string compatible with the standard developed by the "
"ASTM Technical Committee."
msgstr ""
"Indica la sensibilidad espectral de cada canal de la cámara utilizada. El "
"valor de la etiqueta es una cadena de caracteres ASCII compatible con el "
"estándar desarrollado por el ASTM Tecnical committee."

#: libexif/exif-tag.c:496
msgid "GPS Info IFD Pointer"
msgstr ""

#: libexif/exif-tag.c:497
msgid ""
"A pointer to the GPS Info IFD. The Interoperability structure of the GPS "
"Info IFD, like that of Exif IFD, has no image data."
msgstr ""
"Un puntero al GPS Info IFD. La estructura de inter-operabilidad del GPS Info "
"IFD, como la del IFD Exif, no tiene datos de imagen."

#: libexif/exif-tag.c:503
msgid "ISO Speed Ratings"
msgstr "Velocidad ISO"

#: libexif/exif-tag.c:504
msgid ""
"Indicates the ISO Speed and ISO Latitude of the camera or input device as "
"specified in ISO 12232."
msgstr ""
"Indica la velocidad ISO y la latitud ISO de la cámara o dispositivo de "
"entrada como se especifica en ISO 12232."

#: libexif/exif-tag.c:507
msgid "Opto-Electronic Conversion Function"
msgstr ""

#: libexif/exif-tag.c:508
#, fuzzy
msgid ""
"Indicates the Opto-Electronic Conversion Function (OECF) specified in ISO "
"14524. <OECF> is the relationship between the camera optical input and the "
"image values."
msgstr ""
"Indica la Función de Conversión Opto-Electrónica (OECF) especificada en ISO "
"14524. <OECF> es la relación entre la entrada óptica de la cámara y los "
"valores de la imagen."

#: libexif/exif-tag.c:513
msgid "Time Zone Offset"
msgstr ""

#: libexif/exif-tag.c:514
msgid "Encodes time zone of camera clock relative to GMT."
msgstr ""

#: libexif/exif-tag.c:515
msgid "Exif Version"
msgstr "Versión Exif"

#: libexif/exif-tag.c:516
msgid ""
"The version of this standard supported. Nonexistence of this field is taken "
"to mean nonconformance to the standard."
msgstr ""
"La versión soportada de este estándar. Si este campo no existe se toma como "
"que significa que no se cumple con el estándar."

#: libexif/exif-tag.c:520
#, fuzzy
msgid "Date and Time (Original)"
msgstr "Fecha y Hora (original)"

#: libexif/exif-tag.c:521
msgid ""
"The date and time when the original image data was generated. For a digital "
"still camera the date and time the picture was taken are recorded."
msgstr ""
"La fecha y hora cuando se generaron los datos originales de la imagen. Para "
"una cámara digital se registra la fecha y la hora en la que se tomó la foto."

#: libexif/exif-tag.c:526
#, fuzzy
msgid "Date and Time (Digitized)"
msgstr "Fecha y Hora (digitalizado)"

#: libexif/exif-tag.c:527
#, fuzzy
msgid "The date and time when the image was stored as digital data."
msgstr ""
"La fehca y hora cuando se almacenó la imagen en forma de datos digitales."

#: libexif/exif-tag.c:530
#, fuzzy
msgid "Components Configuration"
msgstr "Configuración planar"

#: libexif/exif-tag.c:531
msgid ""
"Information specific to compressed data. The channels of each component are "
"arranged in order from the 1st component to the 4th. For uncompressed data "
"the data arrangement is given in the <PhotometricInterpretation> tag. "
"However, since <PhotometricInterpretation> can only express the order of Y, "
"Cb and Cr, this tag is provided for cases when compressed data uses "
"components other than Y, Cb, and Cr and to enable support of other sequences."
msgstr ""
"Información específica de los datos comprimidos. Los canales de cada "
"componente se arreglan en orden desde el 1er componente al 4to. Para datos "
"no comprimidos el arreglo de los datos se da en la etiqueta "
"<PhometricInterpretation>. Sin embargo, debido a que dicha etiqueta sólo "
"puede expresar el orden de Y, Cb y Cr, esta etiqueta se da para los casos en "
"los que los datos comprimidos usan componentes que no son Y, Cb y Cr y para "
"permitir el soporte de otras secuencias."

#: libexif/exif-tag.c:541
msgid "Compressed Bits per Pixel"
msgstr "Bits comprimidos por pixel"

#: libexif/exif-tag.c:542
msgid ""
"Information specific to compressed data. The compression mode used for a "
"compressed image is indicated in unit bits per pixel."
msgstr ""
"Información específica sobre los datos comprimidos. El modo de compresión "
"utilizado para una imagen comprimida está indicado en unidades de bits por "
"pixel."

#: libexif/exif-tag.c:546 libexif/olympus/mnote-olympus-tag.c:123
#, fuzzy
msgid "Shutter Speed"
msgstr "Velocidad del obturador"

#: libexif/exif-tag.c:547
msgid ""
"Shutter speed. The unit is the APEX (Additive System of Photographic "
"Exposure) setting."
msgstr ""
"Velocidad del obturador. La unidad es el ajuste APEX (Sistema aditivo de "
"exposición fotográfica)."

#: libexif/exif-tag.c:551
msgid "The lens aperture. The unit is the APEX value."
msgstr "La apertura de la lente. La unidad es el valor APEX."

#: libexif/exif-tag.c:553
msgid "Brightness"
msgstr "Brillo"

#: libexif/exif-tag.c:554
msgid ""
"The value of brightness. The unit is the APEX value. Ordinarily it is given "
"in the range of -99.99 to 99.99."
msgstr ""
"El valor del brillo. La unidad es el valor APEX. Por lo general, se da en el "
"rango de -99,99 a 99,99."

#: libexif/exif-tag.c:558
msgid "Exposure Bias"
msgstr "Ajuste de exposición"

#: libexif/exif-tag.c:559
msgid ""
"The exposure bias. The units is the APEX value. Ordinarily it is given in "
"the range of -99.99 to 99.99."
msgstr ""
"El ajuste de exposición. La unidad es el valor APEX. Por lo general, se da "
"en el rango -99,99 a 99,99."

#: libexif/exif-tag.c:562
#, fuzzy
msgid "Maximum Aperture Value"
msgstr "Apertura"

#: libexif/exif-tag.c:563
msgid ""
"The smallest F number of the lens. The unit is the APEX value. Ordinarily it "
"is given in the range of 00.00 to 99.99, but it is not limited to this range."
msgstr ""
"El valor F más pequeño de la lente. La unidad es el valor APEX. Por lo "
"general, se da en el rango de 00,00 a 99,99 pero no está limitado a dicho "
"rango."

#: libexif/exif-tag.c:568
msgid "Subject Distance"
msgstr "Distancia del sujeto"

#: libexif/exif-tag.c:569
msgid "The distance to the subject, given in meters."
msgstr "La distancia al sujeto, dada en metros."

#: libexif/exif-tag.c:572
msgid "The metering mode."
msgstr "El modo de la métrica."

#: libexif/exif-tag.c:574
msgid "Light Source"
msgstr "Fuente de luz"

#: libexif/exif-tag.c:575
msgid "The kind of light source."
msgstr "El tipo de fuente de luz."

#: libexif/exif-tag.c:578
msgid ""
"This tag is recorded when an image is taken using a strobe light (flash)."
msgstr ""
"Esta etiqueta se registra cuando se toma una imagen usando una luz "
"estrosboscópica (flash)."

#: libexif/exif-tag.c:582
msgid ""
"The actual focal length of the lens, in mm. Conversion is not made to the "
"focal length of a 35 mm film camera."
msgstr ""
"La distancia focal real de la lente, en mm. No se realiza la conversión a la "
"distancia focal de una máquina de película de 35 mm."

#: libexif/exif-tag.c:585
msgid "Subject Area"
msgstr "Área del sujeto"

#: libexif/exif-tag.c:586
msgid ""
"This tag indicates the location and area of the main subject in the overall "
"scene."
msgstr ""
"Esta etiqueta indica la ubicación y el área del sujeto principal en la "
"escena general."

#: libexif/exif-tag.c:590
msgid "TIFF/EP Standard ID"
msgstr ""

#: libexif/exif-tag.c:591
msgid "Maker Note"
msgstr "Nota del fabricante"

#: libexif/exif-tag.c:592
msgid ""
"A tag for manufacturers of Exif writers to record any desired information. "
"The contents are up to the manufacturer."
msgstr ""
"Una etiqueta para que los fabricantes o escritores Exif registren cualquier "
"información deseada. El contenido queda a cargo del fabricante."

#: libexif/exif-tag.c:595
msgid "User Comment"
msgstr "Comentario del usuario"

#: libexif/exif-tag.c:596
#, fuzzy
msgid ""
"A tag for Exif users to write keywords or comments on the image besides "
"those in <ImageDescription>, and without the character code limitations of "
"the <ImageDescription> tag. The character code used in the <UserComment> tag "
"is identified based on an ID code in a fixed 8-byte area at the start of the "
"tag data area. The unused portion of the area is padded with NULL (\"00.h"
"\"). ID codes are assigned by means of registration. The designation method "
"and references for each character code are defined in the specification. The "
"value of CountN is determined based on the 8 bytes in the character code "
"area and the number of bytes in the user comment part. Since the TYPE is not "
"ASCII, NULL termination is not necessary. The ID code for the <UserComment> "
"area may be a Defined code such as JIS or ASCII, or may be Undefined. The "
"Undefined name is UndefinedText, and the ID code is filled with 8 bytes of "
"all \"NULL\" (\"00.H\"). An Exif reader that reads the <UserComment> tag "
"must have a function for determining the ID code. This function is not "
"required in Exif readers that do not use the <UserComment> tag. When a "
"<UserComment> area is set aside, it is recommended that the ID code be ASCII "
"and that the following user comment part be filled with blank characters [20."
"H]."
msgstr ""
"Una etiqueta para que los usuarios de Exif escriban palabras clave o "
"comentarios en la imagen además de los de <ImageDescription>, y sin "
"limitaciones en la codificación de caracteres de dicha etiqueta. El código "
"de caracteres utilizado en esta etiqueta se identifica basándose en un "
"código ID en un área fija de 8 bytes al comienzo del área de datos de la "
"etiqueta. La porción no utilizada del área se completa con NULL (\"00.h\"). "
"Los códigos ID se asignan por medio de registro. En la tabla 6 se dan el "
"método de designación y referencias para cada código de caracter. El valor "
"de CountN está determinado basado en los 8 bytes en el área de código de "
"caracter y la cantidad de bytes en la parte del comentario del usuario. Dado "
"que TYPE no es ASCII, no es necesaria la terminación NULL (ver fig. 9). El "
"código ID para el área <UserComment> puede ser un código definido como JIS o "
"ASCII, o puede ser no definido. El nombre no definido es UndefinedText, y el "
"código ID se completa con 8 bytes de todos \"NULL\" (\"00.H\"). Un lector "
"Exif que lee la etiqueta <UserComment> debe tener una función para "
"determinar el código ID. Esta función no se necesita en lectores Exif que no "
"usan la etiqueta <UserComment> (ver tabla 7). Cuando se deja a un lado el "
"área <UserComment>, se recomienda que el código ID sea ASCII y que se "
"complete la parte de comentario de usuario siguiente con caracteres espacio "
"[20.H]."

#: libexif/exif-tag.c:619
msgid "Sub-second Time"
msgstr ""

#: libexif/exif-tag.c:620
msgid "A tag used to record fractions of seconds for the <DateTime> tag."
msgstr ""
"Una etiqueta utilizada para registrar fracciones de segundo para la etiqueta "
"<DateTime>."

#: libexif/exif-tag.c:624
#, fuzzy
msgid "Sub-second Time (Original)"
msgstr "Fecha y Hora (original)"

#: libexif/exif-tag.c:625
msgid ""
"A tag used to record fractions of seconds for the <DateTimeOriginal> tag."
msgstr ""
"Una etiqueta utilizada para registrar fracciones de segundo para la etiqueta "
"<DateTimeOriginal>."

#: libexif/exif-tag.c:629
#, fuzzy
msgid "Sub-second Time (Digitized)"
msgstr "Fecha y Hora (digitalizado)"

#: libexif/exif-tag.c:630
msgid ""
"A tag used to record fractions of seconds for the <DateTimeDigitized> tag."
msgstr ""
"Una etiqueta utilizada para registrar fracciones de segundo para la etiqueta "
"<DateTimeDigitized>."

#: libexif/exif-tag.c:634
msgid "XP Title"
msgstr ""

#: libexif/exif-tag.c:635
msgid "A character string giving the title of the image, encoded in UTF-16LE."
msgstr ""

#: libexif/exif-tag.c:639
#, fuzzy
msgid "XP Comment"
msgstr "Comentario del usuario"

#: libexif/exif-tag.c:640
msgid ""
"A character string containing a comment about the image, encoded in UTF-16LE."
msgstr ""

#: libexif/exif-tag.c:644
msgid "XP Author"
msgstr ""

#: libexif/exif-tag.c:645
msgid ""
"A character string containing the name of the image creator, encoded in "
"UTF-16LE."
msgstr ""

#: libexif/exif-tag.c:649
msgid "XP Keywords"
msgstr ""

#: libexif/exif-tag.c:650
msgid ""
"A character string containing key words describing the image, encoded in "
"UTF-16LE."
msgstr ""

#: libexif/exif-tag.c:654
#, fuzzy
msgid "XP Subject"
msgstr "Área del sujeto"

#: libexif/exif-tag.c:655
msgid "A character string giving the image subject, encoded in UTF-16LE."
msgstr ""

#: libexif/exif-tag.c:659
msgid "The FlashPix format version supported by a FPXR file."
msgstr "La versión del formato FlashPix soportada por un archivo FPXR."

#: libexif/exif-tag.c:661 libexif/pentax/mnote-pentax-tag.c:102
msgid "Color Space"
msgstr "Espacio de color"

#: libexif/exif-tag.c:662
#, fuzzy
msgid ""
"The color space information tag is always recorded as the color space "
"specifier. Normally sRGB (=1) is used to define the color space based on the "
"PC monitor conditions and environment. If a color space other than sRGB is "
"used, Uncalibrated (=FFFF.H) is set. Image data recorded as Uncalibrated can "
"be treated as sRGB when it is converted to FlashPix."
msgstr ""
"La etiqueta de información de espacio de color (<ColorSpace>) siempre se "
"registra como el especificador de espacio de color. Normalmente se utiliza "
"sRGB (=1) para definir el espacio de color basado en las condiciones "
"ambientales y del monitor de la PC. Si se utiliza un espacio de color "
"distinto a sRGB, se ajusta en Uncalibrated (=FFFF.H). Los datos de imagen "
"registrados como no calibrados pueden tratarse como sRGB cuando se convierte "
"a FlashPix."

#: libexif/exif-tag.c:670
msgid "Pixel X Dimension"
msgstr ""

#: libexif/exif-tag.c:671
msgid ""
"Information specific to compressed data. When a compressed file is recorded, "
"the valid width of the meaningful image must be recorded in this tag, "
"whether or not there is padding data or a restart marker. This tag should "
"not exist in an uncompressed file."
msgstr ""
"Información específica a los datos comprimidos. Cuando se registra un "
"archivo comprimido, el ancho válido de la imagen significativa se debe "
"registrar en esta etiqueta, haya o no datos de colchón o una marca de "
"reinicio. Esta etiqueta no debería existir en un archivo no comprimido. Vea "
"la sección 2.8.1 y el Apéndice F para más detalles."

#: libexif/exif-tag.c:677
msgid "Pixel Y Dimension"
msgstr ""

#: libexif/exif-tag.c:678
#, fuzzy
msgid ""
"Information specific to compressed data. When a compressed file is recorded, "
"the valid height of the meaningful image must be recorded in this tag, "
"whether or not there is padding data or a restart marker. This tag should "
"not exist in an uncompressed file. Since data padding is unnecessary in the "
"vertical direction, the number of lines recorded in this valid image height "
"tag will in fact be the same as that recorded in the SOF."
msgstr ""
"Información específica a los datos comprimidos. Cuando se registra un "
"archivo comprimido, la altura válida de la imagen significativa debería "
"registrarse en esta etiqueta, haya o no datos de colchón o una marca de "
"reinicio. Esta etiqueta no debería existir en un archivo no comprimido. Vea "
"la sección 2.8.1 y el Apéndice F para más detalles. Dado que no es necesario "
"el colchón de datos en la dirección vertical, la cantidad de líneas "
"registradas en esta etiqueta válida de altura de imagen será, de hecho, la "
"misma que la registrada en el SOF."

#: libexif/exif-tag.c:688
msgid "Related Sound File"
msgstr ""

#: libexif/exif-tag.c:689
#, fuzzy
msgid ""
"This tag is used to record the name of an audio file related to the image "
"data. The only relational information recorded here is the Exif audio file "
"name and extension (an ASCII string consisting of 8 characters + '.' + 3 "
"characters). The path is not recorded. Stipulations on audio and file naming "
"conventions are defined in the specification. When using this tag, audio "
"files must be recorded in conformance to the Exif audio format. Writers are "
"also allowed to store the data such as Audio within APP2 as FlashPix "
"extension stream data. The mapping of Exif image files and audio files is "
"done in any of three ways, [1], [2] and [3]. If multiple files are mapped to "
"one file as in [2] or [3], the above format is used to record just one audio "
"file name. If there are multiple audio files, the first recorded file is "
"given. In the case of [3], for example, for the Exif image file \"DSC00001."
"JPG\" only  \"SND00001.WAV\" is given as the related Exif audio file. When "
"there are three Exif audio files \"SND00001.WAV\", \"SND00002.WAV\" and "
"\"SND00003.WAV\", the Exif image file name for each of them, \"DSC00001.JPG"
"\", is indicated. By combining multiple relational information, a variety of "
"playback possibilities can be supported. The method of using relational "
"information is left to the implementation on the playback side. Since this "
"information is an ASCII character string, it is terminated by NULL. When "
"this tag is used to map audio files, the relation of the audio file to image "
"data must also be indicated on the audio file end."
msgstr ""
"Esta etiqueta se utiliza para registrar el nombre de un archivo de audio "
"relacionado a los datos de imagen. La única información relacional "
"registrada aquí es el nombre y la extensión del archivo de audio Exif (una "
"cadena de caracteres ASCII que consiste en 8 caracteres, un '.' y 3 "
"caracteres más). No se registra la ruta. Las estipulaciones acerca del audio "
"se dan en la sección 3.6.3. Las convenciones para los nombres de archivo se "
"dan en la sección 3.7.1. Cuando se usa esta etiqueta, los archivos de audio "
"se deben grabar en conformidad con el formato de audio Exif. También se "
"permite a los escritores almacenar los datos tales como Audio dentro de APP2 "
"como un flujo de datos de extensión de FlashPix. Los archivos de audio se "
"deben grabar en conformidad con el formato de audio Exif. El mapeo de "
"archivos de imagen y archivos de audio Exif se realiza en cualquiera de las "
"tres maneras que se muestran en la Tabla 8. Si múltiples archivos mapean a "
"uno solo como en [2] o [3] de la tabla, el formato anterior se usa para "
"registrar sólo un nombre de archivo de audio. Si hay múltiples archivos de "
"audio, se da el primero grabado. En el caso de [3] en la Tabla 8, por "
"ejemplo, para el archivo de imagen Exif \"DSC00001.JPG\" sólo se da "
"\"SND00001.WAV\" como el archivo de audio Exif relacionado. Cuando hay tres "
"archivos de audio Exif, \"SND00001.WAV\", \"SND00002.WAV\" y \"SND00003.WAV"
"\", se indica el nombre de archivo de imagen Exif para cada uno de ellos, "
"\"DSC00001.JPG\". Al combinar información relacional múltiple, es posible "
"soportar una variedad de posibilidades de reproducción. El método de "
"utilizar información relacional se deja a la implementación del lado del "
"reproductor. Dado que esta información es una cadena de caracteres ASCII, la "
"misma está terminada por NULL. Cuando esta etiqueta se utiliza para mapear "
"archivos de audio, también se debe indicar al final del archivo de audio la "
"relación a los datos de la imagen."

#: libexif/exif-tag.c:719
msgid "Interoperability IFD Pointer"
msgstr ""

#: libexif/exif-tag.c:720
msgid ""
"Interoperability IFD is composed of tags which stores the information to "
"ensure the Interoperability and pointed by the following tag located in Exif "
"IFD. The Interoperability structure of Interoperability IFD is the same as "
"TIFF defined IFD structure but does not contain the image data "
"characteristically compared with normal TIFF IFD."
msgstr ""
"El IFD de interoperabilidad está compuesto de etiquetas que almacenan la "
"información para asegurar la interoperabilidad y apuntado por la etiqueta "
"siguiente ubicada en el IFD Exif. La estructura de interoperabilidad de el "
"IFD de interoperabilidad es la misma que la estructura IFD definida por "
"TIFF, pero no contiene los datos de imagen característicos del IFD normal de "
"TIFF."

#: libexif/exif-tag.c:729
msgid "Flash Energy"
msgstr "Energía del flash"

#: libexif/exif-tag.c:730
msgid ""
"Indicates the strobe energy at the time the image is captured, as measured "
"in Beam Candle Power Seconds (BCPS)."
msgstr ""
"Indica la energía del flash en el momento que se captura la imagen, medida "
"en Beam Candle Power Seconds (BCPS)."

#: libexif/exif-tag.c:734
msgid "Spatial Frequency Response"
msgstr "Respuesta en frecuencia espacial"

#: libexif/exif-tag.c:735
msgid ""
"This tag records the camera or input device spatial frequency table and SFR "
"values in the direction of image width, image height, and diagonal "
"direction, as specified in ISO 12233."
msgstr ""
"Este etiqueta registra la tabla de frecuencia espacial de la cámara o "
"dispositivo de entrada y los valores SFR en las direcciones de ancho, alto y "
"diagonal de la imagen, como se especifica en ISO 12233."

#: libexif/exif-tag.c:741
#, fuzzy
msgid "Focal Plane X-Resolution"
msgstr "Resolución X del plano focal"

#: libexif/exif-tag.c:742
msgid ""
"Indicates the number of pixels in the image width (X) direction per "
"<FocalPlaneResolutionUnit> on the camera focal plane."
msgstr ""
"Indica la cantidad de pixels en la dirección del ancho (X) de la imagen por "
"<FocalPlaneResolutionUnit> del plano focal de la cámara."

#: libexif/exif-tag.c:746
#, fuzzy
msgid "Focal Plane Y-Resolution"
msgstr "Resolución X del plano focal"

#: libexif/exif-tag.c:747
msgid ""
"Indicates the number of pixels in the image height (V) direction per "
"<FocalPlaneResolutionUnit> on the camera focal plane."
msgstr ""
"Indica la cantidad de pixels en la dirección del alto (Y) de la imagen por "
"<FocalPlaneResolutionUnit> del plano focal de la cámara."

#: libexif/exif-tag.c:751
msgid "Focal Plane Resolution Unit"
msgstr "Unidad de resolución del plano focal"

#: libexif/exif-tag.c:752
msgid ""
"Indicates the unit for measuring <FocalPlaneXResolution> and "
"<FocalPlaneYResolution>. This value is the same as the <ResolutionUnit>."
msgstr ""
"Indica la unidad para medir <FocalPlaneXResolution> e "
"<FocalPlaneYResolution>. Este valor es el mismo que <ResolutionUnit>."

#: libexif/exif-tag.c:757
msgid "Subject Location"
msgstr "Ubicación del sujeto"

#: libexif/exif-tag.c:758
#, fuzzy
msgid ""
"Indicates the location of the main subject in the scene. The value of this "
"tag represents the pixel at the center of the main subject relative to the "
"left edge, prior to rotation processing as per the <Rotation> tag. The first "
"value indicates the X column number and the second indicates the Y row "
"number."
msgstr ""
"Indica la ubicación del sujeto principal de la escena. El valor de esta "
"etiqueta representa el pixel en el centro del sujeto principal relativo al "
"borde izquierdo, antes del proceso de rotación como lo indica la etiqueta "
"<Rotation>. El primer valor indica el número de columna X y el segundo el "
"número de fila Y."

#: libexif/exif-tag.c:765
#, fuzzy
msgid "Exposure Index"
msgstr "Índice de exposición"

#: libexif/exif-tag.c:766
msgid ""
"Indicates the exposure index selected on the camera or input device at the "
"time the image is captured."
msgstr ""
"Indica el índice de exposición seleccionado en la cámara o dispositivo de "
"entrada en el momento que se captura la imagen."

#: libexif/exif-tag.c:769
msgid "Sensing Method"
msgstr "Método de sensado"

#: libexif/exif-tag.c:770
msgid "Indicates the image sensor type on the camera or input device."
msgstr ""
"Indica el tipo de sensor de imagen en la cámara o dispositivo de entrada."

#: libexif/exif-tag.c:773 libexif/fuji/mnote-fuji-tag.c:64
msgid "File Source"
msgstr "Fuente de archivo"

#: libexif/exif-tag.c:774
#, fuzzy
msgid ""
"Indicates the image source. If a DSC recorded the image, the tag value of "
"this tag always be set to 3, indicating that the image was recorded on a DSC."
msgstr ""
"Indica la fuente de la imagen. Si la imagen se registró en un DSC, el valor "
"de esta etiqueta siempre debe ser 3, indicando que la imagen se registró en "
"un DSC."

#: libexif/exif-tag.c:778
msgid "Scene Type"
msgstr "Tipo de escena"

#: libexif/exif-tag.c:779
msgid ""
"Indicates the type of scene. If a DSC recorded the image, this tag value "
"must always be set to 1, indicating that the image was directly photographed."
msgstr ""
"Indica el tipo de la escena. Si la imagen se registró en un DSC, el valor de "
"esta etiqueta siempre debe ser 1, indicando que la imagen se registró en un "
"DSC."

#: libexif/exif-tag.c:789
msgid "Custom Rendered"
msgstr "Render personalizado"

#: libexif/exif-tag.c:790
msgid ""
"This tag indicates the use of special processing on image data, such as "
"rendering geared to output. When special processing is performed, the reader "
"is expected to disable or minimize any further processing."
msgstr ""
"Esta etiqueta indica el uso de procesamiento especial en los datos de "
"imagen, tal como el rendering de la salida. Cuando se realiza un "
"procesamiento especial, se espera que el lector deshabilite o minimice el "
"procesado siguiente."

#: libexif/exif-tag.c:796
msgid ""
"This tag indicates the exposure mode set when the image was shot. In auto-"
"bracketing mode, the camera shoots a series of frames of the same scene at "
"different exposure settings."
msgstr ""
"Esta etiqueta indica el modo de exposición ajustado cuando se tomó la "
"imagen. En el modo auto-bracketing, la cámara toma una serie de cuadros de "
"la misma escena con ajustes de exposición diferentes."

#: libexif/exif-tag.c:801
msgid "This tag indicates the white balance mode set when the image was shot."
msgstr ""
"Esta etiqueta indica el modo de balance de blanco ajustado cuando se tomó la "
"imagen."

#: libexif/exif-tag.c:805
msgid "Digital Zoom Ratio"
msgstr "Relación de zoom digital"

#: libexif/exif-tag.c:806
msgid ""
"This tag indicates the digital zoom ratio when the image was shot. If the "
"numerator of the recorded value is 0, this indicates that digital zoom was "
"not used."
msgstr ""
"Esta etiqueta indica la relación del zoom digital cuando se tomó la imagen. "
"Si el numerador del valor registrado es 0, esto indica que no se utilizó el "
"zoom digital."

#: libexif/exif-tag.c:811
#, fuzzy
msgid "Focal Length in 35mm Film"
msgstr "Distancia focal en película de 35mm"

#: libexif/exif-tag.c:812
msgid ""
"This tag indicates the equivalent focal length assuming a 35mm film camera, "
"in mm. A value of 0 means the focal length is unknown. Note that this tag "
"differs from the FocalLength tag."
msgstr ""
"Esta etiqueta indica la distancia focal equivalente asumiendo una cámara de "
"película de 35mm, en mm. Un valor de 0 significa que se desconoce la "
"distancia focal. Note que esta etiqueta difiere de la etiqueta FocalLength."

#: libexif/exif-tag.c:818
msgid "Scene Capture Type"
msgstr "Tipo de captura de escena"

#: libexif/exif-tag.c:819
msgid ""
"This tag indicates the type of scene that was shot. It can also be used to "
"record the mode in which the image was shot. Note that this differs from the "
"scene type <SceneType> tag."
msgstr ""
"Esta etiqueta indica el tipo de escena que se tomó. También se puede "
"utilizar para registrar el modo en el cual se tomó la imagen. Note que esto "
"difiere de la etiqueta SceneType."

#: libexif/exif-tag.c:824
msgid "Gain Control"
msgstr "Control de ganancia"

#: libexif/exif-tag.c:825
msgid "This tag indicates the degree of overall image gain adjustment."
msgstr ""
"Esta etiqueta indica el grado del ajuste de ganancia general de imagen."

#: libexif/exif-tag.c:829
msgid ""
"This tag indicates the direction of contrast processing applied by the "
"camera when the image was shot."
msgstr ""
"Esta etiqueta indica la dirección del procesamiento de contraste aplicado "
"por la cámara cuando se tomó la imagen."

#: libexif/exif-tag.c:833
msgid ""
"This tag indicates the direction of saturation processing applied by the "
"camera when the image was shot."
msgstr ""
"Esta etiqueta indica la dirección del procesamiento de stauración aplicado "
"por la cámara cuando se tomó la imagen."

#: libexif/exif-tag.c:837
msgid ""
"This tag indicates the direction of sharpness processing applied by the "
"camera when the image was shot."
msgstr ""
"Esta etiqueta indica la dirección del procesamiento de nitidez aplicado por "
"la cámara cuando se tomó la imagen."

#: libexif/exif-tag.c:841
msgid "Device Setting Description"
msgstr "Descripción de ajuste del dispositivo"

#: libexif/exif-tag.c:842
msgid ""
"This tag indicates information on the picture-taking conditions of a "
"particular camera model. The tag is used only to indicate the picture-taking "
"conditions in the reader."
msgstr ""
"Esta etiqueta indica información sobre las condiciones de toma de la foto de "
"un modelo de cámara en particular. La etiqueta sólo se usa para indicar las "
"condiciones de toma de fotos en el lector."

#: libexif/exif-tag.c:848
msgid "Subject Distance Range"
msgstr "Rango de distancia al sujeto."

#: libexif/exif-tag.c:849
msgid "This tag indicates the distance to the subject."
msgstr "Esta etiqueta indica la distancia al sujeto."

#: libexif/exif-tag.c:851
msgid "Image Unique ID"
msgstr "ID único de imagen"

#: libexif/exif-tag.c:852
msgid ""
"This tag indicates an identifier assigned uniquely to each image. It is "
"recorded as an ASCII string equivalent to hexadecimal notation and 128-bit "
"fixed length."
msgstr ""
"Esta etiqueta indica un identificador asignado unívocamente a cada imagen. "
"Se registra como una cadena de caracteres ASCII equivalente a notación "
"hexadecimal y de una longitud fija de 128 bits."

#: libexif/exif-tag.c:857
msgid "Gamma"
msgstr ""

#: libexif/exif-tag.c:858
msgid "Indicates the value of coefficient gamma."
msgstr ""

#: libexif/exif-tag.c:860
msgid "PRINT Image Matching"
msgstr ""

#: libexif/exif-tag.c:861
msgid "Related to Epson's PRINT Image Matching technology"
msgstr ""

#: libexif/exif-tag.c:863
msgid "Padding"
msgstr ""

#: libexif/exif-tag.c:864
msgid ""
"This tag reserves space that can be reclaimed later when additional metadata "
"are added. New metadata can be written in place by replacing this tag with a "
"smaller data element and using the reclaimed space to store the new or "
"expanded metadata tags."
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:62
#, fuzzy
msgid "Softest"
msgstr "Suave"

#: libexif/fuji/mnote-fuji-entry.c:66
#, fuzzy
msgid "Hardest"
msgstr "Duro"

#: libexif/fuji/mnote-fuji-entry.c:67 libexif/fuji/mnote-fuji-entry.c:96
msgid "Medium soft"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:68 libexif/fuji/mnote-fuji-entry.c:94
msgid "Medium hard"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:69 libexif/fuji/mnote-fuji-entry.c:90
#: libexif/fuji/mnote-fuji-entry.c:98 libexif/fuji/mnote-fuji-entry.c:182
msgid "Film simulation mode"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:79
msgid "Incandescent"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:85
msgid "Medium high"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:87
msgid "Medium low"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:88 libexif/fuji/mnote-fuji-entry.c:97
msgid "Original"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:124 libexif/pentax/mnote-pentax-entry.c:164
#: libexif/pentax/mnote-pentax-entry.c:299
#, fuzzy
msgid "Program AE"
msgstr "Programa normal"

#: libexif/fuji/mnote-fuji-entry.c:125
#, fuzzy
msgid "Natural photo"
msgstr "Saturación"

#: libexif/fuji/mnote-fuji-entry.c:126
#, fuzzy
msgid "Vibration reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/fuji/mnote-fuji-entry.c:127
msgid "Sunset"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:128 libexif/pentax/mnote-pentax-entry.c:181
msgid "Museum"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:129
#, fuzzy
msgid "Party"
msgstr "Parcial"

#: libexif/fuji/mnote-fuji-entry.c:130
msgid "Flower"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:131 libexif/pentax/mnote-pentax-entry.c:176
msgid "Text"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:132
#, fuzzy
msgid "NP & flash"
msgstr "Flash"

#: libexif/fuji/mnote-fuji-entry.c:137
#, fuzzy
msgid "Aperture priority AE"
msgstr "Prioridad de apertura"

#: libexif/fuji/mnote-fuji-entry.c:138
#, fuzzy
msgid "Shutter priority AE"
msgstr "Prioridad del obturador"

#: libexif/fuji/mnote-fuji-entry.c:146
#, fuzzy
msgid "F-Standard"
msgstr "Estándar"

#: libexif/fuji/mnote-fuji-entry.c:147
msgid "F-Chrome"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:148
msgid "F-B&W"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:151
msgid "No blur"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:152
msgid "Blur warning"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:155
#, fuzzy
msgid "Focus good"
msgstr "Modo de exposición"

#: libexif/fuji/mnote-fuji-entry.c:156
msgid "Out of focus"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:159
msgid "AE good"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:160
msgid "Over exposed"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:164
msgid "Wide"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:167
#, fuzzy
msgid "F0/Standard"
msgstr "Estándar"

#: libexif/fuji/mnote-fuji-entry.c:168
#, fuzzy
msgid "F1/Studio portrait"
msgstr "retrato"

#: libexif/fuji/mnote-fuji-entry.c:169
msgid "F1a/Professional portrait"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:170
msgid "F1b/Professional portrait"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:171
msgid "F1c/Professional portrait"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:172
msgid "F2/Fujichrome"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:173
msgid "F3/Studio portrait Ex"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:174
msgid "F4/Velvia"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:177
msgid "Auto (100-400%)"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:179
#, fuzzy
msgid "Standard (100%)"
msgstr "Estándar"

#: libexif/fuji/mnote-fuji-entry.c:180
msgid "Wide1 (230%)"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:181
msgid "Wide2 (400%)"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:263
#, c-format
msgid "%2.2f mm"
msgstr ""

#: libexif/fuji/mnote-fuji-entry.c:298 libexif/pentax/mnote-pentax-entry.c:399
#: libexif/pentax/mnote-pentax-entry.c:451
#, c-format
msgid "%i bytes unknown data"
msgstr "%i bytes de datos desconocidos"

#: libexif/fuji/mnote-fuji-tag.c:36
#, fuzzy
msgid "Maker Note Version"
msgstr "Nota del fabricante"

#: libexif/fuji/mnote-fuji-tag.c:37
msgid "This number is unique and based on the date of manufacture."
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:41
#, fuzzy
msgid "Chromaticity Saturation"
msgstr "Saturación"

#: libexif/fuji/mnote-fuji-tag.c:44
msgid "Flash Firing Strength Compensation"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:46
#, fuzzy
msgid "Focusing Mode"
msgstr "Modo de exposición"

#: libexif/fuji/mnote-fuji-tag.c:47
#, fuzzy
msgid "Focus Point"
msgstr "Modo de exposición"

#: libexif/fuji/mnote-fuji-tag.c:48
msgid "Slow Synchro Mode"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:49 libexif/pentax/mnote-pentax-tag.c:72
#, fuzzy
msgid "Picture Mode"
msgstr "Modo de exposición"

#: libexif/fuji/mnote-fuji-tag.c:50
msgid "Continuous Taking"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:51
msgid "Continuous Sequence Number"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:52
msgid "FinePix Color"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:53
msgid "Blur Check"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:54
msgid "Auto Focus Check"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:55
#, fuzzy
msgid "Auto Exposure Check"
msgstr "Exposición automática"

#: libexif/fuji/mnote-fuji-tag.c:56
msgid "Dynamic Range"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:57
msgid "Film Simulation Mode"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:58
msgid "Dynamic Range Wide Mode"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:59
msgid "Development Dynamic Range Wide Mode"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:60
#, fuzzy
msgid "Minimum Focal Length"
msgstr "Distancia focal"

#: libexif/fuji/mnote-fuji-tag.c:61
#, fuzzy
msgid "Maximum Focal Length"
msgstr "Distancia focal"

#: libexif/fuji/mnote-fuji-tag.c:62
msgid "Maximum Aperture at Minimum Focal"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:63
msgid "Maximum Aperture at Maximum Focal"
msgstr ""

#: libexif/fuji/mnote-fuji-tag.c:65
#, fuzzy
msgid "Order Number"
msgstr "El número F."

#: libexif/fuji/mnote-fuji-tag.c:66 libexif/pentax/mnote-pentax-tag.c:98
#, fuzzy
msgid "Frame Number"
msgstr "El número F."

#: libexif/olympus/mnote-olympus-entry.c:49
#, fuzzy, c-format
msgid "Invalid format '%s', expected '%s' or '%s'."
msgstr "Formato no válido '%s', se esperaba '%s'."

#: libexif/olympus/mnote-olympus-entry.c:92
msgid "AF non D lens"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:94
msgid "AF-D or AF-S lens"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:95
msgid "AF-D G lens"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:96
msgid "AF-D VR lens"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:97
msgid "AF-D G VR lens"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:101
msgid "Flash unit unknown"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:102
msgid "Flash is external"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:103
#, fuzzy
msgid "Flash is on camera"
msgstr "Energía del flash"

#: libexif/olympus/mnote-olympus-entry.c:106
msgid "VGA basic"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:107
#, fuzzy
msgid "VGA normal"
msgstr "normal"

#: libexif/olympus/mnote-olympus-entry.c:108
msgid "VGA fine"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:109
msgid "SXGA basic"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:110
#, fuzzy
msgid "SXGA normal"
msgstr "normal"

#: libexif/olympus/mnote-olympus-entry.c:111
msgid "SXGA fine"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:112
msgid "2 Mpixel basic"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:113
msgid "2 Mpixel normal"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:114
msgid "2 Mpixel fine"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:117
#, fuzzy
msgid "Color"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-entry.c:122
msgid "Bright+"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:123
msgid "Bright-"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:124
#, fuzzy
msgid "Contrast+"
msgstr "Contraste"

#: libexif/olympus/mnote-olympus-entry.c:125
#, fuzzy
msgid "Contrast-"
msgstr "Contraste"

#: libexif/olympus/mnote-olympus-entry.c:128
msgid "ISO 80"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:129
msgid "ISO 160"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:130
msgid "ISO 320"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:131
#: libexif/olympus/mnote-olympus-entry.c:249
msgid "ISO 100"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:135
msgid "Preset"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:137
msgid "Incandescence"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:138
#, fuzzy
msgid "Fluorescence"
msgstr "Fluorescente"

#: libexif/olympus/mnote-olympus-entry.c:140
msgid "SpeedLight"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:143
msgid "No fisheye"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:144
msgid "Fisheye on"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:147
#, fuzzy
msgid "Normal, SQ"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:148
#, fuzzy
msgid "Normal, HQ"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:149
#, fuzzy
msgid "Normal, SHQ"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:150
#, fuzzy
msgid "Normal, RAW"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:151
#, fuzzy
msgid "Normal, SQ1"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:152
#, fuzzy
msgid "Normal, SQ2"
msgstr "Normal"

#: libexif/olympus/mnote-olympus-entry.c:153
#, fuzzy
msgid "Normal, super high"
msgstr "Programa normal"

#: libexif/olympus/mnote-olympus-entry.c:154
msgid "Normal, standard"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:155
#, fuzzy
msgid "Fine, SQ"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:156
#, fuzzy
msgid "Fine, HQ"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:157
#, fuzzy
msgid "Fine, SHQ"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:158
#, fuzzy
msgid "Fine, RAW"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:159
#, fuzzy
msgid "Fine, SQ1"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:160
#, fuzzy
msgid "Fine, SQ2"
msgstr "pulg"

#: libexif/olympus/mnote-olympus-entry.c:161
msgid "Fine, super high"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:162
msgid "Super fine, SQ"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:163
msgid "Super fine, HQ"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:164
msgid "Super fine, SHQ"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:165
msgid "Super fine, RAW"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:166
msgid "Super fine, SQ1"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:167
msgid "Super fine, SQ2"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:168
msgid "Super fine, super high"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:169
msgid "Super fine, high"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:172
#: libexif/olympus/mnote-olympus-entry.c:177
#: libexif/olympus/mnote-olympus-entry.c:211
#: libexif/olympus/mnote-olympus-entry.c:220
#: libexif/olympus/mnote-olympus-entry.c:243
msgid "No"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:183
msgid "On (Preset)"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:188
msgid "Fill"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:195
#, fuzzy
msgid "Internal + external"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-entry.c:224
#, fuzzy
msgid "Interlaced"
msgstr "centrado"

#: libexif/olympus/mnote-olympus-entry.c:225
msgid "Progressive"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:231
#: libexif/pentax/mnote-pentax-entry.c:85
#: libexif/pentax/mnote-pentax-entry.c:139
msgid "Best"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:232
#, fuzzy
msgid "Adjust exposure"
msgstr "Exposición automática"

#: libexif/olympus/mnote-olympus-entry.c:235
#, fuzzy
msgid "Spot focus"
msgstr "Exposición manual"

#: libexif/olympus/mnote-olympus-entry.c:236
#, fuzzy
msgid "Normal focus"
msgstr "Proceso normal"

#: libexif/olympus/mnote-olympus-entry.c:239
msgid "Record while down"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:240
msgid "Press start, press stop"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:248
msgid "ISO 50"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:250
msgid "ISO 200"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:251
msgid "ISO 400"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:255
#: libexif/pentax/mnote-pentax-entry.c:168
#, fuzzy
msgid "Sport"
msgstr "Lugar"

#: libexif/olympus/mnote-olympus-entry.c:256
msgid "TV"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:258
msgid "User 1"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:259
msgid "User 2"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:260
msgid "Lamp"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:263
msgid "5 frames/sec"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:264
msgid "10 frames/sec"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:265
msgid "15 frames/sec"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:266
msgid "20 frames/sec"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:381
#, c-format
msgid "Red Correction %f, blue Correction %f"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:388
#, fuzzy
msgid "No manual focus selection"
msgstr "Balance de blanco manual"

#: libexif/olympus/mnote-olympus-entry.c:391
#, c-format
msgid "%2.2f meters"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:417
msgid "AF position: center"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:418
msgid "AF position: top"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:419
#, fuzzy
msgid "AF position: bottom"
msgstr "izquierda - abajo"

#: libexif/olympus/mnote-olympus-entry.c:420
msgid "AF position: left"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:421
msgid "AF position: right"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:422
msgid "AF position: upper-left"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:423
msgid "AF position: upper-right"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:424
msgid "AF position: lower-left"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:425
msgid "AF position: lower-right"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:426
msgid "AF position: far left"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:427
msgid "AF position: far right"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:428
#, fuzzy
msgid "Unknown AF position"
msgstr "Versión Exif"

#: libexif/olympus/mnote-olympus-entry.c:439
#: libexif/olympus/mnote-olympus-entry.c:509
#, c-format
msgid "Internal error (unknown value %hi)"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:447
#: libexif/olympus/mnote-olympus-entry.c:517
#, c-format
msgid "Unknown value %hi"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:542
#: libexif/olympus/mnote-olympus-entry.c:562
#, fuzzy, c-format
msgid "Unknown %hu"
msgstr "Desconocido"

#: libexif/olympus/mnote-olympus-entry.c:559
#, fuzzy
msgid "2 sec."
msgstr " seg."

#: libexif/olympus/mnote-olympus-entry.c:598
msgid "Fast"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:702
msgid "Automatic"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:732
#, fuzzy, c-format
msgid "Manual: %liK"
msgstr "Manual"

#: libexif/olympus/mnote-olympus-entry.c:735
#, fuzzy
msgid "Manual: unknown"
msgstr "desconocido"

#: libexif/olympus/mnote-olympus-entry.c:741
msgid "One-touch"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:797
#: libexif/olympus/mnote-olympus-entry.c:807
msgid "Infinite"
msgstr ""

#: libexif/olympus/mnote-olympus-entry.c:815
#, c-format
msgid "%i bytes unknown data: "
msgstr "%i bytes de datos desconocidos: "

#: libexif/olympus/mnote-olympus-tag.c:38
#: libexif/olympus/mnote-olympus-tag.c:53
#, fuzzy
msgid "ISO Setting"
msgstr "Velocidad ISO"

#: libexif/olympus/mnote-olympus-tag.c:39
#, fuzzy
msgid "Color Mode (?)"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-tag.c:42
#, fuzzy
msgid "Image Sharpening"
msgstr "Longitud de la imagen"

#: libexif/olympus/mnote-olympus-tag.c:44
#, fuzzy
msgid "Flash Setting"
msgstr "Contraste"

#: libexif/olympus/mnote-olympus-tag.c:46
#, fuzzy
msgid "White Balance Fine Adjustment"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:47
#, fuzzy
msgid "White Balance RB"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:49
#, fuzzy
msgid "ISO Selection"
msgstr "Velocidad ISO"

#: libexif/olympus/mnote-olympus-tag.c:50
msgid "Preview Image IFD"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:50
msgid "Offset of the preview image directory (IFD) inside the file."
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:51
#, fuzzy
msgid "Exposurediff ?"
msgstr "Modo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:54
msgid "Image Boundary"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:56
#, fuzzy
msgid "Flash Exposure Bracket Value"
msgstr "Modo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:57
#, fuzzy
msgid "Exposure Bracket Value"
msgstr "Auto bracket"

#: libexif/olympus/mnote-olympus-tag.c:58
#: libexif/olympus/mnote-olympus-tag.c:96
msgid "Image Adjustment"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:59
#, fuzzy
msgid "Tone Compensation"
msgstr "Tiempo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:60
msgid "Adapter"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:62
msgid "Lens"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:63
#: libexif/olympus/mnote-olympus-tag.c:135
#: libexif/olympus/mnote-olympus-tag.c:185
#, fuzzy
msgid "Manual Focus Distance"
msgstr "Balance de blanco manual"

#: libexif/olympus/mnote-olympus-tag.c:65
#, fuzzy
msgid "Flash Used"
msgstr "El flash disparó."

#: libexif/olympus/mnote-olympus-tag.c:66
#, fuzzy
msgid "AF Focus Position"
msgstr "Modo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:67
msgid "Bracketing"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:69
#, fuzzy
msgid "Lens F Stops"
msgstr "Tipo de escena"

#: libexif/olympus/mnote-olympus-tag.c:70
#, fuzzy
msgid "Contrast Curve"
msgstr "Contraste"

#: libexif/olympus/mnote-olympus-tag.c:71
#: libexif/olympus/mnote-olympus-tag.c:95
#: libexif/pentax/mnote-pentax-tag.c:134
#, fuzzy
msgid "Color Mode"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-tag.c:72
#, fuzzy
msgid "Light Type"
msgstr "Fuente de luz"

#: libexif/olympus/mnote-olympus-tag.c:74
msgid "Hue Adjustment"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:76
#: libexif/olympus/mnote-olympus-tag.c:163
#: libexif/pentax/mnote-pentax-tag.c:108
#, fuzzy
msgid "Noise Reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/olympus/mnote-olympus-tag.c:79
msgid "Sensor Pixel Size"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:82
#, fuzzy
msgid "Image Data Size"
msgstr "Ancho de la imagen"

#: libexif/olympus/mnote-olympus-tag.c:82
msgid "Size of compressed image data in bytes."
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:84
msgid "Total Number of Pictures Taken"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:86
msgid "Optimize Image"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:88
#, fuzzy
msgid "Vari Program"
msgstr "Programa normal"

#: libexif/olympus/mnote-olympus-tag.c:89
msgid "Capture Editor Data"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:90
msgid "Capture Editor Version"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:97
#: libexif/olympus/mnote-olympus-tag.c:183
#, fuzzy
msgid "CCD Sensitivity"
msgstr "Sensibilidad espectral"

#: libexif/olympus/mnote-olympus-tag.c:99
#, fuzzy
msgid "Focus"
msgstr "Modo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:102
#, fuzzy
msgid "Converter"
msgstr "Centímetro"

#: libexif/olympus/mnote-olympus-tag.c:105
msgid "Thumbnail Image"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:106
msgid "Speed/Sequence/Panorama Direction"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:109
#, fuzzy
msgid "Black & White Mode"
msgstr "Modelo"

#: libexif/olympus/mnote-olympus-tag.c:111
#, fuzzy
msgid "Focal Plane Diagonal"
msgstr "Resolución X del plano focal"

#: libexif/olympus/mnote-olympus-tag.c:112
msgid "Lens Distortion Parameters"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:114
msgid "Info"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:115
msgid "Camera ID"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:116
msgid "Precapture Frames"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:117
#, fuzzy
msgid "White Board"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:118
#, fuzzy
msgid "One Touch White Balance"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:119
#, fuzzy
msgid "White Balance Bracket"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:120
#: libexif/pentax/mnote-pentax-tag.c:123
#, fuzzy
msgid "White Balance Bias"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:121
msgid "Data Dump"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:124
msgid "ISO Value"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:125
#, fuzzy
msgid "Aperture Value"
msgstr "Apertura"

#: libexif/olympus/mnote-olympus-tag.c:126
#, fuzzy
msgid "Brightness Value"
msgstr "Brillo"

#: libexif/olympus/mnote-olympus-tag.c:128
#, fuzzy
msgid "Flash Device"
msgstr "El flash disparó."

#: libexif/olympus/mnote-olympus-tag.c:130
msgid "Sensor Temperature"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:131
msgid "Lens Temperature"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:132
msgid "Light Condition"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:136
msgid "Zoom Step Count"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:137
msgid "Focus Step Count"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:138
#, fuzzy
msgid "Sharpness Setting"
msgstr "Nitidez"

#: libexif/olympus/mnote-olympus-tag.c:139
msgid "Flash Charge Level"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:140
#, fuzzy
msgid "Color Matrix"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-tag.c:141
#, fuzzy
msgid "Black Level"
msgstr "Nivel de la batería"

#: libexif/olympus/mnote-olympus-tag.c:142
#, fuzzy
msgid "White Balance Setting"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:143
#: libexif/pentax/mnote-pentax-tag.c:87
#, fuzzy
msgid "Red Balance"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:144
#: libexif/pentax/mnote-pentax-tag.c:86
#, fuzzy
msgid "Blue Balance"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:145
msgid "Color Matrix Number"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:147
#, fuzzy
msgid "Flash Exposure Comp"
msgstr "Modo de exposición"

#: libexif/olympus/mnote-olympus-tag.c:148
#, fuzzy
msgid "Internal Flash Table"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-tag.c:149
#, fuzzy
msgid "External Flash G Value"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-tag.c:150
#, fuzzy
msgid "External Flash Bounce"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-tag.c:151
#, fuzzy
msgid "External Flash Zoom"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-tag.c:152
#, fuzzy
msgid "External Flash Mode"
msgstr "Flash"

#: libexif/olympus/mnote-olympus-tag.c:153
#, fuzzy
msgid "Contrast Setting"
msgstr "Contraste"

#: libexif/olympus/mnote-olympus-tag.c:154
#, fuzzy
msgid "Sharpness Factor"
msgstr "Nitidez"

#: libexif/olympus/mnote-olympus-tag.c:155
#, fuzzy
msgid "Color Control"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-tag.c:156
#, fuzzy
msgid "Olympus Image Width"
msgstr "Ancho de la imagen"

#: libexif/olympus/mnote-olympus-tag.c:157
msgid "Olympus Image Height"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:158
#, fuzzy
msgid "Scene Detect"
msgstr "Tipo de escena"

#: libexif/olympus/mnote-olympus-tag.c:159
#, fuzzy
msgid "Compression Ratio"
msgstr "Compresión"

#: libexif/olympus/mnote-olympus-tag.c:160
msgid "Preview Image Valid"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:161
msgid "AF Result"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:162
msgid "CCD Scan Mode"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:164
msgid "Infinity Lens Step"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:165
#, fuzzy
msgid "Near Lens Step"
msgstr "Tipo de escena"

#: libexif/olympus/mnote-olympus-tag.c:166
#, fuzzy
msgid "Light Value Center"
msgstr "abajo centrado"

#: libexif/olympus/mnote-olympus-tag.c:167
msgid "Light Value Periphery"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:170
msgid "Sequential Shot"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:171
#, fuzzy
msgid "Wide Range"
msgstr "Balance de blanco"

#: libexif/olympus/mnote-olympus-tag.c:172
#, fuzzy
msgid "Color Adjustment Mode"
msgstr "Espacio de color"

#: libexif/olympus/mnote-olympus-tag.c:174
#, fuzzy
msgid "Quick Shot"
msgstr "Multi Lugar"

#: libexif/olympus/mnote-olympus-tag.c:176
msgid "Voice Memo"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:177
msgid "Record Shutter Release"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:178
msgid "Flicker Reduce"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:179
#, fuzzy
msgid "Optical Zoom"
msgstr "Relación de zoom digital"

#: libexif/olympus/mnote-olympus-tag.c:181
#, fuzzy
msgid "Light Source Special"
msgstr "Fuente de luz"

#: libexif/olympus/mnote-olympus-tag.c:182
#, fuzzy
msgid "Resaved"
msgstr "reservado"

#: libexif/olympus/mnote-olympus-tag.c:184
#, fuzzy
msgid "Scene Select"
msgstr "Tipo de escena"

#: libexif/olympus/mnote-olympus-tag.c:186
msgid "Sequence Shot Interval"
msgstr ""

#: libexif/olympus/mnote-olympus-tag.c:189
#, fuzzy
msgid "Epson Image Width"
msgstr "Ancho de la imagen"

#: libexif/olympus/mnote-olympus-tag.c:190
#, fuzzy
msgid "Epson Image Height"
msgstr "Longitud de la imagen"

#: libexif/olympus/mnote-olympus-tag.c:191
#, fuzzy
msgid "Epson Software Version"
msgstr "Nota del fabricante"

#: libexif/pentax/mnote-pentax-entry.c:80
#: libexif/pentax/mnote-pentax-entry.c:134
#, fuzzy
msgid "Multi-exposure"
msgstr "Exposición manual"

#: libexif/pentax/mnote-pentax-entry.c:83
#: libexif/pentax/mnote-pentax-entry.c:137
msgid "Good"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:84
#: libexif/pentax/mnote-pentax-entry.c:138
#, fuzzy
msgid "Better"
msgstr "Centímetro"

#: libexif/pentax/mnote-pentax-entry.c:92
#, fuzzy
msgid "Flash on"
msgstr "Flash"

#: libexif/pentax/mnote-pentax-entry.c:140
msgid "TIFF"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:150
msgid "2560x1920 or 2304x1728"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:156
msgid "2304x1728 or 2592x1944"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:158
msgid "2816x2212 or 2816x2112"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:171
msgid "Surf & snow"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:172
#, fuzzy
msgid "Sunset or candlelight"
msgstr "Luz de día"

#: libexif/pentax/mnote-pentax-entry.c:173
msgid "Autumn"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:178
#, fuzzy
msgid "Self portrait"
msgstr "Retrato"

#: libexif/pentax/mnote-pentax-entry.c:179
#, fuzzy
msgid "Illustrations"
msgstr "Saturación"

#: libexif/pentax/mnote-pentax-entry.c:180
#, fuzzy
msgid "Digital filter"
msgstr "Relación de zoom digital"

#: libexif/pentax/mnote-pentax-entry.c:182
msgid "Food"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:183
#, fuzzy
msgid "Green mode"
msgstr "Modo de métrica"

#: libexif/pentax/mnote-pentax-entry.c:184
#, fuzzy
msgid "Light pet"
msgstr "Fuente de luz"

#: libexif/pentax/mnote-pentax-entry.c:185
msgid "Dark pet"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:186
msgid "Medium pet"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:188
#: libexif/pentax/mnote-pentax-entry.c:296
#, fuzzy
msgid "Candlelight"
msgstr "Luz de día"

#: libexif/pentax/mnote-pentax-entry.c:189
#, fuzzy
msgid "Natural skin tone"
msgstr "Saturación"

#: libexif/pentax/mnote-pentax-entry.c:190
msgid "Synchro sound record"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:191
msgid "Frame composite"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:194
#, fuzzy
msgid "Auto, did not fire"
msgstr "El flash no disparó."

#: libexif/pentax/mnote-pentax-entry.c:196
#, fuzzy
msgid "Auto, did not fire, red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/pentax/mnote-pentax-entry.c:197
#, fuzzy
msgid "Auto, fired"
msgstr "Auto bracket"

#: libexif/pentax/mnote-pentax-entry.c:199
#, fuzzy
msgid "Auto, fired, red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/pentax/mnote-pentax-entry.c:201
msgid "On, wireless"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:202
#, fuzzy
msgid "On, soft"
msgstr "Suave"

#: libexif/pentax/mnote-pentax-entry.c:203
msgid "On, slow-sync"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:204
#, fuzzy
msgid "On, slow-sync, red-eye reduction"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/pentax/mnote-pentax-entry.c:205
msgid "On, trailing-curtain sync"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:213
msgid "AF-S"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:214
msgid "AF-C"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:217
#, fuzzy
msgid "Upper-left"
msgstr "arriba - izquierda"

#: libexif/pentax/mnote-pentax-entry.c:218
msgid "Top"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:219
#, fuzzy
msgid "Upper-right"
msgstr "arriba - derecha"

#: libexif/pentax/mnote-pentax-entry.c:221
msgid "Mid-left"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:223
#, fuzzy
msgid "Mid-right"
msgstr "Copyright"

#: libexif/pentax/mnote-pentax-entry.c:225
#, fuzzy
msgid "Lower-left"
msgstr "arriba - izquierda"

#: libexif/pentax/mnote-pentax-entry.c:226
#, fuzzy
msgid "Bottom"
msgstr "abajo - izquierda"

#: libexif/pentax/mnote-pentax-entry.c:227
#, fuzzy
msgid "Lower-right"
msgstr "arriba - derecha"

#: libexif/pentax/mnote-pentax-entry.c:228
#, fuzzy
msgid "Fixed center"
msgstr "Tiempo bueno"

#: libexif/pentax/mnote-pentax-entry.c:232
#, fuzzy
msgid "Multiple"
msgstr "Multi Lugar"

#: libexif/pentax/mnote-pentax-entry.c:234
#, fuzzy
msgid "Top-center"
msgstr "centrado"

#: libexif/pentax/mnote-pentax-entry.c:240
#, fuzzy
msgid "Bottom-center"
msgstr "centrado"

#: libexif/pentax/mnote-pentax-entry.c:257
msgid "User selected"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:282
msgid "3008x2008 or 3040x2024"
msgstr ""

#: libexif/pentax/mnote-pentax-entry.c:293
#, fuzzy
msgid "Digital filter?"
msgstr "Relación de zoom digital"

#: libexif/pentax/mnote-pentax-entry.c:374
#: libexif/pentax/mnote-pentax-entry.c:383
#, c-format
msgid "Internal error (unknown value %i %i)"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:35 libexif/pentax/mnote-pentax-tag.c:63
#, fuzzy
msgid "Capture Mode"
msgstr "Modo de exposición"

#: libexif/pentax/mnote-pentax-tag.c:36 libexif/pentax/mnote-pentax-tag.c:70
#: libexif/pentax/mnote-pentax-tag.c:129
msgid "Quality Level"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:54
#, fuzzy
msgid "ISO Speed"
msgstr "Velocidad ISO"

#: libexif/pentax/mnote-pentax-tag.c:56
#, fuzzy
msgid "Colors"
msgstr "Espacio de color"

#: libexif/pentax/mnote-pentax-tag.c:59
msgid "PrintIM Settings"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:60 libexif/pentax/mnote-pentax-tag.c:131
msgid "Time Zone"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:61
#, fuzzy
msgid "Daylight Savings"
msgstr "Luz de día"

#: libexif/pentax/mnote-pentax-tag.c:64
msgid "Preview Size"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:65
#, fuzzy
msgid "Preview Length"
msgstr "Longitud de la imagen"

#: libexif/pentax/mnote-pentax-tag.c:66 libexif/pentax/mnote-pentax-tag.c:122
msgid "Preview Start"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:67
msgid "Model Identification"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:68
msgid "Date"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:69
msgid "Time"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:75
#, fuzzy
msgid "AF Point Selected"
msgstr "Balance de blanco manual"

#: libexif/pentax/mnote-pentax-tag.c:76
msgid "Auto AF Point"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:77
#, fuzzy
msgid "Focus Position"
msgstr "Modo de exposición"

#: libexif/pentax/mnote-pentax-tag.c:80
#, fuzzy
msgid "ISO Number"
msgstr "El número F."

#: libexif/pentax/mnote-pentax-tag.c:83
#, fuzzy
msgid "Auto Bracketing"
msgstr "Auto bracket"

#: libexif/pentax/mnote-pentax-tag.c:85
#, fuzzy
msgid "White Balance Mode"
msgstr "Balance de blanco"

#: libexif/pentax/mnote-pentax-tag.c:93
msgid "World Time Location"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:94
msgid "Hometown City"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:95
msgid "Destination City"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:96
msgid "Hometown DST"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:96
#, fuzzy
msgid "Home Daylight Savings Time"
msgstr "Luz de día"

#: libexif/pentax/mnote-pentax-tag.c:97
msgid "Destination DST"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:97
#, fuzzy
msgid "Destination Daylight Savings Time"
msgstr "Luz de día"

#: libexif/pentax/mnote-pentax-tag.c:99
#, fuzzy
msgid "Image Processing"
msgstr "Longitud de la imagen"

#: libexif/pentax/mnote-pentax-tag.c:100
#, fuzzy
msgid "Picture Mode (2)"
msgstr "Modo de exposición"

#: libexif/pentax/mnote-pentax-tag.c:103
msgid "Image Area Offset"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:104
#, fuzzy
msgid "Raw Image Size"
msgstr "Ancho de la imagen"

#: libexif/pentax/mnote-pentax-tag.c:105
#, fuzzy
msgid "Autofocus Points Used"
msgstr "Modo de exposición"

#: libexif/pentax/mnote-pentax-tag.c:107
#, fuzzy
msgid "Camera Temperature"
msgstr "apertura"

#: libexif/pentax/mnote-pentax-tag.c:110
#, fuzzy
msgid "Image Tone"
msgstr "ID único de imagen"

#: libexif/pentax/mnote-pentax-tag.c:111
#, fuzzy
msgid "Shake Reduction Info"
msgstr "El flash disparó, modo de reducción de ojos rojos."

#: libexif/pentax/mnote-pentax-tag.c:112
#, fuzzy
msgid "Black Point"
msgstr "Modo de exposición"

#: libexif/pentax/mnote-pentax-tag.c:114
msgid "AE Info"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:115
msgid "Lens Info"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:116
#, fuzzy
msgid "Flash Info"
msgstr "Flash"

#: libexif/pentax/mnote-pentax-tag.c:117
msgid "Camera Info"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:118
#, fuzzy
msgid "Battery Info"
msgstr "Nivel de la batería"

#: libexif/pentax/mnote-pentax-tag.c:119
msgid "Hometown City Code"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:120
msgid "Destination City Code"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:125
#, fuzzy
msgid "Object Distance"
msgstr "Distancia del sujeto"

#: libexif/pentax/mnote-pentax-tag.c:125
#, fuzzy
msgid "Distance of photographed object in millimeters."
msgstr "La distancia al sujeto, dada en metros."

#: libexif/pentax/mnote-pentax-tag.c:126
#, fuzzy
msgid "Flash Distance"
msgstr "El flash disparó."

#: libexif/pentax/mnote-pentax-tag.c:132
#, fuzzy
msgid "Bestshot Mode"
msgstr "Flash"

#: libexif/pentax/mnote-pentax-tag.c:133
#, fuzzy
msgid "CCS ISO Sensitivity"
msgstr "Sensibilidad espectral"

#: libexif/pentax/mnote-pentax-tag.c:135
msgid "Enhancement"
msgstr ""

#: libexif/pentax/mnote-pentax-tag.c:136
#, fuzzy
msgid "Finer"
msgstr "pulg"

# This is a very special string. It is used for test purposes, and
# we only test the de locale as a proof-of-concept example. There is
# no need for anybody to translate it.
#: test/nls/test-nls.c:20 test/nls/test-nls.c:23 test/nls/test-nls.c:24
msgid "[DO_NOT_TRANSLATE_THIS_MARKER]"
msgstr ""

#, fuzzy
#~ msgid "On + Red-eye reduction"
#~ msgstr "El flash disparó, modo de reducción de ojos rojos."

#, fuzzy
#~ msgid "Center + Right"
#~ msgstr "peso centrado"

#, fuzzy
#~ msgid "Left + Right"
#~ msgstr "abajo - derecha"

#, fuzzy
#~ msgid "Daylight Fluorescent"
#~ msgstr "Luz de día fluorescente"

#~ msgid "1/%d"
#~ msgstr "1/%d"

#, fuzzy
#~ msgid "Focal length"
#~ msgstr "Distancia focal"

#, fuzzy
#~ msgid "Flash mode"
#~ msgstr "Flash"

#, fuzzy
#~ msgid "Focus mode"
#~ msgstr "Modo de exposición"

#, fuzzy
#~ msgid "Image size"
#~ msgstr "Ancho de la imagen"

#, fuzzy
#~ msgid "Digital zoom"
#~ msgstr "Relación de zoom digital"

#, fuzzy
#~ msgid "Metering mode"
#~ msgstr "Modo de métrica"

#, fuzzy
#~ msgid "Focus range"
#~ msgstr "Modo de exposición"

#, fuzzy
#~ msgid "Focal plane y size"
#~ msgstr "Resolución Y del plano focal"

#, fuzzy
#~ msgid "White balance"
#~ msgstr "Balance de blanco"

#~ msgid "Exposure time"
#~ msgstr "Tiempo de exposición"

#~ msgid "top - left"
#~ msgstr "arriba - izquierda"

#~ msgid "top - right"
#~ msgstr "arriba - derecha"

#~ msgid "bottom - right"
#~ msgstr "abajo - derecha"

#~ msgid "bottom - left"
#~ msgstr "abajo - izquierda"

#~ msgid "left - top"
#~ msgstr "izquierda - arriba"

#~ msgid "Center-Weighted Average"
#~ msgstr "Promedio Ponderado en el Centro"

#~ msgid "Flash did not fire."
#~ msgstr "El flash no disparó."

#, fuzzy
#~ msgid "flash"
#~ msgstr "Flash"

#~ msgid "y-Resolution"
#~ msgstr "Resolución Y"

#~ msgid "Shutter speed"
#~ msgstr "Velocidad del obturador"

#~ msgid "Focal Plane y-Resolution"
#~ msgstr "Resolución Y del plano focal"

#, fuzzy
#~ msgid "Daylight-color fluorescent"
#~ msgstr "Luz de día fluorescente"

#, fuzzy
#~ msgid "DayWhite-color fluorescent"
#~ msgstr "Día blanco fluorescente"

#, fuzzy
#~ msgid "Super Macro"
#~ msgstr "Macro"

#~ msgid "unknown"
#~ msgstr "desconocido"

#~ msgid "panorama"
#~ msgstr "panorama"

#, fuzzy
#~ msgid "left to right"
#~ msgstr "abajo - derecha"

#, fuzzy
#~ msgid "right to left"
#~ msgstr "derecha - arriba"

#, fuzzy
#~ msgid "bottom to top"
#~ msgstr "abajo - izquierda"

#, fuzzy
#~ msgid "top to bottom"
#~ msgstr "izquierda - abajo"

#, fuzzy
#~ msgid "Whitebalance"
#~ msgstr "Balance de blanco"

#, fuzzy
#~ msgid "Noisereduction"
#~ msgstr "El flash disparó, modo de reducción de ojos rojos."

#, fuzzy
#~ msgid "Night-scene"
#~ msgstr "Escena nocturna"

#, fuzzy
#~ msgid "Night Scene"
#~ msgstr "Escena nocturna"

#, fuzzy
#~ msgid "Pan Focus"
#~ msgstr "Exposición manual"

#, fuzzy
#~ msgid "Daywhite Fluorescent"
#~ msgstr "Día blanco fluorescente"

#, fuzzy
#~ msgid "White Fluorescent"
#~ msgstr "Blanco fluorescente"

#, fuzzy
#~ msgid "PictureMode"
#~ msgstr "Modo de exposición"

#, fuzzy
#~ msgid "Manual Focus"
#~ msgstr "Exposición manual"

#, fuzzy
#~ msgid ""
#~ "Flash fired, compulsory flash mode, red-eye reduction, return light "
#~ "detected."
#~ msgstr ""
#~ "El flash disparó, modo compulsivo del flash, modo de reducción de ojos "
#~ "rojos, luz de retorno detectada."

#, fuzzy
#~ msgid "Flash not fired"
#~ msgstr "El flash no disparó."

#, fuzzy
#~ msgid "red eyes reduction"
#~ msgstr "El flash disparó, modo de reducción de ojos rojos."

#, fuzzy
#~ msgid "on + red eyes reduction"
#~ msgstr "El flash disparó, modo de reducción de ojos rojos."

#, fuzzy
#~ msgid " / Contrast : "
#~ msgstr "Contraste"

#, fuzzy
#~ msgid " / Saturation : "
#~ msgstr "Saturación"

#, fuzzy
#~ msgid " / Sharpness : "
#~ msgstr "Nitidez"

#, fuzzy
#~ msgid " / Metering mode : "
#~ msgstr "Modo de métrica"

#, fuzzy
#~ msgid " / Exposure mode : "
#~ msgstr "Modo de exposición"

#, fuzzy
#~ msgid " / Focus mode2 : "
#~ msgstr "Modo de exposición"

#, fuzzy
#~ msgid "White balance : "
#~ msgstr "Balance de blanco"

#, fuzzy
#~ msgid "Flourescent"
#~ msgstr "Fluorescente"

#, fuzzy
#~ msgid " / Flash bias : %.2f EV"
#~ msgstr "Flash"

#, fuzzy
#~ msgid " / Subject Distance (mm) : %u"
#~ msgstr "Distancia del sujeto"

#~ msgid "center-weight"
#~ msgstr "peso centrado"

#~ msgid "spot"
#~ msgstr "lugar"

#~ msgid "multi-spot"
#~ msgstr "multi-lugar"

#~ msgid "matrix"
#~ msgstr "matriz"

#~ msgid "partial"
#~ msgstr "parcial"

#~ msgid "other"
#~ msgstr "otro"

#~ msgid "sunny"
#~ msgstr "soleado"

#~ msgid "fluorescent"
#~ msgstr "fluorescente"

#~ msgid "tungsten"
#~ msgstr "tungsteno"

#~ msgid "manual"
#~ msgstr "manual"

#~ msgid "landscape"
#~ msgstr "paisaje"

#~ msgid "yes"
#~ msgstr "sí"

#, fuzzy
#~ msgid "Unknown 2"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 3"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 4"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 5"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 6"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 8"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 9"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 14"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 15"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 16"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 17"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 18"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 19"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 21"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 24"
#~ msgstr "Desconocido"

#, fuzzy
#~ msgid "Unknown 25"
#~ msgstr "Desconocido"
