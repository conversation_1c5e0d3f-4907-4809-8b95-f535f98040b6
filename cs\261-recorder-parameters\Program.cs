﻿// Show Recorder parameters

using System;
using System.IO;
using Euresys.EGrabberRecorder;

namespace _261_recorder_parameters
{
    internal class Program
    {
        static void ShowRecorderParameter(Recorder recorder)
        {
            Console.WriteLine("Recorder parameters");
            Console.WriteLine("-------------------");
            Console.WriteLine(" - RECORDER_PARAMETER_VERSION: " + recorder.Get<string>(RECORDER_PARAMETER.VERSION));
            Console.WriteLine(" - RECORDER_PARAMETER_CONTAINER_SIZE: " + recorder.Get<long>(RECORDER_PARAMETER.CONTAINER_SIZE));
            Console.WriteLine(" - RECORDER_PARAMETER_RECORD_INDEX: " + recorder.Get<long>(RECORDER_PARAMETER.RECORD_INDEX));
            Console.WriteLine(" - RECORDER_PARAMETER_RECORD_COUNT: " + recorder.Get<long>(RECORDER_PARAMETER.RECORD_COUNT));
            Console.WriteLine(" - RECORDER_PARAMETER_REMAINING_SPACE_ON_DEVICE: " + recorder.Get<long>(RECORDER_PARAMETER.REMAINING_SPACE_ON_DEVICE));
            Console.WriteLine(" - RECORDER_PARAMETER_BUFFER_OPTIMAL_ALIGNMENT: " + recorder.Get<long>(RECORDER_PARAMETER.BUFFER_OPTIMAL_ALIGNMENT));
            Console.WriteLine(" - RECORDER_PARAMETER_DATABASE_VERSION: " + recorder.Get<long>(RECORDER_PARAMETER.DATABASE_VERSION));
            Console.WriteLine(" - RECORDER_PARAMETER_REMAINING_SPACE_IN_CONTAINER: " + recorder.Get<long>(RECORDER_PARAMETER.REMAINING_SPACE_IN_CONTAINER));
        }

        static void Main(string[] args)
        {
            using (var recorderLib = new RecorderLibrary()) // load Recorder library
            {
                // get an existing directory where the recorder container will be stored
                var containerPath = Path.Combine(Environment.CurrentDirectory, "output");
                Directory.CreateDirectory(containerPath);

                Console.WriteLine("Create a recorder for writing with automatic trim on close");
                using (var recorder = recorderLib.OpenRecorder(containerPath, RECORDER_OPEN_MODE.WRITE, RECORDER_CLOSE_MODE.TRIM))
                {
                    Console.WriteLine("Allocate recorder container space for 1000000 bytes");
                    recorder.Set(RECORDER_PARAMETER.CONTAINER_SIZE, 1000000);
                    // Note: the recorder size will be rounded up to meet the alignment constraint;
                    // the recorder size will be a multiple of RECORDER_PARAMETER.BUFFER_OPTIMAL_ALIGNMENT

                    Console.WriteLine("Write a 1000-byte buffer to the container");
                    // write a dummy buffer to show the impact on the recorder parameters
                    var data = new byte[1000];
                    var info = new RecorderBufferInfo
                    {
                        size = (ulong)data.Length,
                        pitch = 100,
                        width = 100,
                        height = 10,
                        pixelFormat = (uint)Euresys.PixelFormat.PFNC.Mono8,
                        partCount = 1
                    };
                    recorder.Write(info, data);

                    // query recorder parameters
                    ShowRecorderParameter(recorder);

                    // Note: the recorder is automatically closed when going out of scope;
                    // the RECORDER_CLOSE_MODE.TRIM will reduce the container size to the
                    // smallest size that fits the container contents (i.e. the buffer we
                    // have just written to the container)
                    Console.WriteLine("Trim & close the recorder container");
                }

                Console.WriteLine("Reopen the recorder for reading");
                using (var recorder = recorderLib.OpenRecorder(containerPath, RECORDER_OPEN_MODE.READ))
                {
                    // query recorder parameters
                    ShowRecorderParameter(recorder);

                    Console.WriteLine("Close the recorder container");
                }
            }
        }
    }
}
