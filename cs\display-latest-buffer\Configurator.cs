﻿using System;
using System.Collections.Generic;
using System.Text;

using Euresys.Managed;

namespace displayLatestBuffer
{
  public class Configurator
  {
    static Int64 fpsToMicroseconds(Int64 fps)
    {
      if (fps == 0)
      {
        return 0;
      }
      else
      {
        return (1000000 + fps - 1) / fps;
      }
    }
    public static void Configure(Euresys.Managed.CoaxlinkGrabber grabber)
    {
      grabber.SetString<DevicePort>("CameraControlMethod", "RG");
      grabber.SetInteger<DevicePort>("ExposureReadoutOverlap", 1);
      grabber.SetInteger<DevicePort>("CycleMinimumPeriod", fpsToMicroseconds(70));
      grabber.SetString<DevicePort>("CycleTriggerSource", "Immediate");    // Equivalent to MC_(Next)TrigMode_IMMEDIATE
      grabber.SetInteger<DevicePort>("ExposureTime", 4000);
      grabber.SetInteger<DevicePort>("StrobeDuration", 0);
      grabber.SetInteger<DevicePort>("StrobeDelay", 100);             // 100 us

      grabber.SetString<RemotePort>("ExposureMode", "TriggerWidth");
      grabber.SetString<RemotePort>("TriggerSource", "Trigger");
      grabber.SetString<RemotePort>("TriggerActivation", "RisingEdge");
      grabber.SetString<RemotePort>("PixelFormat", "Mono8");
      grabber.SetString<RemotePort>("LinkConfig", "Link1Speed6250");
      grabber.SetInteger<RemotePort>("Width", 2048);
      grabber.SetInteger<RemotePort>("Height", 2048);

    }

  }
}
