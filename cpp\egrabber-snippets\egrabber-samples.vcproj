﻿<?xml version="1.0" encoding="UTF-8"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="egrabber-samples"
	ProjectGUID="{89CA726F-9ECD-4475-910A-F2B9E69B1A3F}"
	RootNamespace="egrabber-samples"
	Keyword="Win32Proj"
	>
	<Platforms>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;C:\Program Files\Euresys\eGrabber\include&quot;;&quot;C:\Program Files (x86)\Euresys\eGrabber\include&quot;"
				PreprocessorDefinitions="_CRT_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_SCL_SECURE_NO_DEPRECATE"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="1"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="&quot;C:\Program Files\Euresys\eGrabber\include&quot;;&quot;C:\Program Files (x86)\Euresys\eGrabber\include&quot;"
				PreprocessorDefinitions="_CRT_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;_SCL_SECURE_NO_WARNINGS;_SCL_SECURE_NO_DEPRECATE"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="0"
				GenerateDebugInformation="true"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="17"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Tools"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\tools\main.cpp"
				>
			</File>
			<File
				RelativePath=".\tools\tools.cpp"
				>
			</File>
			<File
				RelativePath=".\tools\tools.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Samples"
			>
			<File
				RelativePath=".\samples\100-grabn.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\101-singleframe.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\101-singleframe.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\102-action-grab.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\102-action-grab.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\105-area-scan-grabn.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\106-line-scan-grabn.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\110-get-string-list.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\120-converter.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\130-using-buffer.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\140-genapi-command.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\150-discover.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\200-grabn-callbacks.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\200-grabn-callbacks.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\200-grabn-callbacks.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\201-grabn-pop-oneof.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\201-grabn-pop-oneof.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\201-grabn-pop-oneof.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\210-show-all-grabbers.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\210-show-all-grabbers.show.js"
				>
			</File>
			<File
				RelativePath=".\samples\211-show-all-grabbers-ro.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\211-show-all-grabbers-ro.show.js"
				>
			</File>
			<File
				RelativePath=".\samples\212-create-all-grabbers.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\212-create-all-grabbers.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\213-egrabbers.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\213-egrabbers.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\220-get-announced-handles.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\221-queue-buffer-ranges.bufferSet1.js"
				>
			</File>
			<File
				RelativePath=".\samples\221-queue-buffer-ranges.bufferSet2.js"
				>
			</File>
			<File
				RelativePath=".\samples\221-queue-buffer-ranges.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\230-script-vars.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\231-script-var.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\231-script-var.greetingsFromCpp.js"
				>
			</File>
			<File
				RelativePath=".\samples\231-script-var.numbersFromScript.js"
				>
			</File>
			<File
				RelativePath=".\samples\240-user-memory.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\241-multi-part.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\250-using-lut.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\250-using-lut.userLUT.js"
				>
			</File>
			<File
				RelativePath=".\samples\260-recorder-read-write.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\261-recorder-parameters.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\270-multicast-master.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\270-multicast-master.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\271-multicast-receiver.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\271-multicast-receiver.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\300-events-mt-cic.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\300-events-mt-cic.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\300-events-mt-cic.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\301-events-st-all.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\301-events-st-all.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\301-events-st-all.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\302-cxp-connector-detection.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\302-cxp-connector-detection.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\302-cxp-connector-detection.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\310-high-frame-rate.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\311-high-frame-rate.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\312-part-timestamps.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\320-cl-serial-cli.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\321-gencp-serial.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\330-metadata-insertion.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\330-metadata-insertion.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\330-metadata-insertion.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\340-dma-roi.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\341-dma-deinterlace.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\342-dma-roi-deinterlace.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\600-thread-start-stop-callbacks.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\600-thread-start-stop-callbacks.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\600-thread-start-stop-callbacks.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\610-line-scan-array.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\610-line-scan-array.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\610-line-scan-array.teardown.js"
				>
			</File>
			<File
				RelativePath=".\samples\620-multiple-camera.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\650-multistream.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\650-multistream.setup.js"
				>
			</File>
			<File
				RelativePath=".\samples\700-memento.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\800-process-latest-buffer.cpp"
				>
			</File>
			<File
				RelativePath=".\samples\config-rg.js"
				>
			</File>
			<File
				RelativePath=".\samples\config-sc.js"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
