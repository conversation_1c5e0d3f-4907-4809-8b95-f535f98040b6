#include <EGrabber.h>
#include <FormatConverter.h>
#include <string.h>

#include "../tools/tools.h"
#include "../exif/exif.h"
#include "../jfif/jfif.h"
#include <internal/tools/EuresysLocks.h>

/*
 * Dependency: libexif
 * Ubuntu/Debian: sudo apt install libexif-dev
 * macOS: brew install libexif
 */

using namespace Euresys;

namespace {

static const int previewStreamIndex = 0;
static const int jpegStreamIndex = 1;

class ParentGrabber {
    public:
        ParentGrabber() {};
        virtual ~ParentGrabber() {};

        virtual void onNewBuffer() = 0;
        Euresys::Internal::ConcurrencyLock mutex;
};

class PreviewGrabber : public EGrabber<CallbackSingleThread> {
    public:
        PreviewGrabber(ParentGrabber *parent, EGenTL &gentl, FormatConverter &converter, int interfaceIndex, int deviceIndex)
        : EGrabber<CallbackSingleThread>(gentl, interfaceIndex, deviceIndex, previewStreamIndex)
        , parent(parent)
        , converter(converter)
        , frameId(-1)
        , buffer(0)
        {
            runScript(Tools::getScriptPath("setup"));
            reallocBuffers(5);
        }

        ~PreviewGrabber() {
            if (buffer) {
                buffer->push(*this);
                delete buffer;
            }
        }

    private:
        ParentGrabber *parent;

    public:
        FormatConverter &converter;
        uint64_t frameId;
        Buffer *buffer;

        virtual void onNewBufferEvent(const NewBufferData& data) {
            Euresys::Internal::AutoLock lock(parent->mutex);
            if (buffer) {
                buffer->push(*this);
                delete buffer;
            }
            buffer = new Buffer(data);
            frameId = buffer->getInfo<uint64_t>(*this, gc::BUFFER_INFO_FRAMEID);
            parent->onNewBuffer();
        }
};

class JpegGrabber: public EGrabber<CallbackSingleThread> {
    public:
        JpegGrabber(ParentGrabber *parent, EGenTL &gentl, int interfaceIndex, int deviceIndex)
        : EGrabber<CallbackSingleThread>(gentl, interfaceIndex, deviceIndex, jpegStreamIndex)
        , parent(parent)
        , interfaceIndex(interfaceIndex)
        , deviceIndex(deviceIndex)
        , outputPath(Tools::getEnv("sample-output-path"))
        , frameCounter(0)
        , deviceVendor("Unknown")
        , deviceModel("Unknown")
        , frameId(-1)
        , buffer(0)
        {
            runScript(Tools::getScriptPath("setup"));
            reallocBuffers(5);
            try {
                deviceVendor = getString<Euresys::DeviceModule>("DeviceVendorName");
            } catch (const gentl_error &) {
            }
            try {
                deviceModel = getString<Euresys::DeviceModule>("DeviceModelName");
            } catch (const gentl_error &) {
            }
        }

        ~JpegGrabber() {
            if (buffer) {
                buffer->push(*this);
                delete buffer;
            }
        }

    private:
        ParentGrabber *parent;
        int interfaceIndex;
        int deviceIndex;
        const std::string outputPath;
        unsigned int frameCounter;

    public:
        std::string deviceVendor;
        std::string deviceModel;
        uint64_t frameId;
        Buffer *buffer;

        std::string getFrameName() {
            std::ostringstream sstr;
            sstr << "frame-" << interfaceIndex << "-" << deviceIndex << "." << frameCounter++ << ".jpg";
            std::string imageName(sstr.str());
            return Tools::join2Path(outputPath, imageName);
        }

        virtual void onNewBufferEvent(const NewBufferData& data) {
            Euresys::Internal::AutoLock lock(parent->mutex);
            if (buffer) {
                buffer->push(*this);
                delete buffer;
            }
            buffer = new Buffer(data);
            frameId = buffer->getInfo<uint64_t>(*this, gc::BUFFER_INFO_FRAMEID);
            parent->onNewBuffer();
        }
};

class ExifGrabber : public ParentGrabber {
    public:
        ExifGrabber(EGenTL &gentl, FormatConverter &converter, int interfaceIndex, int deviceIndex)
        : ParentGrabber()
        , previewGrabber(this, gentl, converter, interfaceIndex, deviceIndex)
        , jpegGrabber(this, gentl, interfaceIndex, deviceIndex)
        {
        }

        ~ExifGrabber() {
            previewGrabber.stop();
            jpegGrabber.stop();
        }

        void start() {
            previewGrabber.start();
            jpegGrabber.start();
        }

        unsigned char *createPreview(PreviewGrabber &previewGrabber, FormatConverter &converter, size_t &size) {
            static const int previewJpegQuality = 70;
            Buffer &previewBuffer(*previewGrabber.buffer);
            uint8_t *image = previewBuffer.getInfo<uint8_t *>(previewGrabber, gc::BUFFER_INFO_BASE);
            uint64_t pixelFormat = previewBuffer.getInfo<uint64_t>(previewGrabber, gc::BUFFER_INFO_PIXELFORMAT);
            size_t width = previewBuffer.getInfo<size_t>(previewGrabber, gc::BUFFER_INFO_WIDTH);
            size_t height = previewBuffer.getInfo<size_t>(previewGrabber, gc::BUFFER_INFO_DELIVERED_IMAGEHEIGHT);
            FormatConverter::Auto jfif(converter,
                                       FormatConverter::OutputFormat("CustomJFIF", ge::IMAGE_CONVERT_OUTPUT_CONFIG_DEFAULT, previewJpegQuality),
                                       image, pixelFormat, width, height);
            size = jfif.getOutputSize();
            uint8_t *data = jfif.getBuffer();
            unsigned char *preview = (unsigned char*)exif_mem_alloc(exifMemory.mem(), static_cast<ExifLong>(size));
            if (!preview) {
                throw std::runtime_error("Could not allocate preview buffer");
            }
            memcpy(preview, data, static_cast<size_t>(size));
            return preview;
        }

        void createExifFile(JpegGrabber &jpegGrabber, PreviewGrabber &previewGrabber) {
            Buffer &jpegBuffer(*jpegGrabber.buffer);
            const std::string fileName(jpegGrabber.getFrameName());
            Tools::log(fileName);
            size_t width = jpegBuffer.getInfo<size_t>(jpegGrabber, gc::BUFFER_INFO_WIDTH);
            size_t height = jpegBuffer.getInfo<size_t>(jpegGrabber, gc::BUFFER_INFO_HEIGHT);
            uint8_t *image = jpegBuffer.getInfo<uint8_t *>(jpegGrabber, gc::BUFFER_INFO_BASE);
            size_t size = jpegBuffer.getInfo<size_t>(jpegGrabber, gc::BUFFER_INFO_DATA_SIZE);
            uint64_t ts = jpegBuffer.getInfo<uint64_t>(jpegGrabber, gc::BUFFER_INFO_TIMESTAMP);
            uint8_t *jpeg = Jfif::skipJFIFHeader(image, size);

            Exif::ExifResources e(exifMemory, jpeg, size);

            size_t previewSize;
            unsigned char *preview = createPreview(previewGrabber, previewGrabber.converter, previewSize);
            e.addThumbnail(preview, previewSize);

            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_IMAGE_WIDTH, static_cast<ExifShort>(width));
            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_IMAGE_LENGTH, static_cast<ExifShort>(height));

            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_PIXEL_X_DIMENSION, static_cast<ExifLong>(width));
            e.addTag(EXIF_IFD_EXIF, EXIF_TAG_PIXEL_Y_DIMENSION, static_cast<ExifLong>(height));

            e.addTag(EXIF_IFD_0, EXIF_TAG_MAKE, jpegGrabber.deviceVendor);
            e.addTag(EXIF_IFD_0, EXIF_TAG_MODEL, jpegGrabber.deviceModel);

            std::string strTs("TIMESTAMP=");
            strTs += Tools::formatTimestamp(ts);
            e.addUserComment(strTs);

            e.writeFile(fileName);
        }

        void onNewBuffer() {
            try {
                // parent->mutex is locked by caller
                if (previewGrabber.buffer && jpegGrabber.buffer &&
                    previewGrabber.frameId == jpegGrabber.frameId) {
                    createExifFile(jpegGrabber, previewGrabber);
                }
            } catch(const std::exception & ex) {
                Tools::log(std::string("onNewBuffer failed: ") + ex.what());
            } catch (...) {
                Tools::log("onNewBuffer failed");
            }
        }

    private:
        PreviewGrabber previewGrabber;
        JpegGrabber jpegGrabber;
        Exif::ExifMemory exifMemory;
};

}

const unsigned int DURATION = 10;

static void sample() {
    EGenTL genTL;
    FormatConverter converter(genTL);
    ExifGrabber e0(genTL, converter, 0, 0);
    ExifGrabber e1(genTL, converter, 0, 1);
    ExifGrabber e2(genTL, converter, 0, 2);
    ExifGrabber e3(genTL, converter, 0, 3);
    e0.start();
    e1.start();
    e2.start();
    e3.start();
    Tools::log("Grabbing for " + Tools::toString(DURATION) + " seconds");
    Tools::sleepMs(1000 * DURATION);
}

static Tools::Sample addSample(__FILE__, "Acquire data from 4 Preview and 4 JPEG encoded data streams and produce EXIF files with thumbnails", sample);
