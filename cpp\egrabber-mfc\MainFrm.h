
// MainFrm.h : interface of the CMainFrame class
//

#pragma once

class CMainFrame : public CFrameWnd
{
    protected: // create from serialization only
        CMainFrame();
        DECLARE_DYNCREATE(CMainFrame)

    // Attributes
    public:

    // Operations
    public:

    // Overrides
    public:
        virtual BOOL PreCreateWindow(CREATESTRUCT& cs);

    // Implementation
    public:
        virtual ~CMainFrame();
    #ifdef _DEBUG
        virtual void AssertValid() const;
        virtual void Dump(CDumpContext& dc) const;
    #endif


    // Generated message map functions
    protected:
        DECLARE_MESSAGE_MAP()
};


