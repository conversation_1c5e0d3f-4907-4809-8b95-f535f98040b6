PSP
======
SDL2 port for the Sony PSP contributed by:
- <PERSON><PERSON>
- Wout<PERSON> <PERSON><PERSON><PERSON><PERSON> to
   <PERSON>,<PERSON>,<PERSON> for the original SDL 1.2 for PS<PERSON> for his PSP GU lib "Glib2d"

## Building
To build SDL2 library for the PSP, make sure you have the latest PSPDev status and run:
```bash
cmake -S. -Bbuild -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=$PSPDEV/psp/share/pspdev.cmake
cmake --build build
cmake --install build
```


## Getting PSP Dev
[Installing PSP Dev](https://github.com/pspdev/pspdev)

## Running on PPSSPP Emulator
[PPSSPP](https://github.com/hrydgard/ppsspp)

[Build Instructions](https://github.com/hrydgard/ppsspp/wiki/Build-instructions)


## Compiling a HelloWorld
[PSP Hello World](https://psp-dev.org/doku.php?id=tutorial:hello_world)

## To Do
- PSP Screen Keyboard
- Dialogs
