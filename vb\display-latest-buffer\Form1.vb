﻿Public Class Form1
    Shared NB_BUFFERS As Integer = 50

    Dim genTL As Euresys.EGrabber.EGenTL = Nothing
    Dim discovery As Euresys.EGrabber.EGrabberDiscovery = Nothing
    Dim grabber As Euresys.EGrabber.EGrabber = Nothing
    Dim bitmap As Bitmap = Nothing

    Dim imgWidth As UInt64
    Dim imgHeight As UInt64
    Dim imgFormat As String

    Dim refreshThreadRunning As Boolean
    Dim currentBuffer As Euresys.EGrabber.Buffer = Nothing

    Dim thread As System.Threading.Thread

    Private Sub ExitToolStripMenuItem_Click(sender As Object, e As EventArgs) Handles ExitToolStripMenuItem.Click
        Close()
    End Sub

    Private Sub Form1_FormClosing(ByVal sender As Object, ByVal e As FormClosingEventArgs) Handles MyBase.FormClosing
        Timer1.Stop()
        refreshThreadRunning = False

        If Not (grabber Is Nothing) Then
            Try
                grabber.Stop()
            Catch exc As System.Exception
                MessageBox.Show(exc.Message)
            End Try
            grabber.CancelPop()
            While thread.IsAlive
                System.Threading.Thread.Sleep(100)
            End While
            grabber.Dispose()
            grabber = Nothing
        End If

        If Not (discovery Is Nothing) Then
            discovery.Dispose()
            discovery = Nothing
        End If

        If Not (genTL Is Nothing) Then
            genTL.Dispose()
            genTL = Nothing
        End If
    End Sub

    Private Sub Form1_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        Try
            genTL = New Euresys.EGrabber.EGenTL()
            discovery = New Euresys.EGrabber.EGrabberDiscovery(genTL)
            discovery.Discover()
            If discovery.CameraCount = 0 Then
                Throw New Exception("No cameras discovered")
            End If
            grabber = New Euresys.EGrabber.EGrabber(discovery.GetCamera(0))
            grabber.ReallocBuffers(NB_BUFFERS)
            imgWidth = grabber.Width
            imgHeight = grabber.Height
            imgFormat = grabber.PixelFormat

            bitmap = New Bitmap(imgWidth, imgHeight, PixelFormat.Format24bppRgb)
            PictureBox1.Image = bitmap

            grabber.Start()
            refreshThreadRunning = True
            thread = New System.Threading.Thread(AddressOf Me.RefreshThread)
            thread.Start()

            Timer1.Start()
        Catch exc As System.Exception
            MessageBox.Show(exc.Message)
            Close()
        End Try
    End Sub

    Private Sub RefreshThread()
        Dim buffer As Euresys.EGrabber.Buffer

        Try
            While refreshThreadRunning And Not (grabber Is Nothing)
                buffer = New Euresys.EGrabber.Buffer(grabber)
                If Not refreshThreadRunning Then
                    Exit While
                End If
                If currentBuffer Is Nothing Then
                    currentBuffer = buffer
                Else
                    If Not (grabber Is Nothing) Then
                        buffer.Push()
                    End If
                End If
            End While
        Catch exc As Euresys.EGrabber.GenTLError
            If exc.GcError <> Euresys.EGrabber.GC_ERROR.GC_ERR_ABORT Then
                MessageBox.Show(exc.Message)
            End If
        Catch exc As System.Exception
            MessageBox.Show(exc.Message)
        End Try
    End Sub

    Private Sub Timer1_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles Timer1.Tick
        Dim bufferTmp As Euresys.EGrabber.Buffer
        Dim bmpData As BitmapData

        If (currentBuffer Is Nothing Or grabber Is Nothing) Then
            Return
        End If

        bmpData = Nothing
        Try
            bmpData = bitmap.LockBits(New Rectangle(0, 0, imgWidth, imgHeight), ImageLockMode.WriteOnly, PixelFormat.Format24bppRgb)
            currentBuffer.ConvertTo("BGR8", bmpData.Scan0, Math.Abs(bmpData.Stride) * bmpData.Height)
        Finally
            If Not (bmpData Is Nothing) Then
                bitmap.UnlockBits(bmpData)
            End If
        End Try
        PictureBox1.Refresh()

        bufferTmp = currentBuffer
        currentBuffer = Nothing
        If Not (grabber Is Nothing) Then
            bufferTmp.Push()
        End If
    End Sub
End Class
