﻿Module Module1
    Sub Sample()
        Dim FRAME_COUNT As System.UInt32
        Dim width As System.UInt64
        Dim height As System.UInt64
        Dim dataSize As System.UInt64
        Dim timestamp As System.UInt64

        FRAME_COUNT = 20

        Using genTL As New Euresys.EGrabber.EGenTL() ' use Euresys.EGrabber.EGenTL(Euresys.EGrabber.CtiPath.Gigelink) for Gigelink
                                                     '  or Euresys.EGrabber.EGenTL(Euresys.EGrabber.CtiPath.Grablink) for the Grablink Duo
            Using discovery As New Euresys.EGrabber.EGrabberDiscovery(genTL)
                discovery.Discover()
                If discovery.CameraCount = 0 Then
                    Throw New Exception("No cameras discovered")
                End If
                Using grabber As New Euresys.EGrabber.EGrabber(discovery.GetCamera(0))
                    grabber.ReallocBuffers(FRAME_COUNT)

                    grabber.Start(FRAME_COUNT)

                    For frame As UInteger = 0 To FRAME_COUNT - 1
                        Using buffer As New Euresys.EGrabber.ScopedBuffer(grabber)
                            width = buffer.GetInfo(Of System.UInt64)(Euresys.EGrabber.BUFFER_INFO_CMD.BUFFER_INFO_WIDTH)
                            height = buffer.GetInfo(Of System.UInt64)(Euresys.EGrabber.BUFFER_INFO_CMD.BUFFER_INFO_HEIGHT)
                            dataSize = buffer.GetInfo(Of System.UInt64)(Euresys.EGrabber.BUFFER_INFO_CMD.BUFFER_INFO_DATA_SIZE)
                            timestamp = buffer.GetInfo(Of System.UInt64)(Euresys.EGrabber.BUFFER_INFO_CMD.BUFFER_INFO_TIMESTAMP)

                            System.Console.WriteLine("Acquisition Index: {0}", frame)
                            System.Console.WriteLine(" - Width: {0}", width)
                            System.Console.WriteLine(" - Height: {0}", height)
                            System.Console.WriteLine(" - Data Size: {0}", dataSize)
                            System.Console.WriteLine(" - Timestamp: {0}.{1}", timestamp / 1000000, timestamp Mod 1000000)

                            Using conv As Euresys.EGrabber.ConvertedBuffer = buffer.Convert("BGR8")
                                conv.SaveToDisk("frame.NNN.jpeg", frame)
                            End Using
                        End Using
                    Next
                End Using
            End Using
        End Using
    End Sub


    Sub Main()
        Try
            Sample()
        Catch e As System.Exception
            System.Console.WriteLine(e.Message)
            System.Console.WriteLine(e.StackTrace)
            System.Console.WriteLine(e.Source)
        End Try
    End Sub

End Module
