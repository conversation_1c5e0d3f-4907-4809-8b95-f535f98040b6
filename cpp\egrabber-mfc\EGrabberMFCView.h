
// EGrabberMFCView.h : interface of the CEGrabberMFCView class
//

#pragma once

#include "MainFrm.h"

class CEGrabberMFCView : public CView
{
    protected: // create from serialization only
        CEGrabberMFCView();
        DECLARE_DYNCREATE(CEGrabberMFCView)

    // Attributes
    public:
        CEGrabberMFCDoc* GetDocument() const;

    // Overrides
    public:
        virtual void OnDraw(CDC* pDC);  // overridden to draw this view
        virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
    protected:
        // Implementation
        virtual void SyncDraw(CDC* pDC);
    public:
        afx_msg LRESULT updateImage(WPARAM, LPARAM);
        virtual ~CEGrabberMFCView();
    #ifdef _DEBUG
        virtual void AssertValid() const;
        virtual void Dump(CDumpContext& dc) const;
    #endif

    protected:
    // Generated message map functions
    protected:
        DECLARE_MESSAGE_MAP()
};

#ifndef _DEBUG  // debug version in EGrabberMFCView.cpp
inline CEGrabberMFCDoc* CEGrabberMFCView::GetDocument() const
   { return reinterpret_cast<CEGrabberMFCDoc*>(m_pDocument); }
#endif
