# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

########################################################################
# Conditional rules, depending on tool availability
########################################################################

VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_1 = $(HTML_APIDOC_DIR).uploaded
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_2 = $(HTML_APIDOC_DIR).stamp
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_3 = $(HTML_APIDOC_DIR).tar.gz
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_4 = $(HTML_APIDOC_INTERNALS_DIR).uploaded
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_5 = $(HTML_APIDOC_INTERNALS_DIR).stamp
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_6 = $(HTML_APIDOC_INTERNALS_DIR).tar.gz
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_7 = install-apidocs-internals
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_8 = uninstall-apidocs-internals
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_9 = install-apidocs
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_10 = uninstall-apidocs
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@am__append_11 = clean-apidocs
subdir = doc
DIST_COMMON = $(srcdir)/Doxyfile-internals.in $(srcdir)/Doxyfile.in \
	$(srcdir)/Makefile.am $(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4m/gp-check-shell-environment.m4 \
	$(top_srcdir)/m4m/gp-config-msg.m4 \
	$(top_srcdir)/m4m/gp-documentation.m4 \
	$(top_srcdir)/m4m/gp-gettext-hack.m4 \
	$(top_srcdir)/m4m/stdint.m4 $(top_srcdir)/auto-m4/gettext.m4 \
	$(top_srcdir)/auto-m4/iconv.m4 $(top_srcdir)/auto-m4/lib-ld.m4 \
	$(top_srcdir)/auto-m4/lib-link.m4 \
	$(top_srcdir)/auto-m4/lib-prefix.m4 \
	$(top_srcdir)/auto-m4/libtool.m4 \
	$(top_srcdir)/auto-m4/ltoptions.m4 \
	$(top_srcdir)/auto-m4/ltsugar.m4 \
	$(top_srcdir)/auto-m4/ltversion.m4 \
	$(top_srcdir)/auto-m4/lt~obsolete.m4 \
	$(top_srcdir)/auto-m4/nls.m4 $(top_srcdir)/auto-m4/po.m4 \
	$(top_srcdir)/auto-m4/progtest.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(SHELL) $(top_srcdir)/mkinstalldirs
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES = Doxyfile Doxyfile-internals
CONFIG_CLEAN_VPATH_FILES =
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN   " $@;
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
SOURCES =
DIST_SOURCES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__installdirs = "$(DESTDIR)$(docdir)"
DATA = $(doc_DATA)
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_CPPFLAGS = @AM_CPPFLAGS@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AM_LDFLAGS = @AM_LDFLAGS@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DOT = @DOT@
DOXYGEN = @DOXYGEN@
DOXYGEN_OUTPUT_DIR = @DOXYGEN_OUTPUT_DIR@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GETTEXT_PACKAGE = @GETTEXT_PACKAGE@
GMSGFMT = @GMSGFMT@
GREP = @GREP@
HAVE_DOT = @HAVE_DOT@
HTML_APIDOC_DIR = @HTML_APIDOC_DIR@
HTML_APIDOC_INTERNALS_DIR = @HTML_APIDOC_INTERNALS_DIR@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
INTLLIBS = @INTLLIBS@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBEXIF_AGE = @LIBEXIF_AGE@
LIBEXIF_CURRENT = @LIBEXIF_CURRENT@
LIBEXIF_CURRENT_MIN = @LIBEXIF_CURRENT_MIN@
LIBEXIF_REVISION = @LIBEXIF_REVISION@
LIBEXIF_VERSION_INFO = @LIBEXIF_VERSION_INFO@
LIBICONV = @LIBICONV@
LIBINTL = @LIBINTL@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBICONV = @LTLIBICONV@
LTLIBINTL = @LTLIBINTL@
LTLIBOBJS = @LTLIBOBJS@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MKINSTALLDIRS = @MKINSTALLDIRS@
MSGFMT = @MSGFMT@
MSGMERGE = @MSGMERGE@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POSUB = @POSUB@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
USE_NLS = @USE_NLS@
VERSION = @VERSION@
XGETTEXT = @XGETTEXT@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
DOXYGEN_FILES = 
DOXYGEN_STAMPS = $(am__append_2) $(am__append_5)
DOXYGEN_UPLOAD = $(am__append_1) $(am__append_4)
DOXYGEN_INSTALL_TARGETS = $(am__append_7) $(am__append_9)
DOXYGEN_UNINSTALL_TARGETS = $(am__append_8) $(am__append_10)
DOXYGEN_CLEAN_TARGETS = $(am__append_11)
DOXYGEN_TARBALLS = $(am__append_3) $(am__append_6)

########################################################################
# Common part
########################################################################
EXTRA_DIST = Doxyfile.in Doxyfile-internals.in footer.html README.apidocs $(HTML_APIDOC_DIR).tar.gz
doc_DATA = $(DOXYGEN_FILES)
CLEANFILES = $(DOXYGEN_FILES) $(DOXYGEN_STAMPS)
all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu doc/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu doc/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
Doxyfile: $(top_builddir)/config.status $(srcdir)/Doxyfile.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@
Doxyfile-internals: $(top_builddir)/config.status $(srcdir)/Doxyfile-internals.in
	cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-docDATA: $(doc_DATA)
	@$(NORMAL_INSTALL)
	test -z "$(docdir)" || $(MKDIR_P) "$(DESTDIR)$(docdir)"
	@list='$(doc_DATA)'; test -n "$(docdir)" || list=; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(docdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(docdir)" || exit $$?; \
	done

uninstall-docDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(doc_DATA)'; test -n "$(docdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	test -n "$$files" || exit 0; \
	echo " ( cd '$(DESTDIR)$(docdir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(docdir)" && rm -f $$files
tags: TAGS
TAGS:

ctags: CTAGS
CTAGS:


distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(DATA) all-local
installdirs:
	for dir in "$(DESTDIR)$(docdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-local mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-data-local install-docDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-docDATA uninstall-local

.MAKE: install-am install-strip

.PHONY: all all-am all-local check check-am clean clean-generic \
	clean-libtool clean-local distclean distclean-generic \
	distclean-libtool distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am \
	install-data-local install-docDATA install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am uninstall uninstall-am \
	uninstall-docDATA uninstall-local


# FIXME: Depending on the source files would be sufficient, but only
#        maintainable with a completely non-recursive build.
#        Depending on libexif.la works at least.
$(top_builddir)/libexif/libexif.la:
	cd $(top_builddir)/libexif && $(MAKE) libexif.la
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@$(HTML_APIDOC_DIR).stamp: Doxyfile $(top_builddir)/libexif/libexif.la
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	$(DOXYGEN) $<
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	echo > $@
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@$(HTML_APIDOC_DIR).tar.gz: $(HTML_APIDOC_DIR).stamp
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	(cd $(DOXYGEN_OUTPUT_DIR) && $(AMTAR) chof - $(HTML_APIDOC_DIR) | GZIP=--best gzip -c) > $@
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@$(HTML_APIDOC_INTERNALS_DIR).stamp: Doxyfile-internals $(top_builddir)/libexif/libexif.la
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	$(DOXYGEN) $<
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	echo > $@
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@$(HTML_APIDOC_INTERNALS_DIR).tar.gz: $(HTML_APIDOC_INTERNALS_DIR).stamp
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	(cd $(DOXYGEN_OUTPUT_DIR) && $(AMTAR) chof - $(HTML_APIDOC_INTERNALS_DIR) | GZIP=--best gzip -c) > $@
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@install-apidocs-internals: $(HTML_APIDOC_INTERNALS_DIR).stamp
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	mkdir -p "$(DESTDIR)$(docdir)"
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	find "$(DOXYGEN_OUTPUT_DIR)/$(HTML_APIDOC_INTERNALS_DIR)" \
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		\( -type d -exec chmod 0755 {} \; \) -or \
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		\( -type f -exec chmod 0644 {} \; \)
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	(unset CDPATH; cd "$(DOXYGEN_OUTPUT_DIR)" && tar cpf - "$(HTML_APIDOC_INTERNALS_DIR)") \
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		| (cd "$(DESTDIR)$(docdir)" && tar xpf -)
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@uninstall-apidocs-internals:
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rm -rf "$(DESTDIR)$(docdir)/$(HTML_APIDOC_INTERNALS_DIR)"
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@install-apidocs: $(HTML_APIDOC_DIR).stamp
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	mkdir -p "$(DESTDIR)$(docdir)"
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	find "$(DOXYGEN_OUTPUT_DIR)/$(HTML_APIDOC_DIR)" \
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		\( -type d -exec chmod 0755 {} \; \) -or \
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		\( -type f -exec chmod 0644 {} \; \)
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	(unset CDPATH; cd "$(DOXYGEN_OUTPUT_DIR)" && tar cpf - "$(HTML_APIDOC_DIR)") \
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		| (cd "$(DESTDIR)$(docdir)" && tar xpf -)
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@uninstall-apidocs:
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rm -rf "$(DESTDIR)$(docdir)/$(HTML_APIDOC_DIR)"
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@clean-apidocs:
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rm -rf $(DOXYGEN_OUTPUT_DIR)
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rm -f $(HTML_APIDOC_DIR).tar.gz $(HTML_APIDOC_INTERNALS_DIR).tar.gz

@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@$(DOXYGEN_UPLOAD): $(DOXYGEN_STAMPS)
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	find $(DOXYGEN_OUTPUT_DIR) \( -type d -exec chmod a+rx,go-w,g+s {} \; \) \
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@		-or \( -type f -exec chmod a+r {} \; \)
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rsync -avz --progress $(DOXYGEN_OUTPUT_DIR)/$(HTML_APIDOC_DIR)/ sf:/home/<USER>/h/hu/hun/libexif/htdocs/api/
@BUILD_DOCS_TRUE@@ENABLE_INTERNAL_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	rsync -avz --progress $(DOXYGEN_OUTPUT_DIR)/$(HTML_APIDOC_INTERNALS_DIR)/ sf:/home/<USER>/h/hu/hun/libexif/htdocs/internals/
@BUILD_DOCS_TRUE@@HAVE_DOXYGEN_TRUE@	ssh "sf" "find /home/<USER>/l/li/libexif/htdocs/internals /home/<USER>/l/li/libexif/htdocs/api \( -type d -exec chgrp libexif {} \; -exec chmod g+rwxs,a+rx {} \; \) -or \( -type f -exec chgrp libexif {} \; -exec chmod g+rw {} \; \( -name '*.html' -or -name '*.png' -or -name '*.php' -or -name '*.jpg' -or -name '*.css' -name '*.dot' -name '*.map' \) -exec chmod a+r {} \; \)"

all-local: $(DOXYGEN_STAMPS)

install-data-local: $(DOXYGEN_INSTALL_TARGETS)

uninstall-local: $(DOXYGEN_UNINSTALL_TARGETS)

clean-local: $(DOXYGEN_CLEAN_TARGETS)

upload: $(DOXYGEN_UPLOAD)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
