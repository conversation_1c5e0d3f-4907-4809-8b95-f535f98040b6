git
=========

The latest development version of SDL is available via git.
Git allows you to get up-to-the-minute fixes and enhancements;
as a developer works on a source tree, you can use "git" to mirror that
source tree instead of waiting for an official release. Please look
at the Git website ( https://git-scm.com/ ) for more
information on using git, where you can also download software for
macOS, Windows, and Unix systems.

    git clone https://github.com/libsdl-org/SDL

If you are building SDL via configure, you will need to run autogen.sh
before running configure.

There is a web interface to the Git repository at:
	http://github.com/libsdl-org/SDL/

