﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E64BD5DF-7374-4A73-862D-547EB4F66E7C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_CRT_SECURE_NO_WARNINGS;_DEBUG;_WINDOWS;_USRDLL;LIBEXIF_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)\include;$(ProjectDir)\..\..\third-party\libexif;$(ProjectDir)\..\..\third-party\libexif\libexif;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <ModuleDefinitionFile>$(ProjectDir)\libexif.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_CRT_SECURE_NO_WARNINGS;_DEBUG;_WINDOWS;_USRDLL;LIBEXIF_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)\include;$(ProjectDir)\..\..\third-party\libexif;$(ProjectDir)\..\..\third-party\libexif\libexif;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <ModuleDefinitionFile>$(ProjectDir)\libexif.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_CRT_SECURE_NO_WARNINGS;NDEBUG;_WINDOWS;_USRDLL;LIBEXIF_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)\include;$(ProjectDir)\..\..\third-party\libexif;$(ProjectDir)\..\..\third-party\libexif\libexif;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>$(ProjectDir)\libexif.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_CRT_SECURE_NO_WARNINGS;NDEBUG;_WINDOWS;_USRDLL;LIBEXIF_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)\include;$(ProjectDir)\..\..\third-party\libexif;$(ProjectDir)\..\..\third-party\libexif\libexif;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>$(ProjectDir)\libexif.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\exif-mnote-data-canon.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\mnote-canon-entry.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\canon\mnote-canon-tag.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-byte-order.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-content.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-data.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-entry.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-format.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-ifd.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-loader.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-log.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-mem.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-mnote-data.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-tag.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\exif-utils.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\exif-mnote-data-fuji.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-entry.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-tag.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\exif-mnote-data-olympus.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-entry.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-tag.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\exif-mnote-data-pentax.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-entry.c" />
    <ClCompile Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-tag.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\exif-mnote-data-canon.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\mnote-canon-entry.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\canon\mnote-canon-tag.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-byte-order.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-content.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-data-type.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-data.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-entry.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-format.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-ifd.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-loader.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-log.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mem.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mnote-data-priv.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-mnote-data.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-system.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-tag.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif-utils.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\exif.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\exif-mnote-data-fuji.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-entry.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\fuji\mnote-fuji-tag.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\i18n.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\exif-mnote-data-olympus.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-entry.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\olympus\mnote-olympus-tag.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\exif-mnote-data-pentax.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-entry.h" />
    <ClInclude Include="..\..\third-party\libexif\libexif\pentax\mnote-pentax-tag.h" />
    <ClInclude Include="config.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>